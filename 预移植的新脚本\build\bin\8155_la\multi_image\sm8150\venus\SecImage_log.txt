OEM signed image with RSAPSS
ERROR: Following validations failed for the image:
       1. Following signing attributes do not match: 
          | Attribute | Image | Config |
          |-----------|-------|--------|
          |  soc_vers | 0x6006|Missing |
          |           | 0x600c|Missing |
          |           | 0x600d|Missing |
          |   debug   |  0x1  |  0x0   |
          
Image /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/video/venus_proc/build/bsp/asic/build/PROD/mbn/reloc/signed/venus.mbn is not encrypted

Base Properties: 
| Integrity Check             | True  |
| Signed                      | True  |
| Encrypted                   | False |
| Size of signature           | 256   |
| Size of one cert            | 2048  |
| Num of certs in cert chain  | 3     |
| Number of root certs        | 1     |
| Maximum number of root certs| None  |
| Cert chain size             | 6144  |

ELF Properties: 
Header: 
| Magic                      | ELF                           |
| Class                      | ELF32                          |
| Data                       | 2's complement, little endian  |
| Version                    | 1 (Current)                    |
| OS/ABI                     | No extensions or unspecified   |
| ABI Version                | 0                              |
| Type                       | EXEC (Executable file)         |
| Machine                    | Advanced RISC Machines ARM     |
| Version                    | 0x1                            |
| Entry address              | 0x0f500000                     |
| Program headers offset     | 0x00000034                     |
| Section headers offset     | 0x00000000                     |
| Flags                      | 0x05000000                     |
| ELF header size            | 52                             |
| Program headers size       | 32                             |
| Number of program headers  | 3                              |
| Section headers size       | 40                             |
| Number of section headers  | 0                              |
| String table section index | 0                              |

Program Headers: 
| Num | Type | Offset | VirtAddr | PhysAddr | FileSize | MemSize | Flags |   Align   |
|-----|------|--------|----------|----------|----------|---------|-------|-----------|
|  1  | LOAD |0x004000| 0x000000 |0xf500000 | 0x103108 | 0x103108|   RE  | 0x100000  |
|  2  | LOAD |0x108000| 0x104000 |0xf604000 | 0x009e44 | 0x3f4000|   RW  | 0x4000    |
|  3  | LOAD |0x117000| 0x4ff000 |0xf9ff000 | 0x000020 | 0x000020|   RW  | 0x4000    |

Hash Segment Properties: 
| Header Size     | 168B  |
| Hash Algorithm  | sha384|

Header: 
| cert_chain_ptr              | 0x0fa000c8  |
| cert_chain_size             | 0x00001800  |
| cert_chain_size_qti         | 0x00000000  |
| code_size                   | 0x000000f0  |
| image_id                    | 0x00000000  |
| image_size                  | 0x000019f0  |
| metadata_major_version      | 0x00000000  |
| metadata_major_version_qti  | 0x00000000  |
| metadata_minor_version      | 0x00000000  |
| metadata_minor_version_qti  | 0x00000000  |
| metadata_size               | 0x00000078  |
| metadata_size_qti           | 0x00000000  |
| sig_ptr                     | 0x0fa000c8  |
| sig_size                    | 0x00000100  |
| sig_size_qti                | 0x00000000  |
| version                     | 0x00000006  |
Metadata:
| anti_rollback_version        | 0x00000000                                   |
| app_id                       | 0x00000000                                   |
| debug                        | 0x00000001                                   |
| hw_id                        | 0x00000000                                   |
| in_use_soc_hw_version        | 0x00000001                                   |
| model_id                     | 0x00000000                                   |
| mrc_index                    | 0x00000000                                   |
| multi_serial_numbers         | 0x00000000                                   |
| oem_id                       | 0x00000000                                   |
| oem_id_independent           | 0x00000000                                   |
| root_revoke_activate_enable  | 0x00000000                                   |
| rot_en                       | 0x00000000                                   |
| soc_vers                     | 0x00006003 0x00006006 0x0000600c 0x0000600d  |
| sw_id                        | 0x0000000e                                   |
| uie_key_switch_enable        | 0x00000000                                   |
| use_serial_number_in_signing | 0x00000000                                   |


