#! /bin/bash
#****************************
# 查询Bash进程脚本
# 查询Bash进程号主脚本
# Author xizhuang.wu
# version 1.0
#**************************** 

LOCAL_QUALCOMM_PATH=/mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/startShell
LOCAL_OLD_QUALCOMM_PATH=/mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155
blockPath=$1
curBashPidid=$2
projectname=$3
doquePath=$4
waitDoqueNum=$5

function handle_pidid_field()  
{  
    if [ ! -f $1 ] || [ $# -ne 4 ];then   
        return 1  
    fi
    filepath=$1	
	blockname=$2  
	fieldname=$3
    handleType=$4
	  
	begin_block=0  
	end_block=0  
  
    aline=1
    while read line  
    do
        if [[ $line =~ $blockname ]];then	
            begin_block=1  
            continue  
        fi
        aline=$((aline+1))		
          
        if [[ $begin_block -eq 1 ]];then  
            end_block=$(echo $line | awk 'BEGIN{ret=0} /^\[.*\]$/{ret=1} END{print ret}')  
            if [ $end_block -eq 1 ];then		
                echo "end block"   
                break  
            fi  
      
            need_ignore=$(echo $line | awk 'BEGIN{ret=0} /^#/{ret=1} /^$/{ret=1} END{print ret}')  
            if [[ $need_ignore -eq 1 ]];then
                continue  
            fi
			
            field=$(echo $line | awk -F= '{gsub(" |\t","",$1); print $1}')  
            value=$(echo $line | awk -F= '{gsub(" |\t","",$2); print $2}')
			
			if [[ $fieldname = $field ]];then
			   if [[ $handleType = "write" ]];then
			      if [[ $fieldname =~ PIDID ]];then
					 sed -i "$((aline))s/.*/${fieldname}=${curBashPidid}/" $filepath
				  else
					 continue
				  fi
			   fi
			   
			   if [[ $handleType = "read" ]];then
			      echo $value
                  break	
			   fi
			fi  
        fi  
    done < $1
	
	if [[ $begin_block -eq 0 ]];then
	    echo "[${blockname}]" >> $LOCAL_QUALCOMM_PATH/PidIDConfig.cfg
	    echo "${fieldname}=${curBashPidid}" >> $LOCAL_QUALCOMM_PATH/PidIDConfig.cfg
	    echo "" >> $LOCAL_QUALCOMM_PATH/PidIDConfig.cfg
	fi
}

function _clean_doqueFile()
{
    echo "Cleaning DoqueFile  !!!!"
    while read -r waitDoqueLine
    do 
	    waitPidid=$(handle_pidid_field $LOCAL_QUALCOMM_PATH/PidIDConfig.cfg $waitDoqueLine "PIDID" "read")
        ps gaux | grep -v grep | awk '{print $2}' | grep "^${waitPidid}$"
		if [[ $? -ne 0 ]];then
		    sed -i "/"$waitDoqueLine"/d" $doquePath/waitDoqueFile
			echo "read waitDoqueFile .....   delete $waitDoqueLine from $doquePath/waitDoqueFile success !!!!!!!!!"
		fi
    done < $doquePath/waitDoqueFile
	   
	while read -r runDoqueLine
    do 
	    runPidid=$(handle_pidid_field $LOCAL_QUALCOMM_PATH/PidIDConfig.cfg $runDoqueLine "PIDID" "read")
        ps gaux | grep -v grep | awk '{print $2}' | grep "^${runPidid}$"
		if [[ $? -ne 0 ]];then
		    sed -i "/"$runDoqueLine"/d" $doquePath/runDoqueFile
			echo "read runDoqueFile .....   delete $runDoqueLine from $doquePath/runDoqueFile success !!!!!!!!!"
		fi
    done < $doquePath/runDoqueFile
}

function _start_inquire_pidid()
{
    echo "Begin inquire $projectname !!!!"
	
	poolPidid=$(handle_pidid_field $LOCAL_QUALCOMM_PATH/PidIDConfig.cfg $blockPath "PIDID" "read")
	ps gaux | grep -v grep | awk '{print $2}' > $LOCAL_QUALCOMM_PATH/AllCurPidIDList
	ps gaux | grep -v grep | awk '{print $2}' | grep "^${poolPidid}$"
	   
	#当前下标编译池未空闲，加入排队队列中
	if [[ $? -eq 0 ]];then
	    tail $doquePath/waitDoqueFile | grep ^${projectname}$
		isInWaitQue=$?
		#当前项目已经处于排队队列中，直接exit 2退出
	    if [[ $isInWaitQue -eq 0 ]];then
		    echo "$projectname is already in waiting doque ,Please waiting !!!! exit 2"
			exit 2
		else
		    #排队数量大于预设值，直接退出邮件通知
		    if [[ `awk 'END{print NR}' $doquePath/waitDoqueFile` -ge $waitDoqueNum ]];then
		       echo "Build Qualcomm8155Pool waitDoque more than  $waitDoqueNum !!!! exit 5"
			   exit 5
			fi
		    
            handle_pidid_field $LOCAL_QUALCOMM_PATH/PidIDConfig.cfg $projectname "PIDID" "write"
		    if [[ $? -eq 0 ]];then
				echo "write $projectname to PidIDConfig.cfg [$projectname] success !!!!!!!!!  project is waiting to build !!!!! "
			else
				echo "write $projectname to PidIDConfig.cfg [$projectname] failed !!!!!!!!!  project is waiting to build !!!!! "
			fi
			echo $projectname >> $doquePath/waitDoqueFile
			echo "write $projectname to $doquePath/waitDoqueFile success !!!!!!!!!"
		    
			echo "$projectname is in waiting doque ,Please waiting !!!! exit 2"
	        exit 2
		fi
	fi
	
	handle_pidid_field $LOCAL_QUALCOMM_PATH/PidIDConfig.cfg $projectname "PIDID" "write"
	handle_pidid_field $LOCAL_QUALCOMM_PATH/PidIDConfig.cfg $blockPath "PIDID" "write"
	if [[ $? -ne 0 ]];then
	    echo "write $projectname to PidIDConfig.cfg [$blockPath] failed !!!!!!!!!"
	else
	    echo "write $projectname to PidIDConfig.cfg [$blockPath] success !!!!!!!!!  project is going to build !!!!! "
		tail $doquePath/waitDoqueFile | grep ^${projectname}$
		#删除排队队列中运行项目
		if [[ $? -eq  0 ]];then
		    sed -i "/"$projectname"/d" $doquePath/waitDoqueFile
			echo "delete $projectname from $doquePath/waitDoqueFile success !!!!!!!!!"
		fi
		  
		tail $doquePath/runDoqueFile | grep ^${projectname}$
		#将项目写入正在运行队列中
		if [[ $? -ne 0 ]];then
		    echo $projectname >> $doquePath/runDoqueFile
			echo "write $projectname to $doquePath/runDoqueFile success !!!!!!!!!"
		fi
		
		exit 1
	fi
}

#兼容旧脚本艾拉比程序打包识别，仅允许当前服务器只有一个项目在运行艾拉比程序打包
function _start_ailabi_pidid()
{
    echo "Begin inquire $projectname !!!!"
	
	poolNewPidid=$(handle_pidid_field $LOCAL_QUALCOMM_PATH/PidIDConfig.cfg $blockPath "PIDID" "read")
	ps gaux | grep -v grep | awk '{print $2}' | grep "^${poolNewPidid}$"
    newAilabiPid=$?
	
	poolOldPidid=$(handle_pidid_field $LOCAL_OLD_QUALCOMM_PATH/PidIDConfig.cfg $blockPath "PIDID" "read")
	ps gaux | grep -v grep | awk '{print $2}' | grep "^${poolOldPidid}$"
    oldAilabiPid=$?
 
    if [[ $newAilabiPid -eq 0 ]] || [[ $oldAilabiPid -eq 0 ]];then
	    echo "Ailabi program is running,Please Wait !!!!!!!!!"
		exit 2
	fi
	
	handle_pidid_field $LOCAL_QUALCOMM_PATH/PidIDConfig.cfg $blockPath "PIDID" "write"
	if [[ $? -ne 0 ]];then
	    echo "write [$blockPath] in PidIDConfig.cfg [$curBashPidid] failed !!!!!!!!!"
	else
	    echo "write [$blockPath] in PidIDConfig.cfg [$curBashPidid] success !!!!!!!!"
	fi
}

_clean_doqueFile

if [[ $blockPath != "clean" ]] && [[ $blockPath != "AILABI" ]];then
   _start_inquire_pidid
fi

if [[ $blockPath = "AILABI" ]];then
   _start_ailabi_pidid
fi
