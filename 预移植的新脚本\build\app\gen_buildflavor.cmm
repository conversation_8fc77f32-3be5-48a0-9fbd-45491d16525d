ENTRY &ARG0

GLOBAL &HLOS_TYPE
GLOBAL &TZ_BUILDID
GLOBAL &T32_VER
GLOBAL &CHIPID
GLOBAL &TZ_APPS_BUILDID
GLOBAL &MODEM_BUILDID
GLOBAL &META_VARIANT
GLOBAL &KEYMASTER_BINARY
GLOBAL &IPA_FWS_ELF
GLOBAL &STORESEC_BINARY
GLOBAL &BOOT_CONFIG
GLOBAL &APPS_ELF
GLOBAL &GFX_ELF
GLOBAL &MPSS_BINARY
GLOBAL &CMNLIB64_BINARY
GLOBAL &APPSBOOT_BINARY
GLOBAL &ADSP_BINARY
GLOBAL &CMNLIB_BINARY
GLOBAL &AOP_ELF
GLOBAL &FIREHOSE_DDR_ELF
GLOBAL &HYP_BINARY
GLOBAL &MPSS_QDB_BINARY
GLOBAL &APPS_BINARY
GLOBAL &NPU_BINARY
GLOBAL &FIREHOSE_LITE_ELF
GLOBAL &QUP_BINARY
GLOBAL &TZ_BINARY
GLOBAL &BOOT_ELF
GLOBAL &APPSIFS2_BINARY
GLOBAL &AOP_BINARY
GLOBAL &CDSP_BINARY
GLOBAL &MULTIIMGOEM_BINARY
GLOBAL &IPA_UC_ELF
GLOBAL &VENUS_BINARY
GLOBAL &TZDEVCFG_BINARY
GLOBAL &UEFISEC_BINARY
GLOBAL &BOOT_BINARY

IF ("&ARG0"=="8155_la")
(
	&PRODUCT_FLAVOR="8155_la"
	&HLOS_TYPE="qx"
	&TZ_BUILDID="YAQAANAA"
	&T32_VER="LT180417"
	&CHIPID="SDM855"
	&TZ_APPS_BUILDID="YAQAANAA"
	&MODEM_BUILDID="sm6150.gennmmdf.prod"
	&META_VARIANT="SPF"
	&KEYMASTER_BINARY="trustzone_images/build/ms/bin/YAQAANAA/km4virt.mbn"
	&STORESEC_BINARY="trustzone_images/build/ms/bin/YAQAANAA/storsec.mbn"
	&APPS_ELF="out/target/product/msmnile_gvmq/obj/KERNEL_OBJ/vmlinux"
	&MPSS_BINARY="modem_proc/build/ms/bin/sm8150.gennmgw.prod/qdsp6sw.mbn"
	&CMNLIB64_BINARY="trustzone_images/build/ms/bin/YAQAANAA/cmnlib64.mbn"
	&APPSBOOT_BINARY="qnx_ap/target/hypervisor/host/abl-image/signed/default/abl/abl_fastboot.elf"
	&CMNLIB_BINARY="trustzone_images/build/ms/bin/YAQAANAA/cmnlib.mbn"
	&AOP_ELF="aop_proc/core/bsp/aop/build/AOP_AAAAANAZO.elf"
	&HYP_BINARY="qnx_ap/target/hypervisor/host/out_8155/signed/default/qhee/mifs_hyp_la.img"
	&MPSS_QDB_BINARY="modem_proc/build/ms/bin/sm8150.gennmgw.prod/qdsp6m.qdb"
	&APPS_BINARY="out/target/product/msmnile_gvmq/boot.img"
	&NPU_BINARY="npu_proc/build/ms/signed/npu.mbn"
	&QUP_BINARY="common/core_qupv3fw/sdm855/qupv3fw.elf"
	&TZ_BINARY="trustzone_images/build/ms/bin/YAQAANAA/tz.mbn"
	&APPSIFS2_BINARY="qnx_ap/target/hypervisor/host/out_8155/ifs2_la.img"
	&AOP_BINARY="aop_proc/build/ms/bin/AAAAANAZO/aop.mbn"
	&MULTIIMGOEM_BINARY="common/build/bin/8155_la/multi_image.mbn"
	&VENUS_BINARY="venus_proc/build/bsp/asic/build/PROD/mbn/reloc/signed/venus.mbn"
	&TZDEVCFG_BINARY="trustzone_images/build/ms/bin/YAQAANAA/devcfg_auto.mbn"
	&UEFISEC_BINARY="trustzone_images/build/ms/bin/YAQAANAA/uefi_sec.mbn"
)
IF ("&ARG0"=="6155_la")
(
	&PRODUCT_FLAVOR="6155_la"
	&HLOS_TYPE="qx"
	&TZ_BUILDID="PAZAANAA"
	&T32_VER="LT180417"
	&CHIPID="SM6155"
	&TZ_APPS_BUILDID="PAZAANAA"
	&MODEM_BUILDID="sm6150.gennmmdf.prod"
	&META_VARIANT="SPF"
	&KEYMASTER_BINARY="trustzone_images/build/ms/bin/PAZAANAA/km4virt.mbn"
	&STORESEC_BINARY="trustzone_images/build/ms/bin/PAZAANAA/storsec.mbn"
	&APPS_ELF="out/target/product/msmnile_gvmq/obj/KERNEL_OBJ/vmlinux"
	&MPSS_BINARY="modem_proc/build/ms/bin/sm6150.gennmmdf.prod/qdsp6sw.mbn"
	&CMNLIB64_BINARY="trustzone_images/build/ms/bin/PAZAANAA/cmnlib64.mbn"
	&APPSBOOT_BINARY="qnx_ap/target/hypervisor/host/abl-image/signed/default/abl/abl_fastboot.elf"
	&CMNLIB_BINARY="trustzone_images/build/ms/bin/PAZAANAA/cmnlib.mbn"
	&AOP_ELF="aop_proc/core/bsp/aop/build/TalosAU/AOP_AAAAANAZO.elf"
	&MPSS_QDB_BINARY="modem_proc/build/ms/bin/sm6150.gennmmdf.prod/qdsp6m.qdb"
	&APPS_BINARY="out/target/product/msmnile_gvmq/boot.img"
	&QUP_BINARY="common/core_qupv3fw/sm6150/qupv3fw.elf"
	&TZ_BINARY="trustzone_images/build/ms/bin/PAZAANAA/tz.mbn"
	&AOP_BINARY="aop_proc/build/ms/bin/AAAAANAZO/aop.mbn"
	&VENUS_BINARY="venus_proc/build/bsp/asic/build/PROD/mbn/reloc/socCheckV6/signed/venus.mbn"
	&TZDEVCFG_BINARY="trustzone_images/build/ms/bin/PAZAANAA/devcfg_auto.mbn"
	&UEFISEC_BINARY="trustzone_images/build/ms/bin/PAZAANAA/uefi_sec.mbn"
)
ENDDO