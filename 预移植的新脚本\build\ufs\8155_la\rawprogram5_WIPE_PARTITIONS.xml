<?xml version="1.0" ?>
<data>
  <!--NOTE: This is an ** Autogenerated file **-->
  <!--NOTE: Sector size is 4096bytes-->
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="zeros_33sectorS.bin" label="ALIGN_TO_128K_2" num_partition_sectors="26" partofsingleimage="false" physical_partition_number="5" readbackverify="false" size_in_KB="104.0" sparse="false" start_byte_hex="0x6000" start_sector="6"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="zeros_33sectorS.bin" label="modemst1" num_partition_sectors="512" partofsingleimage="false" physical_partition_number="5" readbackverify="false" size_in_KB="2048.0" sparse="false" start_byte_hex="0x20000" start_sector="32"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="zeros_33sectorS.bin" label="modemst2" num_partition_sectors="512" partofsingleimage="false" physical_partition_number="5" readbackverify="false" size_in_KB="2048.0" sparse="false" start_byte_hex="0x220000" start_sector="544"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="zeros_33sectorS.bin" label="fsg" num_partition_sectors="512" partofsingleimage="false" physical_partition_number="5" readbackverify="false" size_in_KB="2048.0" sparse="false" start_byte_hex="0x420000" start_sector="1056"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="zeros_33sectors.bin" label="last_parti" num_partition_sectors="1" partofsingleimage="false" physical_partition_number="5" readbackverify="false" size_in_KB="4.0" sparse="false" start_byte_hex="0x620000" start_sector="1568"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="zeros_33sectors.bin" label="PrimaryGPT" num_partition_sectors="1" partofsingleimage="true" physical_partition_number="5" readbackverify="false" size_in_KB="4.0" sparse="false" start_byte_hex="0x0" start_sector="0"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="zeros_5sectors.bin" label="PrimaryGPT" num_partition_sectors="5" partofsingleimage="true" physical_partition_number="5" readbackverify="false" size_in_KB="20.0" sparse="false" start_byte_hex="0x1000" start_sector="1"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="zeros_5sectors.bin" label="BackupGPT" num_partition_sectors="5" partofsingleimage="true" physical_partition_number="5" readbackverify="false" size_in_KB="20.0" sparse="false" start_byte_hex="(4096*NUM_DISK_SECTORS)-20480." start_sector="NUM_DISK_SECTORS-5."/>
</data>
