#!/bin/bash
#****************************
# AndroidR编译脚本
# AndroidR源码下载
# Author xizhuang.wu
# version 2.0
#****************************

# echo -e "\033[33m*******************************************************************************************\033[0m"
# echo -e "\033[32m                               AndroidR_path.sh					                        \033[0m"
# echo -e "\033[33m*******************************************************************************************\033[0m"

#############################输入参数#################################
#获取本地路径
#SHELL_FOLDER=$(dirname $(readlink -f "$0"))

#####################${GLOBAL_PROJECT_MANE}###########################
if [ -n "$2" ];then
	GLOBAL_PROJECT_MANE=$2
else
	GLOBAL_PROJECT_MANE="HS7001A"
fi

#####################${GLOBAL_MK_VERSION}#############################
VERSION=$3
MCU_VERSION="V1.0"
if [ -n "$5" ];then
	VERSION=$3
else
	VERSION="V0.99.99"
fi

SUB_VERSION=${12}
#####################${GLOBAL_ISSRCTAG}###############################
#####################${GLOBAL_SRCTAG_VERSION}#########################
MAKETAG=0
READTAG=0
ISSRCTAG=$4
SRCTAG_VERSION=$5
issueVersion=$6

#####################${isBuildC1}#############################
isBuildC1=${7}

PACKAGE_BUILD_DATE=${8}

build_paramNum=${9}

package_type=${10}

isBuildSecure=${13}

isBuildXuGao=${14}

#####################${LOCAL_SOURCE_PATH}#############################
QualcommPathNum=4
QualcommLastPathName='HQX1.2.1.C1_R00004.2'
QualcommPathName='hqx1.2.1.c1_r00004.2'

SourceFolder=$1/${QualcommPathName}/android
SourceQnxSDP=$1/hqx1.2.1.c1_r00004.2/qnx_sdp
SourceAMSS=$1/${QualcommPathName}/amss

#####################${ADAYO_MPU_MODEL}###############################
ADAYO_MPU_MODEL="high"

#####################${ADAYO_IMAGE_DATE}#############################
BUILD_DATE=$(date +%m-%d-%Y)

#####################${ADAYO_BUILD_DATE_AND_TIME}#############################
ADAYO_BUILD_DATE_AND_TIME=${11}

#############################常量定义#################################
#获取用户名密码
UserName=`whoami`
USER_ROOT=`pwd`

#定义工具名称
SVN_CMD="svn"

export SDK1VERSION=0
export SDK1PATH="http://swsvn01.adayoge.com/svn/ADLibrary/00_AAOP/SDK_I/04_8155/SDK_I_source"

#定义编译目标
PLATFORM=salvator
MOD_DEF=userdebug

GLOBAL_MK_DATE=`date +%y.%m.%d`
GLOBAL_MK_TIME=`date +%H%M%S`

#保存环境变量
FGE_PATH=$PATH
FGE_JAVA_HOME=$JAVA_HOME

#项目输入数据路径:导航数据，其他数据
PACKAGE_INPUT_DATA="/mnt/BU2_NAS/Jenkins_Package_Data/SA8155"

#项目输出：Asphalt数据打包路径
ASPHALT_OUTPUT_PATH="com.gameloft.android.BAIC.GloftA9BA"

#编译错误参数   
#            101 编译报错   102 安卓编译配置出错     103 安卓升级包打包异常
#            26编译环境清理过程异常      28 SVN错误    29 Tag 版本错误
ERROR_PARAM_CLEAN=26
ERROR_PARAM_SVN=28
ERROR_PARAM_TAG=29
ERROR_PARAM_BUILD=101
ERROR_PARAM_CONFIG=102
ERROR_PARAM_PACKAGE=103

Qualcomm8155BashPath=/mnt/home/<USER>/e-cockpit/qnx-android/jenkins_qnx-android_Qualcomm8155
Build_Shell_Path="http://10.2.4.101/svn/Qualcomm/02_SA8155P/01_HS7001A/02_CodeLib/01_MainPath/1.2/02_Android/04_Shell/buildShell3.0/buildShell"
Jenkins_Source_Path="http://10.2.4.101/svn/Qualcomm/02_SA8155P/"${GLOBAL_PROJECT_MANE%%_*}"/02_CodeLib/01_ProjectPath/03_Config/Jenkins_Source_Shell"
if [[ $GLOBAL_PROJECT_MANE =~ _ ]];then
    GLOBAL_SVN_SOURCE_TAG_MAIN_HYP="http://swsvn01.adayoge.com/svn/Qualcomm/02_SA8155P/"${GLOBAL_PROJECT_MANE%%_*}"/02_CodeLib/01_ProjectPath/02_Android/03_Sdk2/04_Tag/"${GLOBAL_PROJECT_MANE}
else
    GLOBAL_SVN_SOURCE_TAG_MAIN_HYP="http://swsvn01.adayoge.com/svn/Qualcomm/02_SA8155P/"${GLOBAL_PROJECT_MANE%%_*}"/02_CodeLib/01_ProjectPath/02_Android/03_Sdk2/04_Tag"
fi

cd $USER_ROOT

#项目SVN TAG版本路径
if [[ ${isBuildSecure} = "1" ]];then
	SVN_SOURCE_TAG_PROJ_HPY=${GLOBAL_SVN_SOURCE_TAG_MAIN_HYP}/${GLOBAL_PROJECT_MANE}"_Release-"${VERSION}_safety
else
	SVN_SOURCE_TAG_PROJ_HPY=${GLOBAL_SVN_SOURCE_TAG_MAIN_HYP}/${GLOBAL_PROJECT_MANE}"_Release-"${VERSION}
fi

DEF_PROJECT_TRUNK_PATH=0
DEF_PROJECT_TAG_PATH=1
DEF_PROJECT_SOURCE_PATH=2

PACKAGE_MODULE_PATH=0
PACKAGE_MODULE_NAME=1

SVN_REVISION=0

DEF_PROJECT_DEC_NAME=0
DEF_PROJECT_DEC_VALUE=1
DEF_PROJECT_DEC_FILE=2

_build_error_exit ()
{
    EXIT_ERROR_PARAM=$1
	echo "Android build Error,Error param is $EXIT_ERROR_PARAM,exit ........"
	exit $EXIT_ERROR_PARAM
}

async_build_inspect ()
{
    if [[ -f /mnt/home/<USER>/e-cockpit/qnx-android/jenkins_qnx-android_Qualcomm8155/${GLOBAL_PROJECT_MANE}_Package/RunningFlag_${build_paramNum}/asyncBuildFlag ]];then
	    asyncFlag=`cat /mnt/home/<USER>/e-cockpit/qnx-android/jenkins_qnx-android_Qualcomm8155/${GLOBAL_PROJECT_MANE}_Package/RunningFlag_${build_paramNum}/asyncBuildFlag`
		if [[ $asyncFlag -eq 1 ]];then
		    echo "Qnx Async Build Error before,End Build Android and exit 0"
		    _build_error_exit 0
		fi
	fi
}

RUNING_ERROR_PARAM=0

###################################代码下载#####################################
_repo_source()
{
    async_build_inspect
	
    echo "Begin clean android build source ........"`date +%F" "%T`
    RUNING_ERROR_PARAM=$ERROR_PARAM_CLEAN
	
	if [[ -e $USER_ROOT/Android_code ]];then
	   rm -rf $USER_ROOT/Android_code
	fi
	
	mkdir -p $USER_ROOT/Android_code/${GLOBAL_PROJECT_MANE}_SVN_TAG
	mkdir -p $USER_ROOT/Android_code/AAOP_AndroidR
	mkdir -p $USER_ROOT/Android_code/$GLOBAL_PROJECT_MANE
	
	if [[ $USER_ROOT != "" ]] && [[ $QualcommPathName != "" ]] && [[ -e $USER_ROOT/$QualcommPathName/android ]];then
	    rm -rf $USER_ROOT/$QualcommPathName/android
	fi
	mkdir -p $USER_ROOT/$QualcommPathName/android
	echo "cp -rf Android build source from /mnt/home/<USER>/e-cockpit/qnx-android/jenkins_qnx-android_Qualcomm8155/Qualcomm8155C1Line/$QualcommPathName'_copy'/$QualcommPathName/android"
	cp -rf /mnt/home/<USER>/e-cockpit/qnx-android/jenkins_qnx-android_Qualcomm8155/Qualcomm8155C1Line/$QualcommPathName'_copy'/$QualcommPathName/android/* $USER_ROOT/$QualcommPathName/android  
	    
	if [[ $? -ne 0 ]] && (([[ `cat $Qualcomm8155BashPath/Qualcomm8155C1Line/tailFlag` -ne 99 ]] && [[ `cat $Qualcomm8155BashPath/Qualcomm8155C1Line/tailFlag` -ne 0 ]]) || [[ ! -f $Qualcomm8155BashPath/Qualcomm8155C1Line/tailFlag ]]);then
		echo "Android process begin tail /home/<USER>/e-cockpit/qualcomm/$QualcommLastPathName/$QualcommPathName.tar.gz"
		if [[ -e $Qualcomm8155BashPath/Qualcomm8155C1Line/$QualcommPathName'_copy' ]];then
		    echo "Last $Qualcomm8155BashPath/Qualcomm8155C1Line/$QualcommPathName'_copy' tar failed !!! rm and tar second time !"
			rm -rf $Qualcomm8155BashPath/Qualcomm8155C1Line/$QualcommPathName'_copy'
		fi
		mkdir -p $Qualcomm8155BashPath/Qualcomm8155C1Line/$QualcommPathName'_copy'
		echo 0 > $Qualcomm8155BashPath/Qualcomm8155C1Line/tailFlag
		tar -vxf /home/<USER>/e-cockpit/qualcomm/$QualcommLastPathName/$QualcommPathName.tar.gz -C $Qualcomm8155BashPath/Qualcomm8155C1Line/$QualcommPathName'_copy'
		if [[ $? -ne 0 ]];then
	        echo "tar -vxf /home/<USER>/e-cockpit/qualcomm/$QualcommLastPathName/$QualcommPathName.tar.gz failed !!!!"
	        rm -rf $Qualcomm8155BashPath/Qualcomm8155C1Line
			_build_error_exit $RUNING_ERROR_PARAM
	    fi
		echo 99 > $Qualcomm8155BashPath/Qualcomm8155C1Line/tailFlag
        
		echo "the twice time to cp -rf Android build source from /mnt/home/<USER>/e-cockpit/qnx-android/jenkins_qnx-android_Qualcomm8155/Qualcomm8155C1Line/$QualcommPathName'_copy'/$QualcommPathName/android"
		cp -rf $Qualcomm8155BashPath/Qualcomm8155C1Line/$QualcommPathName'_copy'/$QualcommPathName/android/* $USER_ROOT/$QualcommPathName/android
    fi
    
    if [[ $? -ne 0 ]];then
	    echo "Clean build source error,exit $RUNING_ERROR_PARAM"
	    _build_error_exit $RUNING_ERROR_PARAM
	fi
	
	async_build_inspect

    #集成导航分区（sdcard分区-北汽项目用的这个分区）
	if [[ -e $USER_ROOT/$QualcommPathName/android/sdcard ]];then
	    rm -rf $USER_ROOT/$QualcommPathName/android/sdcard
	fi
	if [[ -f $PACKAGE_INPUT_DATA/${GLOBAL_PROJECT_MANE}/NAVI/Flag.txt ]] && [[ `cat $PACKAGE_INPUT_DATA/${GLOBAL_PROJECT_MANE}/NAVI/Flag.txt` = "true" ]];then
	    mkdir -p $USER_ROOT/$QualcommPathName/android/sdcard
	    rsync -rv --exclude='Flag.txt' $PACKAGE_INPUT_DATA/${GLOBAL_PROJECT_MANE}/NAVI/* $USER_ROOT/$QualcommPathName/android/sdcard
	elif [[ -f $PACKAGE_INPUT_DATA/${GLOBAL_PROJECT_MANE%%_*}/NAVI/Flag.txt ]] && [[ `cat $PACKAGE_INPUT_DATA/${GLOBAL_PROJECT_MANE%%_*}/NAVI/Flag.txt` = "true" ]];then
	    mkdir -p $USER_ROOT/$QualcommPathName/android/sdcard
	    rsync -rv --exclude='Flag.txt' $PACKAGE_INPUT_DATA/${GLOBAL_PROJECT_MANE%%_*}/NAVI/* $USER_ROOT/$QualcommPathName/android/sdcard
	else
	    echo "sdcard Didnt inload navi"
	fi
	
	if [[ $? -ne 0 ]];then
	    echo "Clean build source error,exit $RUNING_ERROR_PARAM"
	    _build_error_exit $RUNING_ERROR_PARAM
	fi
	
	#集成Asphalt镜像（跟导航同一个分区）
	if [[ -f $PACKAGE_INPUT_DATA/${GLOBAL_PROJECT_MANE}/Asphalt/Flag.txt ]] && [[ `cat $PACKAGE_INPUT_DATA/${GLOBAL_PROJECT_MANE}/Asphalt/Flag.txt` = "true" ]];then
		mkdir -p $USER_ROOT/$QualcommPathName/android/sdcard/$ASPHALT_OUTPUT_PATH
	    rsync -rv --exclude='Flag.txt' $PACKAGE_INPUT_DATA/${GLOBAL_PROJECT_MANE}/Asphalt/* $USER_ROOT/$QualcommPathName/android/sdcard/$ASPHALT_OUTPUT_PATH
	elif [[ -f $PACKAGE_INPUT_DATA/${GLOBAL_PROJECT_MANE%%_*}/Asphalt/Flag.txt ]] && [[ `cat $PACKAGE_INPUT_DATA/${GLOBAL_PROJECT_MANE%%_*}/Asphalt/Flag.txt` = "true" ]];then
		mkdir -p $USER_ROOT/$QualcommPathName/android/sdcard/$ASPHALT_OUTPUT_PATH
	    rsync -rv --exclude='Flag.txt' $PACKAGE_INPUT_DATA/${GLOBAL_PROJECT_MANE%%_*}/Asphalt/* $USER_ROOT/$QualcommPathName/android/sdcard/$ASPHALT_OUTPUT_PATH
	else
	    echo "Didnt inload Asphalt"
	fi
	
	if [[ $? -ne 0 ]];then
	    echo "Clean build source error,exit $RUNING_ERROR_PARAM"
	    _build_error_exit $RUNING_ERROR_PARAM
	fi
	
	#集成RH850 MCU的备份文件（跟导航同一个分区）
	if [[ -f $PACKAGE_INPUT_DATA/${GLOBAL_PROJECT_MANE}/RH850/Flag.txt ]] && [[ `cat $PACKAGE_INPUT_DATA/${GLOBAL_PROJECT_MANE}/RH850/Flag.txt` = "true" ]];then
		mkdir -p $USER_ROOT/$QualcommPathName/android/sdcard
	    rsync -rv --exclude='Flag.txt' $PACKAGE_INPUT_DATA/${GLOBAL_PROJECT_MANE}/RH850/* $USER_ROOT/$QualcommPathName/android/sdcard
	elif [[ -f $PACKAGE_INPUT_DATA/${GLOBAL_PROJECT_MANE%%_*}/RH850/Flag.txt ]] && [[ `cat $PACKAGE_INPUT_DATA/${GLOBAL_PROJECT_MANE%%_*}/RH850/Flag.txt` = "true" ]];then
		mkdir -p $USER_ROOT/$QualcommPathName/android/sdcard
	    rsync -rv --exclude='Flag.txt' $PACKAGE_INPUT_DATA/${GLOBAL_PROJECT_MANE%%_*}/RH850/* $USER_ROOT/$QualcommPathName/android/sdcard
	else
	    echo "Didnt inload RH850"
	fi
	
	if [[ $? -ne 0 ]];then
	    echo "Clean build source error,exit $RUNING_ERROR_PARAM"
	    _build_error_exit $RUNING_ERROR_PARAM
	fi
	
	#集成导航分区（mapdata分区-长安项目用的这个分区）
	if [[ -d $PACKAGE_INPUT_DATA/${GLOBAL_PROJECT_MANE}/mapdata ]];then
		mkdir -p $USER_ROOT/$QualcommPathName/android/mapdata
		cp -rf $PACKAGE_INPUT_DATA/${GLOBAL_PROJECT_MANE}/mapdata/* $USER_ROOT/$QualcommPathName/android/mapdata
	elif [[ -d $PACKAGE_INPUT_DATA/${GLOBAL_PROJECT_MANE%%_*}/mapdata ]];then
		mkdir -p $USER_ROOT/$QualcommPathName/android/mapdata
		cp -rf $PACKAGE_INPUT_DATA/${GLOBAL_PROJECT_MANE%%_*}/mapdata/* $USER_ROOT/$QualcommPathName/android/mapdata
	else
		echo "mapdata Didnt inload navi"
	fi

	if [[ $? -ne 0 ]];then
	    echo "Clean build source error,exit $RUNING_ERROR_PARAM"
	    _build_error_exit $RUNING_ERROR_PARAM
	fi
	
	if [[ $Qualcomm8155BashPath != "" ]] && [[ $GLOBAL_PROJECT_MANE != "" ]] && [[ -d $Qualcomm8155BashPath/${GLOBAL_PROJECT_MANE}_Package ]];then
	    rm -rf $Qualcomm8155BashPath/${GLOBAL_PROJECT_MANE}_Package/Image/Android/*
	    rm -rf $Qualcomm8155BashPath/${GLOBAL_PROJECT_MANE}_Package/Update/Android.tar.gz
	    rm -rf $Qualcomm8155BashPath/${GLOBAL_PROJECT_MANE}_Package/output/{wlanSymbol,Android}/*
	fi
	
	if [[ ! -d $Qualcomm8155BashPath/${GLOBAL_PROJECT_MANE}_Package/Image/Android ]];then
	    mkdir -p $Qualcomm8155BashPath/${GLOBAL_PROJECT_MANE}_Package/Image/Android
	fi
	if [[ ! -d $Qualcomm8155BashPath/${GLOBAL_PROJECT_MANE}_Package/Update ]];then
	    mkdir -p $Qualcomm8155BashPath/${GLOBAL_PROJECT_MANE}_Package/Update
	fi
	if [[ ! -d $Qualcomm8155BashPath/${GLOBAL_PROJECT_MANE}_Package/output/{wlanSymbol,Android} ]];then
	    mkdir -p $Qualcomm8155BashPath/${GLOBAL_PROJECT_MANE}_Package/output/{wlanSymbol,Android}
	fi
	
	if [[ ${GLOBAL_PROJECT_MANE} =~ "HS7012A" ]] || [[ ${GLOBAL_PROJECT_MANE} =~ "HS7023A" ]] || [[ ${GLOBAL_PROJECT_MANE} =~ "HS7029A" ]];then
		rm -rf $Qualcomm8155BashPath/${GLOBAL_PROJECT_MANE}_Package/OTA/Android/*
		if [[ ! -d $Qualcomm8155BashPath/${GLOBAL_PROJECT_MANE}_Package/OTA/Android ]];then
			mkdir -p $Qualcomm8155BashPath/${GLOBAL_PROJECT_MANE}_Package/OTA/Android
		fi
	fi
	
	#HS7012A项目特殊需求，删除基线Car，使用客户提供的Car
	if ([[ ${GLOBAL_PROJECT_MANE} =~ ^HS7012A ]] || [[ ${GLOBAL_PROJECT_MANE} =~ ^HS7023A ]] || [[ ${GLOBAL_PROJECT_MANE} =~ ^HS7029A ]]) && [[ $USER_ROOT != "" ]];then
	    echo "HS7012A delete $USER_ROOT/$QualcommPathName/android/packages/services/Car"
	    rm -rf $USER_ROOT/$QualcommPathName/android/packages/services/Car
	fi
	
	#设置软链接
	cd ${SourceFolder}
	AOSP_ROOT_DIR=${SourceFolder}
	rm $AOSP_ROOT_DIR/Android.bp
	rm $AOSP_ROOT_DIR/bootstrap.bash
	ln -s $AOSP_ROOT_DIR/build/soong/root.bp $AOSP_ROOT_DIR/Android.bp
	ln -s $AOSP_ROOT_DIR/build/soong/bootstrap.bash $AOSP_ROOT_DIR/bootstrap.bash
	rm $AOSP_ROOT_DIR/build/buildspec.mk.default
	rm $AOSP_ROOT_DIR/build/CleanSpec.mk
	rm $AOSP_ROOT_DIR/build/core
	rm $AOSP_ROOT_DIR/build/envsetup.sh
	rm $AOSP_ROOT_DIR/build/target
	rm $AOSP_ROOT_DIR/build/tools
	ln -s $AOSP_ROOT_DIR/build/make/buildspec.mk.default $AOSP_ROOT_DIR/build/buildspec.mk.default
	ln -s $AOSP_ROOT_DIR/build/make/CleanSpec.mk $AOSP_ROOT_DIR/build/CleanSpec.mk
	ln -s $AOSP_ROOT_DIR/build/make/core/ $AOSP_ROOT_DIR/build/core
	ln -s $AOSP_ROOT_DIR/build/make/envsetup.sh $AOSP_ROOT_DIR/build/envsetup.sh
	ln -s $AOSP_ROOT_DIR/build/make/target/ $AOSP_ROOT_DIR/build/target
	ln -s $AOSP_ROOT_DIR/build/make/tools/ $AOSP_ROOT_DIR/build/tools
	rm $AOSP_ROOT_DIR/disregard/Android.mk
	ln -s $AOSP_ROOT_DIR/device/qcom/common/stop_scan.mk $AOSP_ROOT_DIR/disregard/Android.mk
	rm $AOSP_ROOT_DIR/vendor/qcom/build/tasks/generate_extra_images.mk
	ln -s $AOSP_ROOT_DIR/device/qcom/common/generate_extra_images.mk $AOSP_ROOT_DIR/vendor/qcom/build/tasks/generate_extra_images.mk
	rm $AOSP_ROOT_DIR/kernel/Android.mk
	ln -s $AOSP_ROOT_DIR/device/qcom/common/stop_scan.mk $AOSP_ROOT_DIR/kernel/Android.mk
	
	echo "End clean android build source ........"`date +%F" "%T`
	return 0
}

_upload_android_config ()
{
    async_build_inspect
	
    RUNING_ERROR_PARAM=$ERROR_PARAM_CONFIG
    echo "Begin upload android xml config,create source shell and add to svn!!!"
	
	if [[ -e $USER_ROOT/${Build_Shell_Path##*/}/Local_Source_Path ]];then
	   rm -rf $USER_ROOT/${Build_Shell_Path##*/}/Local_Source_Path
	fi
	mkdir -p $USER_ROOT/${Build_Shell_Path##*/}/Local_Source_Path
	$SVN_CMD checkout $Jenkins_Source_Path $USER_ROOT/${Build_Shell_Path##*/}/Local_Source_Path
	
	$USER_ROOT/${Build_Shell_Path##*/}/readXMLFile.sh Package $GLOBAL_PROJECT_MANE
	if [[ $? -ne 0 ]];then
	    _build_error_exit $RUNING_ERROR_PARAM
	fi
	mv $USER_ROOT/${Build_Shell_Path##*/}/"Package_Source_"${GLOBAL_PROJECT_MANE}.sh $USER_ROOT/${Build_Shell_Path##*/}/Local_Source_Path
	
	$USER_ROOT/${Build_Shell_Path##*/}/readXMLFile.sh Android $GLOBAL_PROJECT_MANE
	if [[ $? -eq 0 ]];then
	    if [[ ${GLOBAL_PROJECT_MANE} =~ _Release ]];then
	       ReleaseVersion=`$USER_ROOT/${Build_Shell_Path##*/}/readXMLFile.sh Qnx ${GLOBAL_PROJECT_MANE} Release Version`
	       echo "read ${Build_Shell_Path##*/}_Qnx.xml ReleaseVersion = $ReleaseVersion"
	       sed -i "s/ReleaseVersion/"$ReleaseVersion"/g" $USER_ROOT/${Build_Shell_Path##*/}/"AndroidR_Source_"${GLOBAL_PROJECT_MANE}.sh
	    fi
	
	    mv $USER_ROOT/${Build_Shell_Path##*/}/"AndroidR_Source_"${GLOBAL_PROJECT_MANE}.sh $USER_ROOT/${Build_Shell_Path##*/}/Local_Source_Path
	    $SVN_CMD add $USER_ROOT/${Build_Shell_Path##*/}/Local_Source_Path/* --no-ignore --force
        $SVN_CMD commit -m "${GLOBAL_PROJECT_MANE%%_*} AndroidR_Source_${GLOBAL_PROJECT_MANE}.sh or Package_Source_${GLOBAL_PROJECT_MANE}.s Upload Success !!!" $USER_ROOT/${Build_Shell_Path##*/}/Local_Source_Path
	    if [ $? != 0 ];then
	       echo  "write_source_shell Android_Source_Shell failed!"
	       _build_error_exit $RUNING_ERROR_PARAM
	    fi
    else
	    echo "Android XML Config didnot exit ,inload source shell whitch shell add svn by myself !!!"
	    if [[ ! -f $USER_ROOT/${Build_Shell_Path##*/}/Local_Source_Path/AndroidR_Source_${GLOBAL_PROJECT_MANE}.sh ]];then
		    echo "AndroidR_Source_${GLOBAL_PROJECT_MANE}.sh shell whitch add svn by myself didnt exit , inload AndroidR_Source_${GLOBAL_PROJECT_MANE%%_*}.sh shell !! "
		    cp -rf $USER_ROOT/${Build_Shell_Path##*/}/Local_Source_Path/AndroidR_Source_${GLOBAL_PROJECT_MANE%%_*}.sh $USER_ROOT/${Build_Shell_Path##*/}/Local_Source_Path/AndroidR_Source_${GLOBAL_PROJECT_MANE}.sh
			if [[ $? -ne 0 ]];then
			    echo "$USER_ROOT/${Build_Shell_Path##*/}/Android_Source_Shell/AndroidR_Source_${GLOBAL_PROJECT_MANE}.sh is not exitst !!!!! exit $RUNING_ERROR_PARAM!"
		        _build_error_exit $RUNING_ERROR_PARAM
			fi
		fi
	fi
	
    tmp_source_shell_name=AndroidR_Source_${GLOBAL_PROJECT_MANE}.sh
    source $USER_ROOT/${Build_Shell_Path##*/}/Local_Source_Path/${tmp_source_shell_name}
	
	if [[ -f $USER_ROOT/${Build_Shell_Path##*/}/Local_Source_Path/Package_Source_${GLOBAL_PROJECT_MANE}.sh ]];then
        package_source_shell_name=Package_Source_${GLOBAL_PROJECT_MANE}.sh
		source $USER_ROOT/${Build_Shell_Path##*/}/Local_Source_Path/${package_source_shell_name}
    fi
	
	echo "End upload android xml config,create source shell and add to svn!!!"
}


_svn_make_tag()
{
    async_build_inspect
	
    RUNING_ERROR_PARAM=$ERROR_PARAM_SVN
	echo "Begin make svn tag ......"`date +%F" "%T`
	if [ ${MAKETAG} -ne 1 ];then
		return 0
	fi

	if [ -z "$VERSION" ];then
		echo "You did not input a valid tag version."
		return -1
	fi

	svn_check_hpy=`svn list $GLOBAL_SVN_SOURCE_TAG_MAIN_HYP| grep $SVN_SOURCE_TAG_PROJ_HPY`
	SDK1VERSION=`svn info $SDK1PATH |grep "Last Changed Rev"| awk '{print substr($0,19,5)}'`
	
	$SVN_CMD info $SVN_SOURCE_TAG_PROJ_HPY
	if [[ $? -eq 0 ]];then
	   echo "Android Tag $SVN_SOURCE_TAG_PROJ_HPY is exist !!!!! "
	   echo "delect Android previous Tag $SVN_SOURCE_TAG_PROJ_HPY,and build a new Tag $SVN_SOURCE_TAG_PROJ_HPY"
	   $SVN_CMD delete -m "delect Android previous Tag $SVN_SOURCE_TAG_PROJ_HPY,and build a new Tag $SVN_SOURCE_TAG_PROJ_HPY" $SVN_SOURCE_TAG_PROJ_HPY
	   if [ $? -ne "0" ];then
		   echo "SVN delect Android previous Tag $SVN_SOURCE_TAG_PROJ_HPY,and build a new Tag $SVN_SOURCE_TAG_PROJ_HPY ERROR, exit $RUNING_ERROR_PARAM !!!!!!!"
		   _build_error_exit $RUNING_ERROR_PARAM
	   fi
	fi

	$SVN_CMD mkdir --parents -m "add tag folder for $VERSION" $SVN_SOURCE_TAG_PROJ_HPY
	if [ $? -ne 0 ];then
		echo "SVN mkdir $VERSION ERROR, exit $RUNING_ERROR_PARAM !!!!!!"
		_build_error_exit $RUNING_ERROR_PARAM
	fi
	
    $SVN_CMD checkout ${SVN_SOURCE_TAG_PROJ_HPY} $USER_ROOT/Android_code/${GLOBAL_PROJECT_MANE}_SVN_TAG
	if [ $? -ne 0 ];then
		echo "SVN mkdir $VERSION ERROR, exit $RUNING_ERROR_PARAM !!!!!!"
		_build_error_exit $RUNING_ERROR_PARAM 
	fi
	
	for i in "${GLOBAL_PROJECT_DEF_SRCCODE[@]}" ; do
		b=($i)
		if [[ "${b[${DEF_PROJECT_TAG_PATH}]}" =~ ^AAOP_.*$ ]]; then
			$SVN_CMD --force export ${b[${DEF_PROJECT_TRUNK_PATH}]} $USER_ROOT/Android_code/AAOP_AndroidR/${b[${DEF_PROJECT_TAG_PATH}]}
			if [[ `ls $USER_ROOT/Android_code/AAOP_AndroidR/${b[${DEF_PROJECT_TAG_PATH}]} | wc -l` -gt 0 ]];then
			   cp -rf $USER_ROOT/Android_code/AAOP_AndroidR/${b[${DEF_PROJECT_TAG_PATH}]} $USER_ROOT/Android_code/${GLOBAL_PROJECT_MANE}_SVN_TAG
			   $SVN_CMD add $USER_ROOT/Android_code/${GLOBAL_PROJECT_MANE}_SVN_TAG/* --no-ignore --force
			   $SVN_CMD commit -m "create tag from  to AAOP_AndroidR_${b[${DEF_PROJECT_TAG_PATH}]} ${GLOBAL_PROJECT_MANE}_Release-${VERSION}" $USER_ROOT/Android_code/${GLOBAL_PROJECT_MANE}_SVN_TAG
			   if [ $? -ne "0" ];then
		           echo " SVN add commit AAOP_AndroidR_${b[${DEF_PROJECT_TAG_PATH}]} to ${GLOBAL_PROJECT_MANE}_Release-${VERSION} ERROR, exit $RUNING_ERROR_PARAM !!!!!!!"
		           _build_error_exit $ERROR_PARAM_SVN 
	           fi
			fi
        elif [[ "${b[${DEF_PROJECT_TAG_PATH}]}" =~ ^HS.* ]]; then
		    tmp_message_hpy="create tag from ${b[${DEF_PROJECT_TRUNK_PATH}]} to  ${SVN_SOURCE_TAG_PROJ_HPY}/${b[${DEF_PROJECT_TAG_PATH}]}"
		    echo "$SVN_CMD cp -m \"create tag from ${b[${DEF_PROJECT_TRUNK_PATH}]}\"	${b[${DEF_PROJECT_TRUNK_PATH}]} 	${SVN_SOURCE_TAG_PROJ_HPY}/${b[${DEF_PROJECT_TAG_PATH}]}"
		    $SVN_CMD cp -m "create tag from ${b[${DEF_PROJECT_TRUNK_PATH}]}"	${b[${DEF_PROJECT_TRUNK_PATH}]} 	${SVN_SOURCE_TAG_PROJ_HPY}/${b[${DEF_PROJECT_TAG_PATH}]}
			if [ $? -ne "0" ];then
		        echo " SVN cp TAG ${b[${DEF_PROJECT_TRUNK_PATH}]} to ${GLOBAL_PROJECT_MANE}_Release-${VERSION} ERROR, exit $RUNING_ERROR_PARAM !!!!!!!"
		        _build_error_exit $RUNING_ERROR_PARAM
	        fi
		else
            echo "${b[${DEF_PROJECT_TAG_PATH}]} mode is didnt export in Andoird now ........"
		fi
		
		if [[ $? -ne 0 ]];then
			echo " create tag ${b[${DEF_PROJECT_TRUNK_PATH}]} error!! error code is $ret"
			_build_error_exit $RUNING_ERROR_PARAM
		fi
	done
	echo "End make svn tag ......"`date +%F" "%T`
	return 0
}

_export_project_code()
{
    async_build_inspect
	
    RUNING_ERROR_PARAM=$ERROR_PARAM_SVN
    echo "Android:Begin export SVN code ......."`date +%F" "%T`
	if [ ${MAKETAG} -eq 1 ]||[ ${READTAG} -eq 1 ]; then
		tmp_index=${DEF_PROJECT_TAG_PATH}
		tmp_path_head_hyp=${SVN_SOURCE_TAG_PROJ_HPY}
		
		$SVN_CMD info $tmp_path_head_hyp
	    if [[ $? -ne 0 ]];then
		    RUNING_ERROR_PARAM=$ERROR_PARAM_TAG
	        echo "Android History Tag $SVN_SOURCE_TAG_PROJ_HPY didnt exist !!!!! exit $RUNING_ERROR_PARAM"
	        _build_error_exit $RUNING_ERROR_PARAM
	    fi
	
	    echo "${MAKETAG} ${READTAG}  test tag"
		for i in "${GLOBAL_PROJECT_DEF_SRCCODE[@]}" ; do
		async_build_inspect
		b=($i)
		if [[ ${b[tmp_index]} =~ ^AAOP_.*$ ]]; then
		     echo "export tag --- _export_project_code ^AAOP_.*$ model = "${b[tmp_index]}" path = "${tmp_path_head_hyp}/${b[tmp_index]}
		     $SVN_CMD --force export ${tmp_path_head_hyp}/${b[tmp_index]} ${SourceFolder}/${b[${DEF_PROJECT_SOURCE_PATH}]}
             if [[ $? -ne 0 ]];then
	            echo "Android SVN export ERROR ,exit $RUNING_ERROR_PARAM !!!!! "
	            _build_error_exit $RUNING_ERROR_PARAM
	         fi			 
		elif [[ ${b[tmp_index]} =~ ^HS.*$ ]]; then
             echo "export tag --- _export_project_code ^HS.*$ model = "${b[tmp_index]}" path = "${tmp_path_head_hyp}/${b[tmp_index]}		
		     $SVN_CMD --force export ${tmp_path_head_hyp}/${b[tmp_index]} ${SourceFolder}/${b[${DEF_PROJECT_SOURCE_PATH}]}
			 if [[ $? -ne 0 ]];then
	            echo "Android SVN export ERROR ,exit $RUNING_ERROR_PARAM !!!!! "
	            _build_error_exit $RUNING_ERROR_PARAM
	         fi	
		#AMSS不打包直接取svn
		elif [[ ${b[tmp_index]} =~ ^AMSS.*$ ]];then
		     echo "export tag --- _export_project_code ^AMSS.*$ model = "${b[tmp_index]}" path = "${b[${DEF_PROJECT_TRUNK_PATH}]}
		     $SVN_CMD --force export ${b[${DEF_PROJECT_TRUNK_PATH}]} ${SourceFolder}/${b[${DEF_PROJECT_SOURCE_PATH}]}
			 if [[ $? -ne 0 ]];then
	            echo "Android SVN export ERROR ,exit $RUNING_ERROR_PARAM !!!!! "
	            _build_error_exit $RUNING_ERROR_PARAM
	         fi	
		else
		     echo "${b[tmp_index]} mode is didnt export in Andoird now ........"
		fi
		
		done
	else
	    echo "_export_project_code no make tag"
	    tmp_index=${DEF_PROJECT_TRUNK_PATH}
		tmp_path_head_hyp=""
		for i in "${GLOBAL_PROJECT_DEF_SRCCODE[@]}" ; do
		async_build_inspect
		b=($i)
		if [[ ${b[${DEF_PROJECT_TAG_PATH}]} =~ ^AAOP_.*$ ]] || [[ ${b[${DEF_PROJECT_TAG_PATH}]} =~ ^HS.*$ ]]; then
             echo "_export_project_code ^AAOP_.*$ or ^HS.*$  model = "${b[${DEF_PROJECT_TAG_PATH}]}" path = "${tmp_path_head_hyp}${b[tmp_index]}		
		     $SVN_CMD --force export ${tmp_path_head_hyp}${b[tmp_index]} ${SourceFolder}/${b[${DEF_PROJECT_SOURCE_PATH}]}
			 if [[ $? -ne 0 ]];then
	            echo "Android SVN export ERROR ,exit $RUNING_ERROR_PARAM !!!!! "
	            _build_error_exit $RUNING_ERROR_PARAM
	         fi	
	    elif [[ ${b[${DEF_PROJECT_TAG_PATH}]} =~ ^AMSS.*$ ]];then
		     echo "_export_project_code ^AMSS.*$ model = "${b[${DEF_PROJECT_TAG_PATH}]}" path = "${tmp_path_head_hyp}${b[tmp_index]}
		     $SVN_CMD --force export ${tmp_path_head_hyp}${b[tmp_index]} ${SourceFolder}/${b[${DEF_PROJECT_SOURCE_PATH}]}
			 if [[ $? -ne 0 ]];then
	            echo "Android SVN export ERROR ,exit $RUNING_ERROR_PARAM !!!!! "
	            _build_error_exit $RUNING_ERROR_PARAM
	         fi	
		else
		    echo "${b[${DEF_PROJECT_TAG_PATH}]} mode is didnt export in Andoird now ........"
		fi	
		
		done
	fi

	#HS7013A 拷贝资源出来，之后单独打包；
	if [[ $GLOBAL_PROJECT_MANE =~ "HS7013A" ]] || [[ $GLOBAL_PROJECT_MANE =~ "HS7014A" ]] || [[ $GLOBAL_PROJECT_MANE =~ "HS7003A" ]] || [[ $GLOBAL_PROJECT_MANE =~ "HS7003D" ]] || [[ $GLOBAL_PROJECT_MANE =~ "HS7026A" ]] || [[ $GLOBAL_PROJECT_MANE =~ "HS7030A" ]] || [[ $GLOBAL_PROJECT_MANE =~ "HS7034A" ]];then
		if [[ -d ${SourceFolder}/adayo/Application/res ]];then
			if [ -e $Qualcomm8155BashPath/${GLOBAL_PROJECT_MANE}_Package/Android_Res ];then
				rm -rf $Qualcomm8155BashPath/${GLOBAL_PROJECT_MANE}_Package/Android_Res
			fi
			mkdir -p $Qualcomm8155BashPath/${GLOBAL_PROJECT_MANE}_Package/Android_Res
			cp -rf ${SourceFolder}/adayo/Application/res/* $Qualcomm8155BashPath/${GLOBAL_PROJECT_MANE}_Package/Android_Res
		fi
	fi
	
	#C857项目 需要拷贝OTA的资源，之后打包OTA资源的时候使用
	if [[ $GLOBAL_PROJECT_MANE =~ "HS7012A" ]] || [[ $GLOBAL_PROJECT_MANE =~ "HS7023A" ]] || [[ $GLOBAL_PROJECT_MANE =~ "HS7029A" ]];then
		if [[ ${isBuildXuGao} = "1" ]];then
			ResPath=${SourceFolder}/adayo/Application/ota_resources/update_otax
		else
			ResPath=${SourceFolder}/adayo/Application/ota_resources/update
		fi
		
		if [[ $GLOBAL_PROJECT_MANE =~ "HS7023A" ]];then
			ResPath=${SourceFolder}/adayo/Application/OTA_RES/update
		fi
		
		if [[ -d ${ResPath}/appdata ]];then
			if [ -e $Qualcomm8155BashPath/${GLOBAL_PROJECT_MANE}_Package/Android_OTA_Res ];then
				rm -rf $Qualcomm8155BashPath/${GLOBAL_PROJECT_MANE}_Package/Android_OTA_Res
			fi
			mkdir -p $Qualcomm8155BashPath/${GLOBAL_PROJECT_MANE}_Package/Android_OTA_Res
			cp -rf ${ResPath}/* $Qualcomm8155BashPath/${GLOBAL_PROJECT_MANE}_Package/Android_OTA_Res
		fi
	fi
	
	##更新BSP中hlos代码和安卓vendor（1.2基线直接包含于8155_HQX1.2.1_R00005.2路径下，1.0基线需要另外获取）   更新安卓vendor
	cp -rf ${SourceFolder}/kernel/msm-5.4/arch/arm64/boot/dts/vendor/* $SourceFolder/kernel/msm-5.4/arch/arm64/boot/dts/vendor
	cp $SourceQnxSDP/target/qnx7/usr/include/qvm/guest_shm.h $SourceFolder/kernel/msm-5.4/include/

	rm -rf ${SourceFolder}/adayo/Middleware/Java/FP
	rm -rf ${SourceFolder}/adayo/Middleware/Java/Proxy
	
	echo "Android:End export SVN code ......."`date +%F" "%T`
}

_make_jar()
{
	source build/envsetup.sh
	lunch salvator-userdebug

    cd $CarLibPath
    mma -j${NUM_JOBS}
    if [ ! $?  = 0  ]; then
        echo "failed to make JAVADOC & adayo.car.jar, please check message"
        _build_error_exit -1
    fi

	cd ${SourceFolder}
}

_android_build()
{
    async_build_inspect
	
    RUNING_ERROR_PARAM=$ERROR_PARAM_BUILD
	cd ${SourceFolder}
	echo "Begin Build Android Code and Make Image !!!!!!"`date +%F" "%T`
	
	PATH=$FGE_PATH
    JAVA_HOME=$FGE_JAVA_HOME
	
	rm build_${PLATFORM}.log
	rm -rf relese
	rm -rf releseimg
	
	#制作JAVADOC和adayo.car.jar前戏
    CarLibPath="packages/services/Car/car-lib"
    rm -rf $CarLibPath/adayosrc
    mkdir -p $CarLibPath/adayosrc/com
    ln -s ../../../../../../frameworks/base/core/java/com/adayo/ $CarLibPath/adayosrc/com/

	export SKIP_ABI_CHECKS=true
	chmod 777 build/make/tools/releasetools/add_img_to_target_files

    source build/envsetup.sh

	if [[ "${isBuildC1}" = "1" ]];then
	    echo "Lunch Android User Version"
	    lunch msmnile_gvmq-user
	else
	    echo "Lunch Android UserDebug Version"
        lunch msmnile_gvmq-userdebug
	fi
	
	async_build_inspect
	rm -f $Qualcomm8155BashPath/${GLOBAL_PROJECT_MANE}_Package/Android_Build_Error_*.log
	make update-api
	make 2>$Qualcomm8155BashPath/${GLOBAL_PROJECT_MANE}_Package/Android_Build_Error_${build_paramNum}.log

	result=$?
	echo "Build Android Over!!! Make result is "$result
	if [[ $result -ne 0  ]]; then
		echo "Failed to make, build Android ERROR ,ERROR code $result   !!!!!!!!!"
		_build_error_exit $RUNING_ERROR_PARAM
	else
	    rm -f $Qualcomm8155BashPath/${GLOBAL_PROJECT_MANE}_Package/Android_Build_Error_${build_paramNum}.log
	fi	

    if [[ -d ${SourceFolder}/sdcard ]];then
		echo "begin rebuild sdcard.img"
		if [[ -d ${SourceAMSS}/common/config/8155/ufs ]] && [[ -f ${SourceAMSS}/common/config/8155/ufs/partition_la.xml ]];then
			sizesum=`cat ${SourceAMSS}/common/config/8155/ufs/partition_la.xml |grep sdcard |awk '{print $3}' |tr -d "size_in_kb=\""`
			if [[ ${sizesum} -ne 0 ]];then
				sizesum=$(expr $sizesum \/ 1024)
				echo "sdcard size is $sizesum"
				make_ext4fs -s -l ${sizesum}M ${SourceFolder}/out/target/product/msmnile_gvmq/sdcard.img ${SourceFolder}/sdcard
			else
				echo "sdcard size is 0,use default size"
				make_ext4fs -s -l 20480M ${SourceFolder}/out/target/product/msmnile_gvmq/sdcard.img ${SourceFolder}/sdcard
			fi
		else
			echo "not find partition_la.xml,sdcard use default size"
			make_ext4fs -s -l 20480M ${SourceFolder}/out/target/product/msmnile_gvmq/sdcard.img ${SourceFolder}/sdcard
		fi
	fi
	
	if [[ -d ${SourceFolder}/mapdata ]];then
		echo "begin rebuild mapdata.img"
		if [[ -d ${SourceAMSS}/common/config/8155/ufs ]] && [[ -f ${SourceAMSS}/common/config/8155/ufs/partition_la.xml ]];then
			sizesum=`cat ${SourceAMSS}/common/config/8155/ufs/partition_la.xml |grep mapdata |awk '{print $3}' |tr -d "size_in_kb=\""`
			if [[ ${sizesum} -ne 0 ]];then
				sizesum=$(expr $sizesum \/ 1024)
				echo "mapdata size is $sizesum"
				make_ext4fs -s -l ${sizesum}M ${SourceFolder}/out/target/product/msmnile_gvmq/mapdata.img ${SourceFolder}/mapdata
			else
				echo "mapdata size is 0,use default size"
				make_ext4fs -s -l 15360M ${SourceFolder}/out/target/product/msmnile_gvmq/mapdata.img ${SourceFolder}/mapdata
			fi
		else
			echo "not find partition_la.xml,mapdata use default size"
			make_ext4fs -s -l 15360M ${SourceFolder}/out/target/product/msmnile_gvmq/mapdata.img ${SourceFolder}/mapdata
		fi
	fi
	
	echo "End Build Android Code and Make Image !!!!!!"`date +%F" "%T`
    
	_makepackage	
	
	_make_jar
}

_output_after_build()
{
    async_build_inspect
	
    cp ${SourceFolder}/out/target/common/obj/JAVA_LIBRARIES/adayo.car_intermediates/classes.jar $Qualcomm8155BashPath/${GLOBAL_PROJECT_MANE}_Package/output/Android/adayo.jar
	cp ${SourceFolder}/out/target/common/obj/JAVA_LIBRARIES/android.car-stubs_intermediates/classes.jar $Qualcomm8155BashPath/${GLOBAL_PROJECT_MANE}_Package/output/Android/car.jar
	cp ${SourceFolder}/out/target/product/msmnile_gvmq/obj/KERNEL_OBJ/vmlinux $Qualcomm8155BashPath/${GLOBAL_PROJECT_MANE}_Package/output/Android
	
	cp -rf $LOCALOUT_ANDROID/out/target/product/msmnile_gvmq/obj/vendor/qcom/opensource/wlan/qcacld-3.0/.qcn7605/qca_cld3_qcn7605.ko.unstripped $Qualcomm8155BashPath/${GLOBAL_PROJECT_MANE}_Package/output/wlanSymbol
	cp -rf $LOCALOUT_ANDROID/out/target/product/msmnile_gvmq/dlkm/lib/modules/unstripped/cnss2.ko $Qualcomm8155BashPath/${GLOBAL_PROJECT_MANE}_Package/output/wlanSymbol
}

_makepackage()
{
    async_build_inspect
	
    RUNING_ERROR_PARAM=$ERROR_PARAM_PACKAGE
	echo "begin make Android tar.gz after build images ......."
	mkdir -p ${SourceFolder}/out/target/product/msmnile_gvmq/update/$VERSION
    for v in "${GLOBAL_PROJECT_DEF_PACKPATH[@]}"
    do
        package_path=($v)
	    if [[ ${package_path[${PACKAGE_MODULE_NAME}]} = "ANDROID" ]];then
		    echo "Android Package ${package_path[${PACKAGE_MODULE_NAME}]} from ${package_path[${PACKAGE_MODULE_PATH}]}"
		    androidImgPath=${USER_ROOT}/${package_path[${PACKAGE_MODULE_PATH}]}
		    if [[ ${androidImgPath##*/} =~ \{ ]] && [[ ${androidImgPath##*/} =~ \} ]] && [[ ! ${androidImgPath%/*} =~ \{ ]];then
                androidImgPath2=`echo ${androidImgPath##*/} | awk -F '{' '{print $2}' | awk -F '}' '{print $1}'`
                androidImgArr=(${androidImgPath2//,/ })
                for arri in ${androidImgArr[@]}
                do
			        echo "Android Package Output1 --> cp -rf ${androidImgPath%/*}/${arri} ${SourceFolder}/out/target/product/msmnile_gvmq/update/$VERSION"
			        cp -rf ${androidImgPath%/*}/${arri} ${SourceFolder}/out/target/product/msmnile_gvmq/update/$VERSION
				    if [[ $? -ne 0 ]];then
						echo "cp -rf ${androidImgPath%/*}/${arri}  to ${SourceFolder}/out/target/product/msmnile_gvmq/update/$VERSION error !!! exit $RUNING_ERROR_PARAM"
						_build_error_exit $RUNING_ERROR_PARAM
					fi
                done
            else
			    if [[ ${androidImgPath%/*} =~ \{ ]];then
				    echo "Android Pakage XML Fail format error !!!!"
				    _build_error_exit $RUNING_ERROR_PARAM
				fi
		        echo "Android Package Output2 -->  cp -rf ${androidImgPath} ${SourceFolder}/out/target/product/msmnile_gvmq/update/$VERSION"
                cp -rf ${androidImgPath} ${SourceFolder}/out/target/product/msmnile_gvmq/update/$VERSION
				if [[ $? -ne 0 ]];then
				    echo "cp -rf ${androidImgPath} to ${SourceFolder}/out/target/product/msmnile_gvmq/update/$VERSION error !!! exit $RUNING_ERROR_PARAM"
					_build_error_exit $RUNING_ERROR_PARAM
				fi
            fi
        fi
    done

	cd ${SourceFolder}/out/target/product/msmnile_gvmq/update/$VERSION
	tar -czf Android.tar.gz *
	if [[ $? -ne 0 ]];then
	    echo "tar -czf Android.tar.gz from ${SourceFolder}/out/target/product/msmnile_gvmq/update/$VERSION error !!! exit $RUNING_ERROR_PARAM"
		_build_error_exit $RUNING_ERROR_PARAM
	fi
	
	cp Android.tar.gz $Qualcomm8155BashPath/${GLOBAL_PROJECT_MANE}_Package/Update
	rsync -rv --exclude='Android.tar.gz' ${SourceFolder}/out/target/product/msmnile_gvmq/update/$VERSION/* $Qualcomm8155BashPath/${GLOBAL_PROJECT_MANE}_Package/Image/Android
	if [[ ${GLOBAL_PROJECT_MANE} =~ "HS7012A" ]] || [[ ${GLOBAL_PROJECT_MANE} =~ "HS7023A" ]] || [[ ${GLOBAL_PROJECT_MANE} =~ "HS7029A" ]];then
		rsync -rv --exclude='Android.tar.gz' ${SourceFolder}/out/target/product/msmnile_gvmq/update/$VERSION/* $Qualcomm8155BashPath/${GLOBAL_PROJECT_MANE}_Package/OTA/Android
	    cp -rf ${SourceFolder}/out/target/product/msmnile_gvmq/{backup.img,boot.img,dtbo.img,persist.img,resources.img,sdcard.img,system.img,userdata.img,vbmeta.img,vendor.img,appcfg.img,mapdata.img,appdata.img,sota.img,dvr.img,android_log.img} $Qualcomm8155BashPath/${GLOBAL_PROJECT_MANE}_Package/Image/Android
	else
	    cp -rf ${SourceFolder}/out/target/product/msmnile_gvmq/{backup.img,boot.img,dtbo.img,persist.img,resources.img,sdcard.img,system.img,userdata.img,vbmeta.img,vendor.img} $Qualcomm8155BashPath/${GLOBAL_PROJECT_MANE}_Package/Image/Android
	fi
	cd ${SourceFolder}
}


_export_build_info()
{
    async_build_inspect
	
	echo "begin write Android Version to systemservice.json ......."
	rm -f ${SourceFolder}/adayo_build.cfg
	
	echo "ADAYO_MPU_MODEL=${ADAYO_MPU_MODEL}"  			                  >> ${SourceFolder}/adayo_build.cfg
	echo "ADAYO_MPU_VERSION=${VERSION}"					                  >> ${SourceFolder}/adayo_build.cfg
	echo "ADAYO_MPU_INT_VERSION=${SUB_VERSION}"				              >> ${SourceFolder}/adayo_build.cfg
	echo "ADAYO_BUILD_DATE=${ADAYO_BUILD_DATE_AND_TIME}"				  >> ${SourceFolder}/adayo_build.cfg
	
	CarType=`$USER_ROOT/${Build_Shell_Path##*/}/readXMLFile.sh Qnx ${GLOBAL_PROJECT_MANE} Package CarType`
	if [[ $CarType = "Beiqi" ]];then
		sv=`echo ${VERSION//./} | awk -F 'T' '{print $1}'`
		if [[ $sv = "" ]];then
			sv=${VERSION//./}
		fi
	
		tv="T"`echo ${VERSION//./} | awk -F 'T' '{print $2}'`
		# if [[ $tv = "T" ]];then
			# tv=""
		# fi
	
		ControlSys=`$USER_ROOT/${Build_Shell_Path##*/}/readXMLFile.sh Qnx ${GLOBAL_PROJECT_MANE} Package Version ControlSys`
		PN=`$USER_ROOT/${Build_Shell_Path##*/}/readXMLFile.sh Qnx ${GLOBAL_PROJECT_MANE} Package Version PN`
		SoftNum=`$USER_ROOT/${Build_Shell_Path##*/}/readXMLFile.sh Qnx ${GLOBAL_PROJECT_MANE} Package Version SoftNum`
		ProjectNum=`$USER_ROOT/${Build_Shell_Path##*/}/readXMLFile.sh Qnx ${GLOBAL_PROJECT_MANE} Package Version ProjectNum`
	
		if [[ "${issueVersion}" = "0" ]];then
			sueVersion=${ControlSys}${PN}${sv}${SoftNum}${ProjectNum}${tv}${PACKAGE_BUILD_DATE:2}
		else
			sueVersion=${ControlSys}${PN}${sv}${SoftNum}${ProjectNum}
		fi
	else
	    sueVersion=${VERSION}
	fi
	
	if [[ $GLOBAL_PROJECT_MANE =~ 535B ]];then
		cubProjetctName=${GLOBAL_PROJECT_MANE%%_535B*}
		cubProjetctName=${cubProjetctName}535B
	else
	    cubProjetctName=${GLOBAL_PROJECT_MANE%%_*}
	fi
	
	echo "Android After read XML ......"
	echo "ControlSys = $ControlSys    PN = $PN     SoftNum = $SoftNum     ProjectNum = $ProjectNum     sueVersion = $sueVersion"
	
	PROJECT_DEF_DECLARE=(
		"MPU		${sueVersion}				adayo/Middleware/serviceconfig/systemconfig-files/adayo/systemservice/systemservice.json"
		"INT_VER    ${SUB_VERSION}              adayo/Middleware/serviceconfig/systemconfig-files/adayo/systemservice/systemservice.json"
		"MPUSVN		${SVN_REVISION}				adayo/Middleware/serviceconfig/systemconfig-files/adayo/systemservice/systemservice.json"
		"MODLE		${cubProjetctName}		adayo/Middleware/serviceconfig/systemconfig-files/adayo/systemservice/systemservice.json"
	#	"MCU		${MCU_VERSION}		        adayo/Middleware/serviceconfig/systemconfig-files/adayo/systemservice/systemservice.json"
		"OS			Android11.0			        adayo/Middleware/serviceconfig/systemconfig-files/adayo/systemservice/systemservice.json"
		"BUILDTIME	${ADAYO_BUILD_DATE_AND_TIME} adayo/Middleware/serviceconfig/systemconfig-files/adayo/systemservice/systemservice.json"
	)
	
	echo "**************adayo build info******************"
	for line in `cat ${SourceFolder}/adayo_build.cfg`
	do
		echo $line
	done

	for i in "${PROJECT_DEF_DECLARE[@]}" ; do
		b=($i)
		name=${b[DEF_PROJECT_DEC_NAME]}
		value=${b[DEF_PROJECT_DEC_VALUE]}
		file=${SourceFolder}/${b[DEF_PROJECT_DEC_FILE]}
		echo "PROJECT_DEF_DECLARE   $b     SourceFolder = $SourceFolder     bDEF_PROJECT_DEC_FILE = ${b[DEF_PROJECT_DEC_FILE]}"
		sed -i '/^[  \t]*"'"${name}"'"/c"'"${name}"'":"'"${value}"'",' 	${file}
		echo "name:${name} value:${value} file:${file}"
	done
	
	return 0
}

_batch_all()
{
    async_build_inspect
	echo "****************************************************************"
	echo "SourceFolder=${SourceFolder}"
	echo "GLOBAL_PROJECT_MANE=${GLOBAL_PROJECT_MANE}"
	echo "ADAYO_MPU_MODEL=${ADAYO_MPU_MODEL}"
	echo "VERSION=${VERSION}"
	echo "ISSRCTAG=${ISSRCTAG}"
	echo "SRCTAG_VERSION=${SRCTAG_VERSION}"
	echo "****************************************************************"
	
	_repo_source
	
	_upload_android_config
		
	if [[ ${ISSRCTAG} = "0" ]];then
		_export_project_code
		echo "${ISSRCTAG}"
	elif [[ ${ISSRCTAG} = "1" ]];then
		MAKETAG=1
		_svn_make_tag
		_export_project_code				
    elif [[ ${ISSRCTAG} = "2" ]];then
		READTAG=1
		if [[ ${isBuildSecure} = "1" ]];then
			SVN_SOURCE_TAG_PROJ_HPY=${GLOBAL_SVN_SOURCE_TAG_MAIN_HYP}/${GLOBAL_PROJECT_MANE}"_Release-"${SRCTAG_VERSION}_safety
		else
			SVN_SOURCE_TAG_PROJ_HPY=${GLOBAL_SVN_SOURCE_TAG_MAIN_HYP}/${GLOBAL_PROJECT_MANE}"_Release-"${SRCTAG_VERSION}
		fi
		_export_project_code
	else
	    echo "Build Android Choose Type is inVailed ,please input from 0 to 2 !!!!! "
		_build_error_exit 2
	fi
					
	_export_build_info
	
	_android_build
	
	_output_after_build

	return 0
}

_buildstart()
{
    async_build_inspect
	
	echo "_buildstart"
	if [ "Jenkins" = "$('whoami')" ] ; then
	
		DDRTYPE=${VERSION%%.*}
		if [ "V03" = "$DDRTYPE" ]; then
			_batch_all
			result=$?
			if [ ! $result  = 0  ]; then
				echo "_android_build error"
				return $result
			fi
			rm -rf ${SourceFolder}/SDK2Release
			rm -rf ${SourceFolder}/out
			VERSION=V02.${VERSION#*.}
		fi
		
		echo "_buildstart jenkins rebuild $VERSION"
		_batch_all
		result=$?
		if [ ! $result  = 0  ]; then
			echo "_android_build error"
			return $result
		fi
		
	else
		_batch_all
		result=$?
		if [ ! $result  = 0  ]; then
			echo "_android_build error"
			return $result
		fi
	fi
	
	echo "Build Finish"
	
	return 0
}

_buildstart
