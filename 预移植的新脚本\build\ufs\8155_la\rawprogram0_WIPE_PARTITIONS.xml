<?xml version="1.0" ?>
<data>
  <!--NOTE: This is an ** Autogenerated file **-->
  <!--NOTE: Sector size is 4096bytes-->
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="zeros_33sectorS.bin" label="ssd" num_partition_sectors="2" partofsingleimage="false" physical_partition_number="0" readbackverify="false" size_in_KB="8.0" sparse="false" start_byte_hex="0x6000" start_sector="6"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="zeros_33sectorS.bin" label="misc" num_partition_sectors="256" partofsingleimage="false" physical_partition_number="0" readbackverify="false" size_in_KB="1024.0" sparse="false" start_byte_hex="0x8000" start_sector="8"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="zeros_33sectorS.bin" label="keystore" num_partition_sectors="128" partofsingleimage="false" physical_partition_number="0" readbackverify="false" size_in_KB="512.0" sparse="false" start_byte_hex="0x108000" start_sector="264"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="zeros_33sectorS.bin" label="frp" num_partition_sectors="128" partofsingleimage="false" physical_partition_number="0" readbackverify="false" size_in_KB="512.0" sparse="false" start_byte_hex="0x188000" start_sector="392"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="zeros_33sectorS.bin" label="boot_a" num_partition_sectors="16384" partofsingleimage="false" physical_partition_number="0" readbackverify="false" size_in_KB="65536.0" sparse="false" start_byte_hex="0x208000" start_sector="520"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="zeros_33sectorS.bin" label="boot_b" num_partition_sectors="16384" partofsingleimage="false" physical_partition_number="0" readbackverify="false" size_in_KB="65536.0" sparse="false" start_byte_hex="0x4208000" start_sector="16904"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="zeros_33sectorS.bin" label="vbmeta_a" num_partition_sectors="16" partofsingleimage="false" physical_partition_number="0" readbackverify="false" size_in_KB="64.0" sparse="false" start_byte_hex="0x8208000" start_sector="33288"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="zeros_33sectorS.bin" label="vbmeta_b" num_partition_sectors="16" partofsingleimage="false" physical_partition_number="0" readbackverify="false" size_in_KB="64.0" sparse="false" start_byte_hex="0x8218000" start_sector="33304"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="zeros_33sectorS.bin" label="system_a" num_partition_sectors="524288" partofsingleimage="false" physical_partition_number="0" readbackverify="false" size_in_KB="2097152.0" sparse="false" start_byte_hex="0x8228000" start_sector="33320"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="zeros_33sectorS.bin" label="system_b" num_partition_sectors="524288" partofsingleimage="false" physical_partition_number="0" readbackverify="false" size_in_KB="2097152.0" sparse="false" start_byte_hex="0x88228000" start_sector="557608"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="zeros_33sectorS.bin" label="system_recovery" num_partition_sectors="524288" partofsingleimage="false" physical_partition_number="0" readbackverify="false" size_in_KB="2097152.0" sparse="false" start_byte_hex="0x108228000" start_sector="1081896"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="zeros_33sectorS.bin" label="persist" num_partition_sectors="8192" partofsingleimage="false" physical_partition_number="0" readbackverify="false" size_in_KB="32768.0" sparse="false" start_byte_hex="0x188228000" start_sector="1606184"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="zeros_33sectorS.bin" label="firmware" num_partition_sectors="24320" partofsingleimage="false" physical_partition_number="0" readbackverify="false" size_in_KB="97280.0" sparse="false" start_byte_hex="0x18a228000" start_sector="1614376"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="zeros_33sectorS.bin" label="userdata" num_partition_sectors="131072" partofsingleimage="false" physical_partition_number="0" readbackverify="false" size_in_KB="524288.0" sparse="false" start_byte_hex="0x190128000" start_sector="1638696"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="zeros_33sectorS.bin" label="ifs2_a" num_partition_sectors="65536" partofsingleimage="false" physical_partition_number="0" readbackverify="false" size_in_KB="262144.0" sparse="false" start_byte_hex="0x1b0128000" start_sector="1769768"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="zeros_33sectorS.bin" label="ifs2_b" num_partition_sectors="65536" partofsingleimage="false" physical_partition_number="0" readbackverify="false" size_in_KB="262144.0" sparse="false" start_byte_hex="0x1c0128000" start_sector="1835304"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="zeros_33sectorS.bin" label="ifs2_recovery" num_partition_sectors="65536" partofsingleimage="false" physical_partition_number="0" readbackverify="false" size_in_KB="262144.0" sparse="false" start_byte_hex="0x1d0128000" start_sector="1900840"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="zeros_33sectorS.bin" label="bluetooth_a" num_partition_sectors="512" partofsingleimage="false" physical_partition_number="0" readbackverify="false" size_in_KB="2048.0" sparse="false" start_byte_hex="0x1e0128000" start_sector="1966376"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="zeros_33sectorS.bin" label="bluetooth_b" num_partition_sectors="512" partofsingleimage="false" physical_partition_number="0" readbackverify="false" size_in_KB="2048.0" sparse="false" start_byte_hex="0x1e0328000" start_sector="1966888"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="zeros_33sectorS.bin" label="dsp_a" num_partition_sectors="16384" partofsingleimage="false" physical_partition_number="0" readbackverify="false" size_in_KB="65536.0" sparse="false" start_byte_hex="0x1e0528000" start_sector="1967400"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="zeros_33sectorS.bin" label="dsp_b" num_partition_sectors="16384" partofsingleimage="false" physical_partition_number="0" readbackverify="false" size_in_KB="65536.0" sparse="false" start_byte_hex="0x1e4528000" start_sector="1983784"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="zeros_33sectorS.bin" label="modem_a" num_partition_sectors="46080" partofsingleimage="false" physical_partition_number="0" readbackverify="false" size_in_KB="184320.0" sparse="false" start_byte_hex="0x1e8528000" start_sector="2000168"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="zeros_33sectorS.bin" label="modem_b" num_partition_sectors="46080" partofsingleimage="false" physical_partition_number="0" readbackverify="false" size_in_KB="184320.0" sparse="false" start_byte_hex="0x1f3928000" start_sector="2046248"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="zeros_33sectorS.bin" label="info" num_partition_sectors="4096" partofsingleimage="false" physical_partition_number="0" readbackverify="false" size_in_KB="16384.0" sparse="false" start_byte_hex="0x1fed28000" start_sector="2092328"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="zeros_33sectorS.bin" label="errorcode" num_partition_sectors="131072" partofsingleimage="false" physical_partition_number="0" readbackverify="false" size_in_KB="524288.0" sparse="false" start_byte_hex="0x1ffd28000" start_sector="2096424"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="zeros_33sectorS.bin" label="log" num_partition_sectors="131072" partofsingleimage="false" physical_partition_number="0" readbackverify="false" size_in_KB="524288.0" sparse="false" start_byte_hex="0x21fd28000" start_sector="2227496"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="zeros_33sectorS.bin" label="fota" num_partition_sectors="5242880" partofsingleimage="false" physical_partition_number="0" readbackverify="false" size_in_KB="20971520.0" sparse="false" start_byte_hex="0x23fd28000" start_sector="2358568"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="zeros_33sectorS.bin" label="la_boot_a" num_partition_sectors="16384" partofsingleimage="false" physical_partition_number="0" readbackverify="false" size_in_KB="65536.0" sparse="false" start_byte_hex="0x73fd28000" start_sector="7601448"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="zeros_33sectorS.bin" label="la_system_a" num_partition_sectors="2621440" partofsingleimage="false" physical_partition_number="0" readbackverify="false" size_in_KB="10485760.0" sparse="false" start_byte_hex="0x743d28000" start_sector="7617832"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="zeros_33sectorS.bin" label="la_persist" num_partition_sectors="8192" partofsingleimage="false" physical_partition_number="0" readbackverify="false" size_in_KB="32768.0" sparse="false" start_byte_hex="0x9c3d28000" start_sector="10239272"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="zeros_33sectorS.bin" label="la_vendor_a" num_partition_sectors="262144" partofsingleimage="false" physical_partition_number="0" readbackverify="false" size_in_KB="1048576.0" sparse="false" start_byte_hex="0x9c5d28000" start_sector="10247464"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="zeros_33sectorS.bin" label="la_vbmeta_a" num_partition_sectors="16" partofsingleimage="false" physical_partition_number="0" readbackverify="false" size_in_KB="64.0" sparse="false" start_byte_hex="0xa05d28000" start_sector="10509608"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="zeros_33sectorS.bin" label="la_boot_b" num_partition_sectors="16384" partofsingleimage="false" physical_partition_number="0" readbackverify="false" size_in_KB="65536.0" sparse="false" start_byte_hex="0xa05d38000" start_sector="10509624"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="zeros_33sectorS.bin" label="la_system_b" num_partition_sectors="2621440" partofsingleimage="false" physical_partition_number="0" readbackverify="false" size_in_KB="10485760.0" sparse="false" start_byte_hex="0xa09d38000" start_sector="10526008"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="zeros_33sectorS.bin" label="la_vendor_b" num_partition_sectors="262144" partofsingleimage="false" physical_partition_number="0" readbackverify="false" size_in_KB="1048576.0" sparse="false" start_byte_hex="0xc89d38000" start_sector="13147448"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="zeros_33sectorS.bin" label="la_misc" num_partition_sectors="256" partofsingleimage="false" physical_partition_number="0" readbackverify="false" size_in_KB="1024.0" sparse="false" start_byte_hex="0xcc9d38000" start_sector="13409592"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="zeros_33sectorS.bin" label="la_vbmeta_b" num_partition_sectors="16" partofsingleimage="false" physical_partition_number="0" readbackverify="false" size_in_KB="64.0" sparse="false" start_byte_hex="0xcc9e38000" start_sector="13409848"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="zeros_33sectorS.bin" label="la_userdata" num_partition_sectors="7864320" partofsingleimage="false" physical_partition_number="0" readbackverify="false" size_in_KB="31457280.0" sparse="false" start_byte_hex="0xcc9e48000" start_sector="13409864"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="zeros_33sectorS.bin" label="resources" num_partition_sectors="262144" partofsingleimage="false" physical_partition_number="0" readbackverify="false" size_in_KB="1048576.0" sparse="false" start_byte_hex="0x1449e48000" start_sector="21274184"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="zeros_33sectorS.bin" label="metazone" num_partition_sectors="12288" partofsingleimage="false" physical_partition_number="0" readbackverify="false" size_in_KB="49152.0" sparse="false" start_byte_hex="0x1489e48000" start_sector="21536328"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="zeros_33sectorS.bin" label="sdcard" num_partition_sectors="7864320" partofsingleimage="false" physical_partition_number="0" readbackverify="false" size_in_KB="31457280.0" sparse="false" start_byte_hex="0x148ce48000" start_sector="21548616"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="zeros_33sectors.bin" label="last_parti" num_partition_sectors="1" partofsingleimage="false" physical_partition_number="0" readbackverify="false" size_in_KB="4.0" sparse="false" start_byte_hex="0x1c0ce48000" start_sector="29412936"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="zeros_33sectors.bin" label="PrimaryGPT" num_partition_sectors="1" partofsingleimage="true" physical_partition_number="0" readbackverify="false" size_in_KB="4.0" sparse="false" start_byte_hex="0x0" start_sector="0"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="zeros_5sectors.bin" label="PrimaryGPT" num_partition_sectors="5" partofsingleimage="true" physical_partition_number="0" readbackverify="false" size_in_KB="20.0" sparse="false" start_byte_hex="0x1000" start_sector="1"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="zeros_5sectors.bin" label="BackupGPT" num_partition_sectors="5" partofsingleimage="true" physical_partition_number="0" readbackverify="false" size_in_KB="20.0" sparse="false" start_byte_hex="(4096*NUM_DISK_SECTORS)-20480." start_sector="NUM_DISK_SECTORS-5."/>
</data>
