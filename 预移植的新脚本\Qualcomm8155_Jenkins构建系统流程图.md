# Qualcomm8155 Jenkins自动编译系统 - 流程图文档

## 📋 系统概述

这是一个复杂的多模块并行构建系统，支持Android、QNX、AMSS等多个模块的联合构建。系统采用进程池管理机制，支持最多4个并发构建，9个等待队列。

## 🏗️ 1. 总体架构流程图

```mermaid
graph TD
    A[Jenkins触发构建] --> B[E-Cockpit_Qualcomm8155_build.sh<br/>主入口脚本]
    B --> C{项目类型判断}
    
    C -->|HS7002/HS7010B/HS7016/HS7019| D[_start_build<br/>传统构建流程]
    C -->|HS7025| E[ratbuild.sh<br/>RAT构建流程]
    C -->|其他项目| F[_start_build_xml<br/>XML配置构建流程]
    
    F --> G[进程池管理<br/>Qualcomm8155BashPID.sh]
    G --> H{检查构建池状态}
    H -->|池满| I[等待队列<br/>最多9个等待]
    H -->|有空闲| J[分配构建池<br/>最多4个并发]
    
    I --> K[定期检查池状态<br/>每90秒检查一次]
    K --> H
    
    J --> L[设置构建环境变量]
    L --> M{构建类型选择}
    
    M -->|SW1_NAME=0或1| N[Android + QNX联合构建]
    M -->|SW1_NAME=2| O[仅Android构建]
    M -->|SW1_NAME=3| P[仅QNX构建]
    
    N --> Q[Android构建<br/>AndroidR_GA.sh]
    N --> R[QNX构建<br/>Qualcomm8155QnxBuild.sh]
    O --> Q
    P --> R
    
    Q --> S[Android构建流程]
    R --> T[QNX构建流程]
    
    S --> U{构建结果检查}
    T --> U
    
    U -->|成功| V[后处理阶段]
    U -->|失败| W[错误处理<br/>CollectBuildError.py]
    
    V --> X{是否需要OTA包}
    X -->|SW3_NAME=2| Y[OTA包构建<br/>buildOtaPackage.sh]
    X -->|否| Z[跳过OTA构建]
    
    Y --> AA[打包阶段<br/>MakePackage_3.0.sh]
    Z --> AA
    
    AA --> BB{是否需要UFS构建}
    BB -->|JENKINS_BUILD_UFS=1| CC[UFS构建<br/>Ufs_Qualcomm_Build.sh]
    BB -->|否| DD[跳过UFS构建]
    
    CC --> EE[最终打包和上传]
    DD --> EE
    
    EE --> FF{是否上传JFROG}
    FF -->|是| GG[上传到JFROG仓库]
    FF -->|否| HH[本地存储]
    
    GG --> II[构建完成通知]
    HH --> II
    
    W --> JJ[错误日志收集]
    JJ --> KK[邮件通知<br/>AutoSendEmail.py]
    
    II --> LL[清理临时文件]
    LL --> MM[释放构建池资源]
    MM --> NN[构建结束]
    
    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style Q fill:#e8f5e8
    style R fill:#fff3e0
    style AA fill:#fce4ec
    style W fill:#ffebee
```

## 📱 2. Android构建详细流程图

```mermaid
graph TD
    A[AndroidR_GA.sh启动] --> B[参数解析和环境设置]
    B --> C[设置构建路径和变量<br/>USER_ROOT, SourceFolder等]
    
    C --> D[XML配置读取<br/>readXMLFile.sh Android]
    D --> E[生成源码配置数组<br/>GLOBAL_PROJECT_DEF_SRCCODE]
    
    E --> F{是否需要创建TAG}
    F -->|MAKETAG=1| G[_svn_make_tag<br/>创建SVN标签]
    F -->|否| H[_export_project_code<br/>导出源码]
    
    G --> H
    H --> I[源码导出循环处理]
    I --> J{源码类型判断}
    
    J -->|AAOP_*| K[导出AAOP相关源码]
    J -->|AMSS| L[导出AMSS源码]
    J -->|其他| M[导出其他模块源码]
    
    K --> N[源码后处理]
    L --> N
    M --> N
    
    N --> O[_make_jar<br/>构建JAR包]
    O --> P[设置Android构建环境]
    
    P --> Q[动态分区配置设置<br/>BOARD_SUPER_PARTITION_SIZE等]
    Q --> R[source build/envsetup.sh]
    R --> S{构建版本选择}
    
    S -->|isBuildC1=1| T[lunch msmnile_gvmq-user]
    S -->|否| U[lunch msmnile_gvmq-userdebug]
    
    T --> V[显示动态分区配置]
    U --> V
    V --> W[make update-api]
    W --> X[make 主构建过程]
    
    X --> Y{构建结果检查}
    Y -->|失败| Z[构建错误处理]
    Y -->|成功| AA[检查生成的镜像文件]
    
    AA --> BB{super.img检查}
    BB -->|存在| CC[super.img生成成功]
    BB -->|不存在| DD[警告：super.img未生成<br/>检查传统分区文件]
    
    CC --> EE[自定义镜像构建]
    DD --> EE
    
    EE --> FF{是否有sdcard目录}
    FF -->|是| GG[构建sdcard.img<br/>使用partition_la.xml配置]
    FF -->|否| HH[跳过sdcard.img]
    
    GG --> II{是否有mapdata目录}
    HH --> II
    II -->|是| JJ[构建mapdata.img<br/>使用partition_la.xml配置]
    II -->|否| KK[跳过mapdata.img]
    
    JJ --> LL[_makepackage<br/>打包Android镜像]
    KK --> LL
    
    LL --> MM[复制镜像到打包目录<br/>GLOBAL_PROJECT_DEF_PACKPATH]
    MM --> NN{镜像文件检查}
    
    NN -->|缺少文件| OO[警告：镜像文件缺失]
    NN -->|完整| PP[Android构建完成]
    
    OO --> QQ[记录错误日志]
    PP --> RR[返回构建结果]
    QQ --> RR
    
    Z --> SS[分析构建错误类型]
    SS --> TT[生成错误报告]
    TT --> UU[返回错误码]
    
    style A fill:#e8f5e8
    style Q fill:#e1f5fe
    style X fill:#fff3e0
    style BB fill:#fce4ec
    style Z fill:#ffebee
```

## 🔧 3. QNX构建详细流程图

```mermaid
graph TD
    A[Qualcomm8155QnxBuild.sh启动] --> B[参数解析和环境设置]
    B --> C[设置构建路径变量<br/>LOCAL_PATH, LOCALQUA_BSP_PATH等]

    C --> D[set_version<br/>设置版本信息]
    D --> E[sync_mainline<br/>同步主线代码]

    E --> F[清理旧的构建源码]
    F --> G[解压QNX基线代码<br/>hqx1.2.1.c1_r00004.2.tar.gz]

    G --> H{是否需要SDP}
    H -->|isBuildC1=0且SDP不存在| I[解压SDP<br/>sdp700_hqx1_2.tar.gz]
    H -->|否| J[跳过SDP解压]

    I --> K[QNX许可证注册]
    J --> K
    K --> L[解压许可证到.qnx/license]
    L --> M[复制许可证到qnx_bins/license]

    M --> N[设置脚本权限<br/>chmod 755/777]
    N --> O[_upload_qnx_config<br/>上传QNX配置]

    O --> P[XML配置读取<br/>readXMLFile.sh Qnx Install]
    P --> Q[生成源码配置数组<br/>GLOBAL_PROJECT_DEF_SRCCODE]

    Q --> R{是否需要创建TAG}
    R -->|MAKETAG=1| S[_svn_make_tag<br/>创建SVN标签]
    R -->|否| T[_export_project_code<br/>导出源码]

    S --> T
    T --> U[源码导出循环处理]
    U --> V{源码模块类型}

    V -->|MPU| W[处理MPU模块<br/>apps/mids/libs列表]
    V -->|MCU| X[处理MCU模块<br/>更新渠道]
    V -->|4G_MODULE| Y[处理4G模块<br/>更新渠道]
    V -->|其他| Z[处理其他模块]

    W --> AA[删除重复模块]
    X --> AA
    Y --> AA
    Z --> AA

    AA --> BB[set_config_versions<br/>设置配置版本]
    BB --> CC{构建类型选择}

    CC -->|isBuildSecure=1| DD[secure_boot_make<br/>安全启动构建]
    CC -->|否| EE[_build<br/>普通构建]

    DD --> FF[AMSS安全构建流程]
    EE --> GG[QNX普通构建流程]

    FF --> HH[构建ADSP<br/>build.py -c sm8150 -o all -f ADSP]
    FF --> II[构建TZ<br/>trustzone构建]
    FF --> JJ[构建AOP<br/>aop_proc/build/build_855au.sh]

    HH --> KK[QNX构建<br/>make all]
    II --> KK
    JJ --> KK

    GG --> LL[QNX构建环境设置<br/>source setenv_64.sh]
    LL --> MM[make clean]
    MM --> NN[make all]

    KK --> OO{构建结果检查}
    NN --> OO

    OO -->|失败| PP[构建错误分析<br/>MPU/BSP/AMSS错误分类]
    OO -->|成功| QQ[AMSS Meta构建准备]

    QQ --> RR[等待Android子进程完成]
    RR --> SS[检查必需文件<br/>Android/QNX/AMSS镜像]

    SS --> TT{构建类型检查}
    TT -->|非安全启动| UU[修改rawprogram.xml<br/>注释xbl文件引用]
    TT -->|安全启动| VV[保持原始配置]

    UU --> WW[执行build.py --flavors=8155_la]
    VV --> WW

    WW --> XX{Meta构建结果}
    XX -->|失败| YY[分析Meta构建错误<br/>文件验证失败等]
    XX -->|成功| ZZ[恢复rawprogram.xml]

    ZZ --> AAA[构建CDT<br/>cdt_generator.py]
    AAA --> BBB[镜像文件打包<br/>GLOBAL_PROJECT_DEF_PACKPATH]

    BBB --> CCC[复制到打包目录]
    CCC --> DDD[_output_install<br/>输出符号文件]

    PP --> EEE[错误日志记录]
    YY --> EEE
    DDD --> FFF[QNX构建完成]
    EEE --> FFF

    style A fill:#fff3e0
    style K fill:#e1f5fe
    style WW fill:#fce4ec
    style XX fill:#e8f5e8
    style PP fill:#ffebee
```

## ⏱️ 4. 系统时序图 - Jenkins构建完整时序

```mermaid
sequenceDiagram
    participant J as Jenkins
    participant M as 主脚本<br/>E-Cockpit_Qualcomm8155_build.sh
    participant P as 进程池管理<br/>Qualcomm8155BashPID.sh
    participant X as XML读取<br/>readXMLFile.sh
    participant A as Android构建<br/>AndroidR_GA.sh
    participant Q as QNX构建<br/>Qualcomm8155QnxBuild.sh
    participant S as SVN服务器
    participant F as 文件系统

    J->>M: 触发构建(项目参数)
    M->>M: 解析构建参数
    M->>P: 检查构建池状态

    alt 构建池已满
        P->>P: 等待队列(最多9个)
        P->>P: 每90秒检查一次
    else 有空闲池
        P->>M: 分配构建池(Pool0-3)
    end

    M->>X: 读取XML配置文件
    X->>S: 获取SVN配置信息
    S-->>X: 返回源码路径配置
    X-->>M: 返回配置数组

    par Android构建流程
        M->>A: 启动Android构建
        A->>X: 读取Android XML配置
        X-->>A: 返回Android源码配置
        A->>S: 导出Android源码
        S-->>A: 源码下载完成
        A->>A: 设置构建环境
        A->>A: lunch msmnile_gvmq-userdebug
        A->>A: 设置动态分区配置
        A->>A: make update-api
        A->>A: make (主构建)

        alt 构建成功
            A->>F: 检查生成的镜像文件
            F-->>A: 镜像文件状态
            A->>A: 构建自定义镜像
            A-->>M: Android构建完成
        else 构建失败
            A->>A: 分析构建错误
            A-->>M: 返回错误码
        end
    and QNX构建流程
        M->>Q: 启动QNX构建
        Q->>Q: set_version(设置版本)
        Q->>Q: sync_mainline(同步主线)
        Q->>F: 解压QNX基线代码
        Q->>F: 解压SDP(如需要)
        Q->>F: 注册QNX许可证
        Q->>X: 读取QNX XML配置
        X-->>Q: 返回QNX源码配置
        Q->>S: 导出QNX源码(循环)
        S-->>Q: 源码下载完成
        Q->>Q: set_config_versions

        alt 安全启动构建
            Q->>Q: secure_boot_make
            Q->>Q: 构建ADSP/TZ/AOP
        else 普通构建
            Q->>Q: _build
            Q->>Q: QNX环境设置
        end

        Q->>Q: make clean && make all

        alt QNX构建成功
            Q->>Q: 等待Android完成
            Q->>F: 检查必需文件

            alt 非安全启动
                Q->>Q: 修改rawprogram.xml
                Q->>Q: 注释xbl文件引用
            end

            Q->>Q: AMSS Meta构建
            Q->>Q: build.py --flavors=8155_la

            alt Meta构建成功
                Q->>Q: 恢复rawprogram.xml
                Q->>Q: 构建CDT
                Q-->>M: QNX构建完成
            else Meta构建失败
                Q->>Q: 分析Meta错误
                Q-->>M: 返回错误码
            end
        else QNX构建失败
            Q->>Q: 错误分析和日志
            Q-->>M: 返回错误码
        end
    end

    alt 构建成功
        M->>M: 后处理阶段

        alt 需要OTA包
            M->>M: buildOtaPackage.sh
        end

        M->>M: MakePackage_3.0.sh(打包)

        alt 需要UFS构建
            M->>M: Ufs_Qualcomm_Build.sh
        end

        alt 上传JFROG
            M->>M: 上传到JFROG仓库
        end

        M->>M: 清理和通知
        M-->>J: 构建成功
    else 构建失败
        M->>M: CollectBuildError.py
        M->>M: AutoSendEmail.py
        M-->>J: 构建失败
    end

    M->>P: 释放构建池资源
    P-->>M: 资源释放完成
```

## 🔄 5. 进程池管理详细流程图

```mermaid
graph TD
    A[Jenkins触发构建请求] --> B[Qualcomm8155BashPID.sh<br/>进程池管理器启动]
    B --> C[读取当前系统状态]

    C --> D{检查构建池状态}
    D --> E[扫描Pool0-Pool3状态]
    E --> F[读取各池的PID文件]

    F --> G{Pool状态分析}
    G -->|所有池都忙| H[进入等待队列]
    G -->|有空闲池| I[分配空闲池]

    H --> J[等待队列管理<br/>最多9个等待]
    J --> K{等待队列状态}
    K -->|队列满| L[拒绝新请求<br/>返回错误]
    K -->|队列未满| M[加入等待队列]

    M --> N[设置等待标志<br/>waitDoqueNum++]
    N --> O[每90秒检查一次池状态]
    O --> P{重新检查池状态}
    P -->|仍然全忙| O
    P -->|有空闲| Q[从等待队列分配池]

    I --> R[选择空闲池<br/>Pool0优先]
    Q --> R
    R --> S[设置池状态为忙碌]
    S --> T[创建PID文件<br/>记录当前进程信息]

    T --> U[设置构建环境变量]
    U --> V[启动构建子进程<br/>Android + QNX并行]

    V --> W[监控构建进程状态]
    W --> X{构建进程监控}
    X -->|进程运行中| Y[定期检查进程状态<br/>每30秒检查]
    X -->|进程完成| Z[清理池资源]
    X -->|进程异常| AA[异常处理]

    Y --> BB{进程健康检查}
    BB -->|正常| Y
    BB -->|超时或异常| CC[强制终止进程]

    Z --> DD[删除PID文件]
    AA --> CC
    CC --> DD

    DD --> EE[清理临时文件]
    EE --> FF[重置池状态为空闲]
    FF --> GG[更新池计数器]

    GG --> HH{是否有等待队列}
    HH -->|有等待| II[通知等待队列<br/>可以分配新池]
    HH -->|无等待| JJ[池管理完成]

    II --> KK[从等待队列取出下一个请求]
    KK --> R

    L --> LL[记录拒绝日志]
    JJ --> MM[进程池管理器待命]
    LL --> MM

    MM --> NN{新的构建请求}
    NN -->|有新请求| D
    NN -->|无请求| MM

    style A fill:#e1f5fe
    style H fill:#fff3e0
    style L fill:#ffebee
    style V fill:#e8f5e8
    style CC fill:#fce4ec
```

## 📊 6. 系统技术细节

### 🏗️ **系统架构特点**

#### **核心组件**：
- **主入口脚本**: E-Cockpit_Qualcomm8155_build.sh
- **进程池管理器**: Qualcomm8155BashPID.sh
- **Android构建器**: AndroidR_GA.sh
- **QNX构建器**: Qualcomm8155QnxBuild.sh
- **配置读取器**: readXMLFile.sh

#### **并发控制**：
- **构建池数量**: 4个 (Pool0-Pool3)
- **等待队列**: 最多9个任务
- **轮询间隔**: 90秒检查一次资源状态
- **进程监控**: 每30秒检查进程健康状态

### 🔧 **关键技术实现**

#### **SVN认证机制**：
```bash
--username gjzhao --password gjz123 --no-auth-cache --trust-server-cert
```

#### **动态分区配置**：
```bash
export BOARD_SUPER_PARTITION_SIZE=**********
export BOARD_BUILD_SUPER_IMAGE_BY_DEFAULT=true
export BOARD_SUPER_PARTITION_GROUPS="qti_dynamic_partitions"
export BOARD_QTI_DYNAMIC_PARTITIONS_SIZE=**********
export BOARD_QTI_DYNAMIC_PARTITIONS_PARTITION_LIST="system vendor"
```

#### **QNX许可证双重部署**：
```bash
# 标准位置
$LOCAL_PATH/.qnx/license/
# 构建位置
$LOCALQUA_BSP_PATH/apps/qnx_ap/qnx_bins/license/
```

#### **非安全启动修复逻辑**：
```bash
# 备份原始文件
cp "$rawprogram_file" "$rawprogram_file.backup"

# 注释xbl文件引用
sed -i 's/<program.*filename="xbl\.elf".*\/>/<\!-- &  (commented out for non-secure build) -->/' "$rawprogram_file"
sed -i 's/<program.*filename="xbl_config\.elf".*\/>/<\!-- &  (commented out for non-secure build) -->/' "$rawprogram_file"

# 构建完成后恢复
mv "$backup_file" "$original_file"
```

### ⏱️ **构建时序分析**

#### **阶段1: 构建初始化** (0-5分钟)
- Jenkins触发和参数解析
- 进程池资源分配
- XML配置读取
- SVN认证设置

#### **阶段2: 源码准备** (5-15分钟)
- QNX基线代码解压
- SDP解压 (如需要)
- QNX许可证注册
- SVN源码导出 (并行多模块)

#### **阶段3: 并行构建** (15-120分钟)
- **Android线程**: 环境设置 → lunch → make → 自定义镜像
- **QNX线程**: 环境设置 → make clean → make all → AMSS构建

#### **阶段4: Meta构建** (120-140分钟)
- 文件依赖检查
- rawprogram.xml动态修改
- AMSS Meta构建执行
- CDT生成

#### **阶段5: 后处理** (140-160分钟)
- OTA包构建 (可选)
- 最终打包
- UFS构建 (可选)
- JFROG上传 (可选)
- 清理和通知

### 📈 **性能指标**

- **总构建时间**: 通常2-3小时
- **并发处理能力**: 最多4个项目同时构建
- **队列处理能力**: 最多9个任务等待
- **资源利用率**: 自动负载均衡和资源回收
- **错误恢复**: 完善的错误处理和日志记录机制

### 🛠️ **故障处理机制**

#### **构建失败处理**：
1. **错误分类**: MPU/BSP/AMSS/Android错误分类
2. **日志收集**: CollectBuildError.py自动收集
3. **邮件通知**: AutoSendEmail.py发送详细报告
4. **资源清理**: 自动清理临时文件和进程

#### **进程异常处理**：
1. **健康检查**: 定期检查进程状态
2. **超时处理**: 自动终止超时进程
3. **资源回收**: 强制清理异常进程资源
4. **池状态恢复**: 重置池状态为可用

这个系统设计精良，具有高度的可扩展性、稳定性和可维护性，能够高效处理复杂的多模块并行构建任务。
