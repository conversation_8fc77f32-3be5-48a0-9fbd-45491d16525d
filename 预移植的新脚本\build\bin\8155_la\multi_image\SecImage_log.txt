Logging to /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/bin/8155_la/multi_image/SecImage_log.txt


     SecImage v5.39 launched as: "/mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/./common/sectools/sectools.py secimage -p sm8150 -m /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/ --m_gen --m_sign --m_validate -a -o ./bin/8155_la/multi_image/"

Config path is set to: /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/sectools/config/sm8150/sm8150_secimage.xml
WARNING: Meta lib did not return image path for sign id: "xbl"
WARNING: Meta lib did not return image path for sign id: "xbl_config"
WARNING: OEM ID is set to 0 for sign_id "abl"
WARNING: OEM ID is set to 0 for sign_id "multi_image"
WARNING: Meta lib did not return image path for sign id: "prog_firehose_ddr"
WARNING: Meta lib did not return image path for sign id: "prog_firehouse_lite"
WARNING: OEM ID is set to 0 for sign_id "modem"
WARNING: OEM ID is set to 0 for sign_id "hyp"
WARNING: OEM ID is set to 0 for sign_id "devcfg"
WARNING: OEM ID is set to 0 for sign_id "aop"
WARNING: Meta lib did not return image path for sign id: "adsp"
WARNING: File not found in meta build: slpi
WARNING: File not found in meta build: wlan
WARNING: OEM ID is set to 0 for sign_id "venus"
WARNING: Meta lib did not return image path for sign id: "ipa_fw"
WARNING: OEM ID is set to 0 for sign_id "sampleapp32"
WARNING: OEM ID is set to 0 for sign_id "sampleapp64"
WARNING: File not found in meta build: securemm
WARNING: File not found in meta build: isdbtmm
WARNING: OEM ID is set to 0 for sign_id "widevine"
WARNING: File not found in meta build: cppf
WARNING: File not found in meta build: playready
WARNING: OEM ID is set to 0 for sign_id "keymaster"
WARNING: File not found in meta build: km41
WARNING: OEM ID is set to 0 for sign_id "hdcp1"
WARNING: OEM ID is set to 0 for sign_id "hdcp2p2"
WARNING: OEM ID is set to 0 for sign_id "hdcpsrm"
WARNING: Meta lib did not return image path for sign id: "gfx_microcode"
WARNING: File not found in meta build: winsecapp
WARNING: OEM ID is set to 0 for sign_id "uefisecapp"
WARNING: OEM ID is set to 0 for sign_id "storsec"
WARNING: OEM ID is set to 0 for sign_id "npu"
WARNING: File not found in meta build: iris
WARNING: File not found in meta build: haventkn
WARNING: File not found in meta build: dhsecapp
WARNING: File not found in meta build: fingerpr
WARNING: File not found in meta build: voiceprint
WARNING: OEM ID is set to 0 for sign_id "gptest"
WARNING: File not found in meta build: rtic
WARNING: File not found in meta build: rtic_tst
WARNING: Meta lib did not return image path for sign id: "cdsp"
WARNING: File not found in meta build: imagefv
WARNING: File not found in meta build: sp_mcp_v1
WARNING: File not found in meta build: sp_mcp_v2
WARNING: File not found in meta build: spss1t
WARNING: File not found in meta build: spss2t
WARNING: Meta lib did not return image path for sign id: "ipa_uc"
WARNING: File not found in meta build: soter64
------------------------------------------------------
Processing 1/22: /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/apps/qnx_ap/target/hypervisor/host/abl-image/signed/default/abl/abl_fastboot.elf

OEM signed image with RSAPSS
ERROR: Following validations failed for the image:
       1. Following signing attributes do not match: 
          | Attribute | Image | Config |
          |-----------|-------|--------|
          |  soc_vers | 0x6007|Missing |
          |           | 0x6006|Missing |
          
Image /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/apps/qnx_ap/target/hypervisor/host/abl-image/signed/default/abl/abl_fastboot.elf is not encrypted

Base Properties: 
| Integrity Check             | True  |
| Signed                      | True  |
| Encrypted                   | False |
| Size of signature           | 256   |
| Size of one cert            | 2048  |
| Num of certs in cert chain  | 3     |
| Number of root certs        | 1     |
| Maximum number of root certs| None  |
| Cert chain size             | 6144  |

ELF Properties: 
Header: 
| Magic                      | ELF                           |
| Class                      | ELF32                          |
| Data                       | 2's complement, little endian  |
| Version                    | 1 (Current)                    |
| OS/ABI                     | No extensions or unspecified   |
| ABI Version                | 0                              |
| Type                       | EXEC (Executable file)         |
| Machine                    | Advanced RISC Machines ARM     |
| Version                    | 0x1                            |
| Entry address              | 0x9fa00000                     |
| Program headers offset     | 0x00000034                     |
| Section headers offset     | 0x00000000                     |
| Flags                      | 0x00000000                     |
| ELF header size            | 52                             |
| Program headers size       | 32                             |
| Number of program headers  | 1                              |
| Section headers size       | 0                              |
| Number of section headers  | 0                              |
| String table section index | 0                              |

Program Headers: 
| Num | Type | Offset | VirtAddr | PhysAddr | FileSize | MemSize | Flags | Align |
|-----|------|--------|----------|----------|----------|---------|-------|-------|
|  1  | LOAD | 0x3000 |0x9fa00000|0x9fa00000| 0x22000  | 0x22000 |  RWE  | 0x1000|

Hash Segment Properties: 
| Header Size     | 168B  |
| Hash Algorithm  | sha384|

Header: 
| cert_chain_ptr              | 0xffffffff  |
| cert_chain_size             | 0x00001800  |
| cert_chain_size_qti         | 0x00000000  |
| code_size                   | 0x00000090  |
| image_id                    | 0x00000000  |
| image_size                  | 0x00001990  |
| metadata_major_version      | 0x00000000  |
| metadata_major_version_qti  | 0x00000000  |
| metadata_minor_version      | 0x00000000  |
| metadata_minor_version_qti  | 0x00000000  |
| metadata_size               | 0x00000078  |
| metadata_size_qti           | 0x00000000  |
| sig_ptr                     | 0xffffffff  |
| sig_size                    | 0x00000100  |
| sig_size_qti                | 0x00000000  |
| version                     | 0x00000006  |
Metadata:
| anti_rollback_version        | 0x00000000                        |
| app_id                       | 0x00000000                        |
| debug                        | 0x00000000                        |
| hw_id                        | 0x00000000                        |
| in_use_soc_hw_version        | 0x00000001                        |
| model_id                     | 0x00000000                        |
| mrc_index                    | 0x00000000                        |
| multi_serial_numbers         | 0x00000000                        |
| oem_id                       | 0x00000000                        |
| oem_id_independent           | 0x00000000                        |
| root_revoke_activate_enable  | 0x00000000                        |
| rot_en                       | 0x00000000                        |
| soc_vers                     | 0x00006007 0x00006003 0x00006006  |
| sw_id                        | 0x0000001c                        |
| uie_key_switch_enable        | 0x00000000                        |
| use_serial_number_in_signing | 0x00000000                        |


------------------------------------------------------

------------------------------------------------------
Processing 2/22: /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/mpss_8155/modem_proc/build/ms/bin/sm8150.gennmgw.prod/qdsp6sw.mbn

OEM signed image with RSAPSS
ERROR: Following validations failed for the image:
       1. Following signing attributes do not match: 
          | Attribute | Image | Config |
          |-----------|-------|--------|
          |   debug   |  0x1  |  0x0   |
          
Image /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/mpss_8155/modem_proc/build/ms/bin/sm8150.gennmgw.prod/qdsp6sw.mbn is not encrypted

Base Properties: 
| Integrity Check             | True  |
| Signed                      | True  |
| Encrypted                   | False |
| Size of signature           | 256   |
| Size of one cert            | 2048  |
| Num of certs in cert chain  | 3     |
| Number of root certs        | 1     |
| Maximum number of root certs| None  |
| Cert chain size             | 6144  |

ELF Properties: 
Header: 
| Magic                      | ELF                           |
| Class                      | ELF32                          |
| Data                       | 2's complement, little endian  |
| Version                    | 1 (Current)                    |
| OS/ABI                     | No extensions or unspecified   |
| ABI Version                | 0                              |
| Type                       | EXEC (Executable file)         |
| Machine                    | 164                            |
| Version                    | 0x1                            |
| Entry address              | 0x8d800000                     |
| Program headers offset     | 0x00000034                     |
| Section headers offset     | 0x00000000                     |
| Flags                      | 0x00000066                     |
| ELF header size            | 52                             |
| Program headers size       | 32                             |
| Number of program headers  | 15                             |
| Section headers size       | 40                             |
| Number of section headers  | 0                              |
| String table section index | 0                              |

Program Headers: 
| Num | Type | Offset | VirtAddr | PhysAddr | FileSize | MemSize | Flags |   Align   |
|-----|------|--------|----------|----------|----------|---------|-------|-----------|
|  1  | LOAD |0x003000|0xc0800000|0x8d800000| 0x002020 | 0x003000|   RE  | 0x100000  |
|  2  | LOAD |0x006000|0xc0812000|0x8d812000| 0x039728 | 0x03a000|  RWE  | 0x1000    |
|  3  | LOAD |0x040000|0xc084c000|0x8d84c000| 0x00f770 | 0x010000|   RW  | 0x1000    |
|  4  | LOAD |0x050000|0xc085c000|0x8d85c000| 0x000ec8 | 0x001000|   R   | 0x1000    |
|  5  | LOAD |0x051000|0xc085d000|0x8d85d000| 0x0097e8 | 0x00a000|  RWE  | 0x1000    |
|  6  | LOAD |0x05b000|0xc0867000|0x8d867000| 0x01d054 | 0x01e000|  RWE  | 0x1000    |
|  7  | LOAD |0x079000|0xc0890000|0x8d890000| 0x2e8ab0 | 0x2e9000|   RE  | 0x1000    |
|  8  | LOAD |0x362000|0xc0b80000|0x8db80000| 0x000000 | 0x001000|   RW  | 0x1000    |
|  9  | LOAD |0x362000|0xc0bc0000|0x8dbc0000| 0x001010 | 0x002000|   RW  | 0x1000    |
|  10 | LOAD |0x364000|0xc0c00000|0x8dc00000| 0x149198 | 0x92e000|   R   | 0x1000    |
|  11 | LOAD |0x4ae000|0xc152e000|0x8e52e000| 0x2005a2 | 0x201000|   RW  | 0x1000    |
|  12 | LOAD |0x6af000|0xc172f000|0x8e72f000| 0x000000 | 0x506000|   RW  | 0x1000    |
|  13 | LOAD |0x6af000|0xc1c35000|0x8ec35000| 0x009d80 | 0x00a000|   RW  | 0x1000    |
|  14 | LOAD |0x6b9000|0xc1c40000|0x8ec40000| 0x006f23 | 0x007000|   R   | 0x1000    |
|  15 | LOAD |0x6c0000|0xc1c48000|0x8ec48000| 0x000000 | 0xbb8000|   R   | 0x1000    |

Hash Segment Properties: 
| Header Size     | 168B  |
| Hash Algorithm  | sha384|

Header: 
| cert_chain_ptr              | 0x8f800248  |
| cert_chain_size             | 0x00001800  |
| cert_chain_size_qti         | 0x00000000  |
| code_size                   | 0x00000330  |
| image_id                    | 0x0000000c  |
| image_size                  | 0x00001c30  |
| metadata_major_version      | 0x00000000  |
| metadata_major_version_qti  | 0x00000000  |
| metadata_minor_version      | 0x00000000  |
| metadata_minor_version_qti  | 0x00000000  |
| metadata_size               | 0x00000078  |
| metadata_size_qti           | 0x00000000  |
| sig_ptr                     | 0x8f800248  |
| sig_size                    | 0x00000100  |
| sig_size_qti                | 0x00000000  |
| version                     | 0x00000006  |
Metadata:
| anti_rollback_version        | 0x00000000  |
| app_id                       | 0x00000000  |
| debug                        | 0x00000001  |
| hw_id                        | 0x00000000  |
| in_use_soc_hw_version        | 0x00000001  |
| model_id                     | 0x00000000  |
| mrc_index                    | 0x00000000  |
| multi_serial_numbers         | 0x00000000  |
| oem_id                       | 0x00000000  |
| oem_id_independent           | 0x00000000  |
| root_revoke_activate_enable  | 0x00000000  |
| rot_en                       | 0x00000000  |
| soc_vers                     | 0x00006003  |
| sw_id                        | 0x00000002  |
| uie_key_switch_enable        | 0x00000000  |
| use_serial_number_in_signing | 0x00000000  |


------------------------------------------------------

------------------------------------------------------
Processing 3/22: /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/tz_8155/trustzone_images/build/ms/bin/YAQAANAA/tz.mbn

OEM signed image with RSAPSS
QTI PROD signed image with RSAPSS
ERROR: Following validations failed for the image:
       1. Following signing attributes do not match: 
          | Attribute | Image | Config |
          |-----------|-------|--------|
          |   debug   |  0x1  |  0x0   |
          
Image /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/tz_8155/trustzone_images/build/ms/bin/YAQAANAA/tz.mbn is not encrypted

Base Properties: 
| Integrity Check             | True  |
| Signed                      | True  |
| Encrypted                   | False |
| Size of signature           | 256   |
| Size of one cert            | 2048  |
| Num of certs in cert chain  | 3     |
| Number of root certs        | 1     |
| Maximum number of root certs| 4     |
| Cert chain size             | 12288 |

ELF Properties: 
Header: 
| Magic                      | ELF                           |
| Class                      | ELF64                          |
| Data                       | 2's complement, little endian  |
| Version                    | 1 (Current)                    |
| OS/ABI                     | GNU                            |
| ABI Version                | 0                              |
| Type                       | EXEC (Executable file)         |
| Machine                    | 183                            |
| Version                    | 0x1                            |
| Entry address              | 0x14680000                     |
| Program headers offset     | 0x00000040                     |
| Section headers offset     | 0x00000000                     |
| Flags                      | 0x00000000                     |
| ELF header size            | 64                             |
| Program headers size       | 56                             |
| Number of program headers  | 21                             |
| Section headers size       | 0                              |
| Number of section headers  | 0                              |
| String table section index | 0                              |

Program Headers: 
| Num | Type | Offset | VirtAddr | PhysAddr | FileSize | MemSize | Flags | Align |
|-----|------|--------|----------|----------|----------|---------|-------|-------|
|  1  | LOAD |0x007000|0x14680000|0x14680000| 0x0b250  | 0x0b250 |  RWE  | 0x1000|
|  2  | LOAD |0x013000|0x1468c000|0x1468c000| 0x02248  | 0x02248 |   RW  | 0x1000|
|  3  | LOAD |0x016000|0x14690000|0x14690000| 0x017c8  | 0x017c8 |  RWE  | 0x1000|
|  4  | LOAD |0x018000|0x14692000|0x14692000| 0x01790  | 0x01790 |   RW  | 0x1000|
|  5  | LOAD |0x01a000|0x1c018000|0x1c018000| 0x07d00  | 0x07d00 |   RE  | 0x1000|
|  6  | LOAD |0x022000|0x1c020000|0x1c020000| 0x0c988  | 0x0c988 |   RW  | 0x1000|
|  7  | LOAD |0x02f000|0x1c02d000|0x1c02d000| 0x0a000  | 0x0a000 |   RW  | 0x1000|
|  8  | LOAD |0x039000|0x1c03a000|0x1c03a000| 0xdcdaa  | 0xdcdaa |  RWE  | 0x1000|
|  9  | LOAD |0x116000|0x1c117000|0x1c117000| 0x215e0  | 0x215e0 |   RW  | 0x1000|
|  10 | LOAD |0x138000|0x1c139000|0x1c139000| 0xc3380  | 0xc3380 |   RW  | 0x1000|
|  11 | LOAD |0x1fc000|0x1c1fd000|0x1c1fd000| 0x00000  | 0x00000 |   R   | 0x1000|
|  12 | LOAD |0x1fc000|0x1c281000|0x1c281000| 0x11548  | 0x5d000 |   RW  | 0x1000|
|  13 | LOAD |0x20e000|0x1c2de000|0x1c2de000| 0x10000  | 0x10000 |   RW  | 0x1000|
|  14 | LOAD |0x21e000|0x1c2ee000|0x1c2ee000| 0x10000  | 0x10000 |   RW  | 0x1000|
|  15 | LOAD |0x22e000|0x1c300000|0x1c300000| 0x000c5  | 0x000c5 |   R   | 0x1000|
|  16 | LOAD |0x22f000|0x1c301000|0x1c301000| 0x55550  | 0x55550 |   RE  | 0x4   |
|  17 | LOAD |0x285000|0x1c357000|0x1c357000| 0x00ad0  | 0x00ad0 |   RW  | 0x4   |
|  18 | LOAD |0x286000|0x1c358000|0x1c358000| 0x05b84  | 0x05b84 |   RW  | 0x4   |
|  19 | LOAD |0x28c000|0x1c35e000|0x1c35e000| 0x6e600  | 0x6e600 |   RE  | 0x1000|
|  20 | LOAD |0x2fb000|0x1c3cd000|0x1c3cd000| 0x012b8  | 0x012b8 |   RW  | 0x1000|
|  21 | LOAD |0x2fd000|0x1c3cf000|0x1c3cf000| 0x07e3e  | 0x07e3e |   RW  | 0x1000|

Hash Segment Properties: 
| Header Size     | 288B  |
| Hash Algorithm  | sha384|

Header: 
| cert_chain_ptr              | 0xffffffff  |
| cert_chain_size             | 0x00003000  |
| cert_chain_size_qti         | 0x00001800  |
| code_size                   | 0x00000450  |
| image_id                    | 0x00000019  |
| image_size                  | 0x00004f50  |
| metadata_major_version      | 0x00000000  |
| metadata_major_version_qti  | 0x00000000  |
| metadata_minor_version      | 0x00000000  |
| metadata_minor_version_qti  | 0x00000000  |
| metadata_size               | 0x00000078  |
| metadata_size_qti           | 0x00000078  |
| sig_ptr                     | 0xffffffff  |
| sig_size                    | 0x00000100  |
| sig_size_qti                | 0x00000100  |
| version                     | 0x00000006  |
Metadata:
| anti_rollback_version        | 0x00000000  |
| app_id                       | 0x00000000  |
| debug                        | 0x00000001  |
| hw_id                        | 0x00000000  |
| in_use_soc_hw_version        | 0x00000001  |
| model_id                     | 0x00000000  |
| mrc_index                    | 0x00000000  |
| multi_serial_numbers         | 0x00000000  |
| oem_id                       | 0x00000001  |
| oem_id_independent           | 0x00000001  |
| root_revoke_activate_enable  | 0x00000000  |
| rot_en                       | 0x00000000  |
| soc_vers                     | 0x00006003  |
| sw_id                        | 0x00000007  |
| uie_key_switch_enable        | 0x00000000  |
| use_serial_number_in_signing | 0x00000000  |
Metadata QTI:
| anti_rollback_version        | 0x00000000  |
| app_id                       | 0x00000000  |
| debug                        | 0x00000001  |
| hw_id                        | 0x00000000  |
| in_use_soc_hw_version        | 0x00000001  |
| model_id                     | 0x00000000  |
| mrc_index                    | 0x00000000  |
| multi_serial_numbers         | 0x00000000  |
| oem_id                       | 0x00000001  |
| oem_id_independent           | 0x00000001  |
| root_revoke_activate_enable  | 0x00000000  |
| rot_en                       | 0x00000000  |
| soc_vers                     | 0x00006003  |
| sw_id                        | 0x00000007  |
| uie_key_switch_enable        | 0x00000000  |
| use_serial_number_in_signing | 0x00000000  |


------------------------------------------------------

------------------------------------------------------
Processing 4/22: /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/apps/qnx_ap/target/hypervisor/host/out_8155/signed/default/qhee/mifs_hyp_la.img

OEM signed image with RSAPSS
Image /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/apps/qnx_ap/target/hypervisor/host/out_8155/signed/default/qhee/mifs_hyp_la.img signature is valid
Image /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/apps/qnx_ap/target/hypervisor/host/out_8155/signed/default/qhee/mifs_hyp_la.img is not encrypted

Base Properties: 
| Integrity Check             | True  |
| Signed                      | True  |
| Encrypted                   | False |
| Size of signature           | 256   |
| Size of one cert            | 2048  |
| Num of certs in cert chain  | 3     |
| Number of root certs        | 1     |
| Maximum number of root certs| None  |
| Cert chain size             | 6144  |

ELF Properties: 
Header: 
| Magic                      | ELF                           |
| Class                      | ELF64                          |
| Data                       | 2's complement, little endian  |
| Version                    | 1 (Current)                    |
| OS/ABI                     | No extensions or unspecified   |
| ABI Version                | 0                              |
| Type                       | EXEC (Executable file)         |
| Machine                    | 183                            |
| Version                    | 0x1                            |
| Entry address              | 0x85701000                     |
| Program headers offset     | 0x00000040                     |
| Section headers offset     | 0x00000000                     |
| Flags                      | 0x00000000                     |
| ELF header size            | 64                             |
| Program headers size       | 56                             |
| Number of program headers  | 1                              |
| Section headers size       | 64                             |
| Number of section headers  | 0                              |
| String table section index | 0                              |

Program Headers: 
| Num | Type | Offset | VirtAddr | PhysAddr | FileSize | MemSize | Flags | Align |
|-----|------|--------|----------|----------|----------|---------|-------|-------|
|  1  | LOAD | 0x3000 |0x85700200|0x85700200| 0x2fd16c | 0x2fd16c|  RWE  | 0x200 |

Hash Segment Properties: 
| Header Size     | 168B  |
| Hash Algorithm  | sha384|

Header: 
| cert_chain_ptr              | 0xffffffff  |
| cert_chain_size             | 0x00001800  |
| cert_chain_size_qti         | 0x00000000  |
| code_size                   | 0x00000090  |
| image_id                    | 0x00000000  |
| image_size                  | 0x00001990  |
| metadata_major_version      | 0x00000000  |
| metadata_major_version_qti  | 0x00000000  |
| metadata_minor_version      | 0x00000000  |
| metadata_minor_version_qti  | 0x00000000  |
| metadata_size               | 0x00000078  |
| metadata_size_qti           | 0x00000000  |
| sig_ptr                     | 0xffffffff  |
| sig_size                    | 0x00000100  |
| sig_size_qti                | 0x00000000  |
| version                     | 0x00000006  |
Metadata:
| anti_rollback_version        | 0x00000000  |
| app_id                       | 0x00000000  |
| debug                        | 0x00000000  |
| hw_id                        | 0x00000000  |
| in_use_soc_hw_version        | 0x00000001  |
| model_id                     | 0x00000000  |
| mrc_index                    | 0x00000000  |
| multi_serial_numbers         | 0x00000000  |
| oem_id                       | 0x00000000  |
| oem_id_independent           | 0x00000000  |
| root_revoke_activate_enable  | 0x00000000  |
| rot_en                       | 0x00000000  |
| soc_vers                     | 0x00006003  |
| sw_id                        | 0x00000015  |
| uie_key_switch_enable        | 0x00000000  |
| use_serial_number_in_signing | 0x00000000  |


------------------------------------------------------

------------------------------------------------------
Processing 5/22: /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/tz_8155/trustzone_images/build/ms/bin/YAQAANAA/devcfg_auto.mbn

OEM signed image with RSAPSS
ERROR: Following validations failed for the image:
       1. Following signing attributes do not match: 
          | Attribute | Image | Config |
          |-----------|-------|--------|
          |   debug   |  0x1  |  0x0   |
          
Image /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/tz_8155/trustzone_images/build/ms/bin/YAQAANAA/devcfg_auto.mbn is not encrypted

Base Properties: 
| Integrity Check             | True  |
| Signed                      | True  |
| Encrypted                   | False |
| Size of signature           | 256   |
| Size of one cert            | 2048  |
| Num of certs in cert chain  | 3     |
| Number of root certs        | 1     |
| Maximum number of root certs| None  |
| Cert chain size             | 6144  |

ELF Properties: 
Header: 
| Magic                      | ELF                           |
| Class                      | ELF64                          |
| Data                       | 2's complement, little endian  |
| Version                    | 1 (Current)                    |
| OS/ABI                     | No extensions or unspecified   |
| ABI Version                | 0                              |
| Type                       | EXEC (Executable file)         |
| Machine                    | 183                            |
| Version                    | 0x1                            |
| Entry address              | 0x1c00d000                     |
| Program headers offset     | 0x00000040                     |
| Section headers offset     | 0x00000000                     |
| Flags                      | 0x00000000                     |
| ELF header size            | 64                             |
| Program headers size       | 56                             |
| Number of program headers  | 2                              |
| Section headers size       | 64                             |
| Number of section headers  | 0                              |
| String table section index | 0                              |

Program Headers: 
| Num | Type | Offset | VirtAddr | PhysAddr | FileSize | MemSize | Flags | Align |
|-----|------|--------|----------|----------|----------|---------|-------|-------|
|  1  | LOAD | 0x3000 |0x1c00d000|0x1c00d000|  0x91f8  |  0x91f8 |   RW  | 0x1000|
|  2  | LOAD | 0xd000 |0x85cfd000|0x85cfd000|  0x1fc0  |  0x1fc0 |   RW  | 0x1000|

Hash Segment Properties: 
| Header Size     | 168B  |
| Hash Algorithm  | sha384|

Header: 
| cert_chain_ptr              | 0xffffffff  |
| cert_chain_size             | 0x00001800  |
| cert_chain_size_qti         | 0x00000000  |
| code_size                   | 0x000000c0  |
| image_id                    | 0x00000004  |
| image_size                  | 0x000019c0  |
| metadata_major_version      | 0x00000000  |
| metadata_major_version_qti  | 0x00000000  |
| metadata_minor_version      | 0x00000000  |
| metadata_minor_version_qti  | 0x00000000  |
| metadata_size               | 0x00000078  |
| metadata_size_qti           | 0x00000000  |
| sig_ptr                     | 0xffffffff  |
| sig_size                    | 0x00000100  |
| sig_size_qti                | 0x00000000  |
| version                     | 0x00000006  |
Metadata:
| anti_rollback_version        | 0x00000000  |
| app_id                       | 0x00000000  |
| debug                        | 0x00000001  |
| hw_id                        | 0x00000000  |
| in_use_soc_hw_version        | 0x00000001  |
| model_id                     | 0x00000000  |
| mrc_index                    | 0x00000000  |
| multi_serial_numbers         | 0x00000000  |
| oem_id                       | 0x00000000  |
| oem_id_independent           | 0x00000000  |
| root_revoke_activate_enable  | 0x00000000  |
| rot_en                       | 0x00000000  |
| soc_vers                     | 0x00006003  |
| sw_id                        | 0x00000005  |
| uie_key_switch_enable        | 0x00000000  |
| use_serial_number_in_signing | 0x00000000  |


------------------------------------------------------

------------------------------------------------------
Processing 6/22: /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/aop_8155/aop_proc/build/ms/bin/AAAAANAZO/aop.mbn

OEM signed image with RSAPSS
ERROR: Following validations failed for the image:
       1. Following signing attributes do not match: 
          | Attribute | Image | Config |
          |-----------|-------|--------|
          |   debug   |  0x1  |  0x0   |
          
Image /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/aop_8155/aop_proc/build/ms/bin/AAAAANAZO/aop.mbn is not encrypted

Base Properties: 
| Integrity Check             | True  |
| Signed                      | True  |
| Encrypted                   | False |
| Size of signature           | 256   |
| Size of one cert            | 2048  |
| Num of certs in cert chain  | 3     |
| Number of root certs        | 1     |
| Maximum number of root certs| None  |
| Cert chain size             | 6144  |

ELF Properties: 
Header: 
| Magic                      | ELF                           |
| Class                      | ELF32                          |
| Data                       | 2's complement, little endian  |
| Version                    | 1 (Current)                    |
| OS/ABI                     | GNU                            |
| ABI Version                | 0                              |
| Type                       | EXEC (Executable file)         |
| Machine                    | Advanced RISC Machines ARM     |
| Version                    | 0x1                            |
| Entry address              | 0x0b000009                     |
| Program headers offset     | 0x00000034                     |
| Section headers offset     | 0x00000000                     |
| Flags                      | 0x05000000                     |
| ELF header size            | 52                             |
| Program headers size       | 32                             |
| Number of program headers  | 4                              |
| Section headers size       | 40                             |
| Number of section headers  | 0                              |
| String table section index | 0                              |

Program Headers: 
| Num | Type | Offset | VirtAddr | PhysAddr | FileSize | MemSize | Flags | Align |
|-----|------|--------|----------|----------|----------|---------|-------|-------|
|  1  | LOAD |0x03000 |0x0b000000|0x0b000000| 0x155a4  | 0x155a4 |  RWE  | 0x4   |
|  2  | LOAD |0x185b0 |0x0b0e0000|0x0b0e0000| 0x0682c  | 0x0682c |  RWE  | 0x4   |
|  3  | LOAD |0x1ede0 |0x85f00000|0x85f00000| 0x0e968  | 0x0e968 |   RW  | 0x4   |
|  4  | LOAD |0x2d748 |0x85f10000|0x85f10000| 0x00f74  | 0x00f74 |   RW  | 0x4   |

Hash Segment Properties: 
| Header Size     | 168B  |
| Hash Algorithm  | sha384|

Header: 
| cert_chain_ptr              | 0xffffffff  |
| cert_chain_size             | 0x00001800  |
| cert_chain_size_qti         | 0x00000000  |
| code_size                   | 0x00000120  |
| image_id                    | 0x00000000  |
| image_size                  | 0x00001a20  |
| metadata_major_version      | 0x00000000  |
| metadata_major_version_qti  | 0x00000000  |
| metadata_minor_version      | 0x00000000  |
| metadata_minor_version_qti  | 0x00000000  |
| metadata_size               | 0x00000078  |
| metadata_size_qti           | 0x00000000  |
| sig_ptr                     | 0xffffffff  |
| sig_size                    | 0x00000100  |
| sig_size_qti                | 0x00000000  |
| version                     | 0x00000006  |
Metadata:
| anti_rollback_version        | 0x00000000  |
| app_id                       | 0x00000000  |
| debug                        | 0x00000001  |
| hw_id                        | 0x00000000  |
| in_use_soc_hw_version        | 0x00000001  |
| model_id                     | 0x00000000  |
| mrc_index                    | 0x00000000  |
| multi_serial_numbers         | 0x00000000  |
| oem_id                       | 0x00000000  |
| oem_id_independent           | 0x00000000  |
| root_revoke_activate_enable  | 0x00000000  |
| rot_en                       | 0x00000000  |
| soc_vers                     | 0x00006003  |
| sw_id                        | 0x00000021  |
| uie_key_switch_enable        | 0x00000000  |
| use_serial_number_in_signing | 0x00000000  |


------------------------------------------------------

------------------------------------------------------
Processing 7/22: /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/video/venus_proc/build/bsp/asic/build/PROD/mbn/reloc/signed/venus.mbn

OEM signed image with RSAPSS
ERROR: Following validations failed for the image:
       1. Following signing attributes do not match: 
          | Attribute | Image | Config |
          |-----------|-------|--------|
          |  soc_vers | 0x6006|Missing |
          |           | 0x600c|Missing |
          |           | 0x600d|Missing |
          |   debug   |  0x1  |  0x0   |
          
Image /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/video/venus_proc/build/bsp/asic/build/PROD/mbn/reloc/signed/venus.mbn is not encrypted

Base Properties: 
| Integrity Check             | True  |
| Signed                      | True  |
| Encrypted                   | False |
| Size of signature           | 256   |
| Size of one cert            | 2048  |
| Num of certs in cert chain  | 3     |
| Number of root certs        | 1     |
| Maximum number of root certs| None  |
| Cert chain size             | 6144  |

ELF Properties: 
Header: 
| Magic                      | ELF                           |
| Class                      | ELF32                          |
| Data                       | 2's complement, little endian  |
| Version                    | 1 (Current)                    |
| OS/ABI                     | No extensions or unspecified   |
| ABI Version                | 0                              |
| Type                       | EXEC (Executable file)         |
| Machine                    | Advanced RISC Machines ARM     |
| Version                    | 0x1                            |
| Entry address              | 0x0f500000                     |
| Program headers offset     | 0x00000034                     |
| Section headers offset     | 0x00000000                     |
| Flags                      | 0x05000000                     |
| ELF header size            | 52                             |
| Program headers size       | 32                             |
| Number of program headers  | 3                              |
| Section headers size       | 40                             |
| Number of section headers  | 0                              |
| String table section index | 0                              |

Program Headers: 
| Num | Type | Offset | VirtAddr | PhysAddr | FileSize | MemSize | Flags |   Align   |
|-----|------|--------|----------|----------|----------|---------|-------|-----------|
|  1  | LOAD |0x004000| 0x000000 |0xf500000 | 0x103108 | 0x103108|   RE  | 0x100000  |
|  2  | LOAD |0x108000| 0x104000 |0xf604000 | 0x009e44 | 0x3f4000|   RW  | 0x4000    |
|  3  | LOAD |0x117000| 0x4ff000 |0xf9ff000 | 0x000020 | 0x000020|   RW  | 0x4000    |

Hash Segment Properties: 
| Header Size     | 168B  |
| Hash Algorithm  | sha384|

Header: 
| cert_chain_ptr              | 0x0fa000c8  |
| cert_chain_size             | 0x00001800  |
| cert_chain_size_qti         | 0x00000000  |
| code_size                   | 0x000000f0  |
| image_id                    | 0x00000000  |
| image_size                  | 0x000019f0  |
| metadata_major_version      | 0x00000000  |
| metadata_major_version_qti  | 0x00000000  |
| metadata_minor_version      | 0x00000000  |
| metadata_minor_version_qti  | 0x00000000  |
| metadata_size               | 0x00000078  |
| metadata_size_qti           | 0x00000000  |
| sig_ptr                     | 0x0fa000c8  |
| sig_size                    | 0x00000100  |
| sig_size_qti                | 0x00000000  |
| version                     | 0x00000006  |
Metadata:
| anti_rollback_version        | 0x00000000                                   |
| app_id                       | 0x00000000                                   |
| debug                        | 0x00000001                                   |
| hw_id                        | 0x00000000                                   |
| in_use_soc_hw_version        | 0x00000001                                   |
| model_id                     | 0x00000000                                   |
| mrc_index                    | 0x00000000                                   |
| multi_serial_numbers         | 0x00000000                                   |
| oem_id                       | 0x00000000                                   |
| oem_id_independent           | 0x00000000                                   |
| root_revoke_activate_enable  | 0x00000000                                   |
| rot_en                       | 0x00000000                                   |
| soc_vers                     | 0x00006003 0x00006006 0x0000600c 0x0000600d  |
| sw_id                        | 0x0000000e                                   |
| uie_key_switch_enable        | 0x00000000                                   |
| use_serial_number_in_signing | 0x00000000                                   |


------------------------------------------------------

WARNING: Skipped adding Multi-Image Sign & Integrity entry for Trusted Application
------------------------------------------------------
Processing 8/22: /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/tz_8155/trustzone_images/build/ms/bin/YAQAANAA/smplap32.mbn

OEM signed image with RSAPSS
ERROR: Following validations failed for the image:
       1. Following signing attributes do not match: 
          | Attribute |   Image    | Config |
          |-----------|------------|--------|
          |   app_id  | 0xc68c8fa7 | 0x111  |
          |   debug   |    0x1     |  0x0   |
          
Image /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/tz_8155/trustzone_images/build/ms/bin/YAQAANAA/smplap32.mbn is not encrypted

Base Properties: 
| Integrity Check             | True  |
| Signed                      | True  |
| Encrypted                   | False |
| Size of signature           | 256   |
| Size of one cert            | 2048  |
| Num of certs in cert chain  | 3     |
| Number of root certs        | 1     |
| Maximum number of root certs| None  |
| Cert chain size             | 6144  |

ELF Properties: 
Header: 
| Magic                      | ELF                           |
| Class                      | ELF32                          |
| Data                       | 2's complement, little endian  |
| Version                    | 1 (Current)                    |
| OS/ABI                     | No extensions or unspecified   |
| ABI Version                | 0                              |
| Type                       | DYN (Shared object file)       |
| Machine                    | Advanced RISC Machines ARM     |
| Version                    | 0x1                            |
| Entry address              | 0x00000000                     |
| Program headers offset     | 0x00000034                     |
| Section headers offset     | 0x00000000                     |
| Flags                      | 0x05000000                     |
| ELF header size            | 52                             |
| Program headers size       | 32                             |
| Number of program headers  | 6                              |
| Section headers size       | 40                             |
| Number of section headers  | 0                              |
| String table section index | 0                              |

Program Headers: 
| Num |   Type   | Offset | VirtAddr | PhysAddr | FileSize | MemSize | Flags | Align |
|-----|----------|--------|----------|----------|----------|---------|-------|-------|
|  1  | LOAD     |0x003000| 0x000000 | 0x000000 | 0x0424d4 | 0x0424d4|   RE  | 0x4   |
|  2  | LOAD     |0x0454d4| 0x043000 | 0x043000 | 0x0000c2 | 0x0000c2|   RW  | 0x4   |
|  3  | LOAD     |0x045600| 0x044000 | 0x044000 | 0x154388 | 0x154388|   RW  | 0x4   |
|  4  | LOAD     |0x199988| 0x199000 | 0x199000 | 0x000514 | 0x000514|   RW  | 0x4   |
|  5  | DYNAMIC  |0x199e9c| 0x19a000 | 0x19a000 | 0x000098 | 0x000098|   RW  | 0x4   |
|  6  | LOAD     |0x199e9c| 0x19a000 | 0x19a000 | 0x009495 | 0x009495|   RW  | 0x4   |

Hash Segment Properties: 
| Header Size     | 168B  |
| Hash Algorithm  | sha384|

Header: 
| cert_chain_ptr              | 0x0019b128  |
| cert_chain_size             | 0x00001800  |
| cert_chain_size_qti         | 0x00000000  |
| code_size                   | 0x00000180  |
| image_id                    | 0x00000004  |
| image_size                  | 0x00001a80  |
| metadata_major_version      | 0x00000000  |
| metadata_major_version_qti  | 0x00000000  |
| metadata_minor_version      | 0x00000000  |
| metadata_minor_version_qti  | 0x00000000  |
| metadata_size               | 0x00000078  |
| metadata_size_qti           | 0x00000000  |
| sig_ptr                     | 0x0019b128  |
| sig_size                    | 0x00000100  |
| sig_size_qti                | 0x00000000  |
| version                     | 0x00000006  |
Metadata:
| anti_rollback_version        | 0x00000000  |
| app_id                       | 0xc68c8fa7  |
| debug                        | 0x00000001  |
| hw_id                        | 0x00000000  |
| in_use_soc_hw_version        | 0x00000001  |
| model_id                     | 0x00000000  |
| mrc_index                    | 0x00000000  |
| multi_serial_numbers         | 0x00000000  |
| oem_id                       | 0x00000000  |
| oem_id_independent           | 0x00000000  |
| root_revoke_activate_enable  | 0x00000000  |
| rot_en                       | 0x00000000  |
| soc_vers                     | 0x00006003  |
| sw_id                        | 0x0000000c  |
| uie_key_switch_enable        | 0x00000000  |
| use_serial_number_in_signing | 0x00000000  |


------------------------------------------------------

WARNING: Skipped adding Multi-Image Sign & Integrity entry for Trusted Application
------------------------------------------------------
Processing 9/22: /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/tz_8155/trustzone_images/build/ms/bin/YAQAANAA/smplap64.mbn

OEM signed image with RSAPSS
ERROR: Following validations failed for the image:
       1. Following signing attributes do not match: 
          | Attribute |   Image    | Config |
          |-----------|------------|--------|
          |   app_id  | 0x24c18fd4 | 0x112  |
          |   debug   |    0x1     |  0x0   |
          
Image /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/tz_8155/trustzone_images/build/ms/bin/YAQAANAA/smplap64.mbn is not encrypted

Base Properties: 
| Integrity Check             | True  |
| Signed                      | True  |
| Encrypted                   | False |
| Size of signature           | 256   |
| Size of one cert            | 2048  |
| Num of certs in cert chain  | 3     |
| Number of root certs        | 1     |
| Maximum number of root certs| None  |
| Cert chain size             | 6144  |

ELF Properties: 
Header: 
| Magic                      | ELF                           |
| Class                      | ELF64                          |
| Data                       | 2's complement, little endian  |
| Version                    | 1 (Current)                    |
| OS/ABI                     | No extensions or unspecified   |
| ABI Version                | 0                              |
| Type                       | DYN (Shared object file)       |
| Machine                    | 183                            |
| Version                    | 0x1                            |
| Entry address              | 0x00000000                     |
| Program headers offset     | 0x00000040                     |
| Section headers offset     | 0x00000000                     |
| Flags                      | 0x00000000                     |
| ELF header size            | 64                             |
| Program headers size       | 56                             |
| Number of program headers  | 6                              |
| Section headers size       | 64                             |
| Number of section headers  | 0                              |
| String table section index | 0                              |

Program Headers: 
| Num |   Type   | Offset | VirtAddr | PhysAddr | FileSize | MemSize | Flags | Align |
|-----|----------|--------|----------|----------|----------|---------|-------|-------|
|  1  | LOAD     |0x003000| 0x000000 | 0x000000 | 0x04e7f5 | 0x04e7f5|   RE  | 0x1000|
|  2  | LOAD     |0x052000| 0x04f000 | 0x04f000 | 0x0000ec | 0x0000ec|   RW  | 0x1000|
|  3  | LOAD     |0x053000| 0x050000 | 0x050000 | 0x1556f0 | 0x1556f0|   RW  | 0x1000|
|  4  | LOAD     |0x1a9000| 0x1a6000 | 0x1a6000 | 0x000a28 | 0x000a28|   RW  | 0x1000|
|  5  | DYNAMIC  |0x1aa000| 0x1a7000 | 0x1a7000 | 0x000130 | 0x000130|   RW  | 0x1000|
|  6  | LOAD     |0x1aa000| 0x1a7000 | 0x1a7000 | 0x00eac3 | 0x00eac3|   RW  | 0x1000|

Hash Segment Properties: 
| Header Size     | 168B  |
| Hash Algorithm  | sha384|

Header: 
| cert_chain_ptr              | 0xffffffff  |
| cert_chain_size             | 0x00001800  |
| cert_chain_size_qti         | 0x00000000  |
| code_size                   | 0x00000180  |
| image_id                    | 0x00000004  |
| image_size                  | 0x00001a80  |
| metadata_major_version      | 0x00000000  |
| metadata_major_version_qti  | 0x00000000  |
| metadata_minor_version      | 0x00000000  |
| metadata_minor_version_qti  | 0x00000000  |
| metadata_size               | 0x00000078  |
| metadata_size_qti           | 0x00000000  |
| sig_ptr                     | 0xffffffff  |
| sig_size                    | 0x00000100  |
| sig_size_qti                | 0x00000000  |
| version                     | 0x00000006  |
Metadata:
| anti_rollback_version        | 0x00000000  |
| app_id                       | 0x24c18fd4  |
| debug                        | 0x00000001  |
| hw_id                        | 0x00000000  |
| in_use_soc_hw_version        | 0x00000001  |
| model_id                     | 0x00000000  |
| mrc_index                    | 0x00000000  |
| multi_serial_numbers         | 0x00000000  |
| oem_id                       | 0x00000000  |
| oem_id_independent           | 0x00000000  |
| root_revoke_activate_enable  | 0x00000000  |
| rot_en                       | 0x00000000  |
| soc_vers                     | 0x00006003  |
| sw_id                        | 0x0000000c  |
| uie_key_switch_enable        | 0x00000000  |
| use_serial_number_in_signing | 0x00000000  |


------------------------------------------------------

WARNING: Skipped adding Multi-Image Sign & Integrity entry for Trusted Application
------------------------------------------------------
Processing 10/22: /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/tz_apps/qtee_tas/build/ms/bin/YAQAANAA/widevine.mbn

OEM signed image with RSAPSS
ERROR: Following validations failed for the image:
       1. Following signing attributes do not match: 
          | Attribute |   Image    | Config |
          |-----------|------------|--------|
          |   app_id  | 0xe8ea9532 | 0x333  |
          |   debug   |    0x1     |  0x0   |
          
Image /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/tz_apps/qtee_tas/build/ms/bin/YAQAANAA/widevine.mbn is not encrypted

Base Properties: 
| Integrity Check             | True  |
| Signed                      | True  |
| Encrypted                   | False |
| Size of signature           | 256   |
| Size of one cert            | 2048  |
| Num of certs in cert chain  | 3     |
| Number of root certs        | 1     |
| Maximum number of root certs| None  |
| Cert chain size             | 6144  |

ELF Properties: 
Header: 
| Magic                      | ELF                           |
| Class                      | ELF64                          |
| Data                       | 2's complement, little endian  |
| Version                    | 1 (Current)                    |
| OS/ABI                     | No extensions or unspecified   |
| ABI Version                | 0                              |
| Type                       | DYN (Shared object file)       |
| Machine                    | 183                            |
| Version                    | 0x1                            |
| Entry address              | 0x00000000                     |
| Program headers offset     | 0x00000040                     |
| Section headers offset     | 0x00000000                     |
| Flags                      | 0x00000000                     |
| ELF header size            | 64                             |
| Program headers size       | 56                             |
| Number of program headers  | 6                              |
| Section headers size       | 64                             |
| Number of section headers  | 0                              |
| String table section index | 0                              |

Program Headers: 
| Num |   Type   | Offset | VirtAddr | PhysAddr | FileSize | MemSize | Flags | Align |
|-----|----------|--------|----------|----------|----------|---------|-------|-------|
|  1  | LOAD     |0x03000 | 0x00000  | 0x00000  | 0x20921  | 0x20921 |   RE  | 0x1000|
|  2  | LOAD     |0x24000 | 0x21000  | 0x21000  | 0x000d9  | 0x000d9 |   RW  | 0x1000|
|  3  | LOAD     |0x25000 | 0x22000  | 0x22000  | 0x0181d  | 0x0181d |   RW  | 0x1000|
|  4  | LOAD     |0x27000 | 0x24000  | 0x24000  | 0x00300  | 0x00300 |   RW  | 0x1000|
|  5  | DYNAMIC  |0x28000 | 0x25000  | 0x25000  | 0x00130  | 0x00130 |   RW  | 0x1000|
|  6  | LOAD     |0x28000 | 0x25000  | 0x25000  | 0x052e8  | 0x052e8 |   RW  | 0x1000|

Hash Segment Properties: 
| Header Size     | 168B  |
| Hash Algorithm  | sha384|

Header: 
| cert_chain_ptr              | 0xffffffff  |
| cert_chain_size             | 0x00001800  |
| cert_chain_size_qti         | 0x00000000  |
| code_size                   | 0x00000180  |
| image_id                    | 0x00000004  |
| image_size                  | 0x00001a80  |
| metadata_major_version      | 0x00000000  |
| metadata_major_version_qti  | 0x00000000  |
| metadata_minor_version      | 0x00000000  |
| metadata_minor_version_qti  | 0x00000000  |
| metadata_size               | 0x00000078  |
| metadata_size_qti           | 0x00000000  |
| sig_ptr                     | 0xffffffff  |
| sig_size                    | 0x00000100  |
| sig_size_qti                | 0x00000000  |
| version                     | 0x00000006  |
Metadata:
| anti_rollback_version        | 0x00000000  |
| app_id                       | 0xe8ea9532  |
| debug                        | 0x00000001  |
| hw_id                        | 0x00000000  |
| in_use_soc_hw_version        | 0x00000001  |
| model_id                     | 0x00000000  |
| mrc_index                    | 0x00000000  |
| multi_serial_numbers         | 0x00000000  |
| oem_id                       | 0x00000000  |
| oem_id_independent           | 0x00000000  |
| root_revoke_activate_enable  | 0x00000000  |
| rot_en                       | 0x00000000  |
| soc_vers                     | 0x00006003  |
| sw_id                        | 0x0000000c  |
| uie_key_switch_enable        | 0x00000000  |
| use_serial_number_in_signing | 0x00000000  |


------------------------------------------------------

------------------------------------------------------
Processing 11/22: /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/tz_8155/trustzone_images/build/ms/bin/YAQAANAA/cmnlib.mbn

OEM signed image with RSAPSS
QTI PROD signed image with RSAPSS
ERROR: Following validations failed for the image:
       1. Following signing attributes do not match: 
          | Attribute | Image | Config |
          |-----------|-------|--------|
          |   debug   |  0x1  |  0x0   |
          
Image /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/tz_8155/trustzone_images/build/ms/bin/YAQAANAA/cmnlib.mbn is not encrypted

Base Properties: 
| Integrity Check             | True  |
| Signed                      | True  |
| Encrypted                   | False |
| Size of signature           | 256   |
| Size of one cert            | 2048  |
| Num of certs in cert chain  | 3     |
| Number of root certs        | 1     |
| Maximum number of root certs| 4     |
| Cert chain size             | 12288 |

ELF Properties: 
Header: 
| Magic                      | ELF                           |
| Class                      | ELF32                          |
| Data                       | 2's complement, little endian  |
| Version                    | 1 (Current)                    |
| OS/ABI                     | No extensions or unspecified   |
| ABI Version                | 0                              |
| Type                       | DYN (Shared object file)       |
| Machine                    | Advanced RISC Machines ARM     |
| Version                    | 0x1                            |
| Entry address              | 0x00001bd1                     |
| Program headers offset     | 0x00000034                     |
| Section headers offset     | 0x00000000                     |
| Flags                      | 0x05000000                     |
| ELF header size            | 52                             |
| Program headers size       | 32                             |
| Number of program headers  | 4                              |
| Section headers size       | 40                             |
| Number of section headers  | 0                              |
| String table section index | 0                              |

Program Headers: 
| Num |   Type   | Offset | VirtAddr | PhysAddr | FileSize | MemSize | Flags | Align |
|-----|----------|--------|----------|----------|----------|---------|-------|-------|
|  1  | LOAD     |0x06000 | 0x00000  | 0x00000  | 0x55550  | 0x55550 |   RE  | 0x4   |
|  2  | LOAD     |0x5b550 | 0x56000  | 0x56000  | 0x00ad0  | 0x00ad0 |   RW  | 0x4   |
|  3  | DYNAMIC  |0x5c020 | 0x57000  | 0x57000  | 0x00090  | 0x00090 |   RW  | 0x4   |
|  4  | LOAD     |0x5c020 | 0x57000  | 0x57000  | 0x05b84  | 0x05b84 |   RW  | 0x4   |

Hash Segment Properties: 
| Header Size     | 288B  |
| Hash Algorithm  | sha384|

Header: 
| cert_chain_ptr              | 0x000580e8  |
| cert_chain_size             | 0x00003000  |
| cert_chain_size_qti         | 0x00001800  |
| code_size                   | 0x00000120  |
| image_id                    | 0x00000004  |
| image_size                  | 0x00004c20  |
| metadata_major_version      | 0x00000000  |
| metadata_major_version_qti  | 0x00000000  |
| metadata_minor_version      | 0x00000000  |
| metadata_minor_version_qti  | 0x00000000  |
| metadata_size               | 0x00000078  |
| metadata_size_qti           | 0x00000078  |
| sig_ptr                     | 0x000580e8  |
| sig_size                    | 0x00000100  |
| sig_size_qti                | 0x00000100  |
| version                     | 0x00000006  |
Metadata:
| anti_rollback_version        | 0x00000000  |
| app_id                       | 0x00000555  |
| debug                        | 0x00000001  |
| hw_id                        | 0x00000000  |
| in_use_soc_hw_version        | 0x00000001  |
| model_id                     | 0x00000000  |
| mrc_index                    | 0x00000000  |
| multi_serial_numbers         | 0x00000000  |
| oem_id                       | 0x00000001  |
| oem_id_independent           | 0x00000001  |
| root_revoke_activate_enable  | 0x00000000  |
| rot_en                       | 0x00000000  |
| soc_vers                     | 0x00006003  |
| sw_id                        | 0x0000001f  |
| uie_key_switch_enable        | 0x00000000  |
| use_serial_number_in_signing | 0x00000000  |
Metadata QTI:
| anti_rollback_version        | 0x00000000  |
| app_id                       | 0x00000555  |
| debug                        | 0x00000001  |
| hw_id                        | 0x00000000  |
| in_use_soc_hw_version        | 0x00000001  |
| model_id                     | 0x00000000  |
| mrc_index                    | 0x00000000  |
| multi_serial_numbers         | 0x00000000  |
| oem_id                       | 0x00000001  |
| oem_id_independent           | 0x00000001  |
| root_revoke_activate_enable  | 0x00000000  |
| rot_en                       | 0x00000000  |
| soc_vers                     | 0x00006003  |
| sw_id                        | 0x0000001f  |
| uie_key_switch_enable        | 0x00000000  |
| use_serial_number_in_signing | 0x00000000  |


------------------------------------------------------

------------------------------------------------------
Processing 12/22: /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/tz_8155/trustzone_images/build/ms/bin/YAQAANAA/cmnlib64.mbn

OEM signed image with RSAPSS
QTI PROD signed image with RSAPSS
ERROR: Following validations failed for the image:
       1. Following signing attributes do not match: 
          | Attribute | Image | Config |
          |-----------|-------|--------|
          |   debug   |  0x1  |  0x0   |
          
Image /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/tz_8155/trustzone_images/build/ms/bin/YAQAANAA/cmnlib64.mbn is not encrypted

Base Properties: 
| Integrity Check             | True  |
| Signed                      | True  |
| Encrypted                   | False |
| Size of signature           | 256   |
| Size of one cert            | 2048  |
| Num of certs in cert chain  | 3     |
| Number of root certs        | 1     |
| Maximum number of root certs| 4     |
| Cert chain size             | 12288 |

ELF Properties: 
Header: 
| Magic                      | ELF                           |
| Class                      | ELF64                          |
| Data                       | 2's complement, little endian  |
| Version                    | 1 (Current)                    |
| OS/ABI                     | No extensions or unspecified   |
| ABI Version                | 0                              |
| Type                       | DYN (Shared object file)       |
| Machine                    | 183                            |
| Version                    | 0x1                            |
| Entry address              | 0x00002814                     |
| Program headers offset     | 0x00000040                     |
| Section headers offset     | 0x00000000                     |
| Flags                      | 0x00000000                     |
| ELF header size            | 64                             |
| Program headers size       | 56                             |
| Number of program headers  | 4                              |
| Section headers size       | 64                             |
| Number of section headers  | 0                              |
| String table section index | 0                              |

Program Headers: 
| Num |   Type   | Offset | VirtAddr | PhysAddr | FileSize | MemSize | Flags | Align |
|-----|----------|--------|----------|----------|----------|---------|-------|-------|
|  1  | LOAD     |0x06000 | 0x00000  | 0x00000  | 0x6e600  | 0x6e600 |   RE  | 0x1000|
|  2  | LOAD     |0x75000 | 0x6f000  | 0x6f000  | 0x012b8  | 0x012b8 |   RW  | 0x1000|
|  3  | DYNAMIC  |0x77000 | 0x71000  | 0x71000  | 0x00120  | 0x00120 |   RW  | 0x1000|
|  4  | LOAD     |0x77000 | 0x71000  | 0x71000  | 0x07e3e  | 0x07e3e |   RW  | 0x1000|

Hash Segment Properties: 
| Header Size     | 288B  |
| Hash Algorithm  | sha384|

Header: 
| cert_chain_ptr              | 0xffffffff  |
| cert_chain_size             | 0x00003000  |
| cert_chain_size_qti         | 0x00001800  |
| code_size                   | 0x00000120  |
| image_id                    | 0x00000004  |
| image_size                  | 0x00004c20  |
| metadata_major_version      | 0x00000000  |
| metadata_major_version_qti  | 0x00000000  |
| metadata_minor_version      | 0x00000000  |
| metadata_minor_version_qti  | 0x00000000  |
| metadata_size               | 0x00000078  |
| metadata_size_qti           | 0x00000078  |
| sig_ptr                     | 0xffffffff  |
| sig_size                    | 0x00000100  |
| sig_size_qti                | 0x00000100  |
| version                     | 0x00000006  |
Metadata:
| anti_rollback_version        | 0x00000000  |
| app_id                       | 0x00000556  |
| debug                        | 0x00000001  |
| hw_id                        | 0x00000000  |
| in_use_soc_hw_version        | 0x00000001  |
| model_id                     | 0x00000000  |
| mrc_index                    | 0x00000000  |
| multi_serial_numbers         | 0x00000000  |
| oem_id                       | 0x00000001  |
| oem_id_independent           | 0x00000001  |
| root_revoke_activate_enable  | 0x00000000  |
| rot_en                       | 0x00000000  |
| soc_vers                     | 0x00006003  |
| sw_id                        | 0x0000001f  |
| uie_key_switch_enable        | 0x00000000  |
| use_serial_number_in_signing | 0x00000000  |
Metadata QTI:
| anti_rollback_version        | 0x00000000  |
| app_id                       | 0x00000556  |
| debug                        | 0x00000001  |
| hw_id                        | 0x00000000  |
| in_use_soc_hw_version        | 0x00000001  |
| model_id                     | 0x00000000  |
| mrc_index                    | 0x00000000  |
| multi_serial_numbers         | 0x00000000  |
| oem_id                       | 0x00000001  |
| oem_id_independent           | 0x00000001  |
| root_revoke_activate_enable  | 0x00000000  |
| rot_en                       | 0x00000000  |
| soc_vers                     | 0x00006003  |
| sw_id                        | 0x0000001f  |
| uie_key_switch_enable        | 0x00000000  |
| use_serial_number_in_signing | 0x00000000  |


------------------------------------------------------

WARNING: Skipped adding Multi-Image Sign & Integrity entry for Trusted Application
------------------------------------------------------
Processing 13/22: /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/tz_8155/trustzone_images/build/ms/bin/YAQAANAA/km4virt.mbn

OEM signed image with RSAPSS
ERROR: Following validations failed for the image:
       1. Following signing attributes do not match: 
          | Attribute |   Image    | Config |
          |-----------|------------|--------|
          |   app_id  | 0x38099e0b | 0x666  |
          |   debug   |    0x1     |  0x0   |
          
Image /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/tz_8155/trustzone_images/build/ms/bin/YAQAANAA/km4virt.mbn is not encrypted

Base Properties: 
| Integrity Check             | True  |
| Signed                      | True  |
| Encrypted                   | False |
| Size of signature           | 256   |
| Size of one cert            | 2048  |
| Num of certs in cert chain  | 3     |
| Number of root certs        | 1     |
| Maximum number of root certs| None  |
| Cert chain size             | 6144  |

ELF Properties: 
Header: 
| Magic                      | ELF                           |
| Class                      | ELF64                          |
| Data                       | 2's complement, little endian  |
| Version                    | 1 (Current)                    |
| OS/ABI                     | No extensions or unspecified   |
| ABI Version                | 0                              |
| Type                       | DYN (Shared object file)       |
| Machine                    | 183                            |
| Version                    | 0x1                            |
| Entry address              | 0x00000000                     |
| Program headers offset     | 0x00000040                     |
| Section headers offset     | 0x00000000                     |
| Flags                      | 0x00000000                     |
| ELF header size            | 64                             |
| Program headers size       | 56                             |
| Number of program headers  | 6                              |
| Section headers size       | 64                             |
| Number of section headers  | 0                              |
| String table section index | 0                              |

Program Headers: 
| Num |   Type   | Offset | VirtAddr | PhysAddr | FileSize | MemSize | Flags | Align |
|-----|----------|--------|----------|----------|----------|---------|-------|-------|
|  1  | LOAD     |0x03000 | 0x00000  | 0x00000  | 0x37516  | 0x37516 |   RE  | 0x1000|
|  2  | LOAD     |0x3b000 | 0x38000  | 0x38000  | 0x000b5  | 0x000b5 |   RW  | 0x1000|
|  3  | LOAD     |0x3c000 | 0x39000  | 0x39000  | 0x04a00  | 0x04a00 |   RW  | 0x1000|
|  4  | LOAD     |0x41000 | 0x3e000  | 0x3e000  | 0x003e0  | 0x003e0 |   RW  | 0x1000|
|  5  | DYNAMIC  |0x42000 | 0x3f000  | 0x3f000  | 0x00130  | 0x00130 |   RW  | 0x1000|
|  6  | LOAD     |0x42000 | 0x3f000  | 0x3f000  | 0x03547  | 0x03547 |   RW  | 0x1000|

Hash Segment Properties: 
| Header Size     | 168B  |
| Hash Algorithm  | sha384|

Header: 
| cert_chain_ptr              | 0xffffffff  |
| cert_chain_size             | 0x00001800  |
| cert_chain_size_qti         | 0x00000000  |
| code_size                   | 0x00000180  |
| image_id                    | 0x00000004  |
| image_size                  | 0x00001a80  |
| metadata_major_version      | 0x00000000  |
| metadata_major_version_qti  | 0x00000000  |
| metadata_minor_version      | 0x00000000  |
| metadata_minor_version_qti  | 0x00000000  |
| metadata_size               | 0x00000078  |
| metadata_size_qti           | 0x00000000  |
| sig_ptr                     | 0xffffffff  |
| sig_size                    | 0x00000100  |
| sig_size_qti                | 0x00000000  |
| version                     | 0x00000006  |
Metadata:
| anti_rollback_version        | 0x00000000  |
| app_id                       | 0x38099e0b  |
| debug                        | 0x00000001  |
| hw_id                        | 0x00000000  |
| in_use_soc_hw_version        | 0x00000001  |
| model_id                     | 0x00000000  |
| mrc_index                    | 0x00000000  |
| multi_serial_numbers         | 0x00000000  |
| oem_id                       | 0x00000000  |
| oem_id_independent           | 0x00000000  |
| root_revoke_activate_enable  | 0x00000000  |
| rot_en                       | 0x00000000  |
| soc_vers                     | 0x00006003  |
| sw_id                        | 0x0000000c  |
| uie_key_switch_enable        | 0x00000000  |
| use_serial_number_in_signing | 0x00000000  |


------------------------------------------------------

WARNING: Skipped adding Multi-Image Sign & Integrity entry for Trusted Application
------------------------------------------------------
Processing 14/22: /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/tz_8155/trustzone_images/build/ms/bin/YAQAANAA/hdcp1.mbn

OEM signed image with RSAPSS
ERROR: Following validations failed for the image:
       1. Following signing attributes do not match: 
          | Attribute |   Image    | Config |
          |-----------|------------|--------|
          |   app_id  | 0x99901056 | 0x776  |
          |   debug   |    0x1     |  0x0   |
          
Image /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/tz_8155/trustzone_images/build/ms/bin/YAQAANAA/hdcp1.mbn is not encrypted

Base Properties: 
| Integrity Check             | True  |
| Signed                      | True  |
| Encrypted                   | False |
| Size of signature           | 256   |
| Size of one cert            | 2048  |
| Num of certs in cert chain  | 3     |
| Number of root certs        | 1     |
| Maximum number of root certs| None  |
| Cert chain size             | 6144  |

ELF Properties: 
Header: 
| Magic                      | ELF                           |
| Class                      | ELF32                          |
| Data                       | 2's complement, little endian  |
| Version                    | 1 (Current)                    |
| OS/ABI                     | No extensions or unspecified   |
| ABI Version                | 0                              |
| Type                       | DYN (Shared object file)       |
| Machine                    | Advanced RISC Machines ARM     |
| Version                    | 0x1                            |
| Entry address              | 0x00000000                     |
| Program headers offset     | 0x00000034                     |
| Section headers offset     | 0x00000000                     |
| Flags                      | 0x05000000                     |
| ELF header size            | 52                             |
| Program headers size       | 32                             |
| Number of program headers  | 6                              |
| Section headers size       | 40                             |
| Number of section headers  | 0                              |
| String table section index | 0                              |

Program Headers: 
| Num |   Type   | Offset | VirtAddr | PhysAddr | FileSize | MemSize | Flags | Align |
|-----|----------|--------|----------|----------|----------|---------|-------|-------|
|  1  | LOAD     | 0x3000 |  0x0000  |  0x0000  |  0x55f1  |  0x55f1 |   RE  | 0x4   |
|  2  | LOAD     | 0x85f4 |  0x6000  |  0x6000  |  0x0081  |  0x0081 |   RW  | 0x4   |
|  3  | LOAD     | 0x867c |  0x7000  |  0x7000  |  0x1518  |  0x1518 |   RW  | 0x4   |
|  4  | LOAD     | 0x9b94 |  0x9000  |  0x9000  |  0x0100  |  0x0100 |   RW  | 0x4   |
|  5  | DYNAMIC  | 0x9c94 |  0xa000  |  0xa000  |  0x0098  |  0x0098 |   RW  | 0x4   |
|  6  | LOAD     | 0x9c94 |  0xa000  |  0xa000  |  0x12bd  |  0x12bd |   RW  | 0x4   |

Hash Segment Properties: 
| Header Size     | 168B  |
| Hash Algorithm  | sha384|

Header: 
| cert_chain_ptr              | 0x0000b128  |
| cert_chain_size             | 0x00001800  |
| cert_chain_size_qti         | 0x00000000  |
| code_size                   | 0x00000180  |
| image_id                    | 0x00000004  |
| image_size                  | 0x00001a80  |
| metadata_major_version      | 0x00000000  |
| metadata_major_version_qti  | 0x00000000  |
| metadata_minor_version      | 0x00000000  |
| metadata_minor_version_qti  | 0x00000000  |
| metadata_size               | 0x00000078  |
| metadata_size_qti           | 0x00000000  |
| sig_ptr                     | 0x0000b128  |
| sig_size                    | 0x00000100  |
| sig_size_qti                | 0x00000000  |
| version                     | 0x00000006  |
Metadata:
| anti_rollback_version        | 0x00000000  |
| app_id                       | 0x99901056  |
| debug                        | 0x00000001  |
| hw_id                        | 0x00000000  |
| in_use_soc_hw_version        | 0x00000001  |
| model_id                     | 0x00000000  |
| mrc_index                    | 0x00000000  |
| multi_serial_numbers         | 0x00000000  |
| oem_id                       | 0x00000000  |
| oem_id_independent           | 0x00000000  |
| root_revoke_activate_enable  | 0x00000000  |
| rot_en                       | 0x00000000  |
| soc_vers                     | 0x00006003  |
| sw_id                        | 0x0000000c  |
| uie_key_switch_enable        | 0x00000000  |
| use_serial_number_in_signing | 0x00000000  |


------------------------------------------------------

WARNING: Skipped adding Multi-Image Sign & Integrity entry for Trusted Application
------------------------------------------------------
Processing 15/22: /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/tz_8155/trustzone_images/build/ms/bin/YAQAANAA/hdcp2p2.mbn

OEM signed image with RSAPSS
ERROR: Following validations failed for the image:
       1. Following signing attributes do not match: 
          | Attribute |   Image    | Config |
          |-----------|------------|--------|
          |   app_id  | 0x5f1ef4e5 | 0x777  |
          |   debug   |    0x1     |  0x0   |
          
Image /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/tz_8155/trustzone_images/build/ms/bin/YAQAANAA/hdcp2p2.mbn is not encrypted

Base Properties: 
| Integrity Check             | True  |
| Signed                      | True  |
| Encrypted                   | False |
| Size of signature           | 256   |
| Size of one cert            | 2048  |
| Num of certs in cert chain  | 3     |
| Number of root certs        | 1     |
| Maximum number of root certs| None  |
| Cert chain size             | 6144  |

ELF Properties: 
Header: 
| Magic                      | ELF                           |
| Class                      | ELF32                          |
| Data                       | 2's complement, little endian  |
| Version                    | 1 (Current)                    |
| OS/ABI                     | No extensions or unspecified   |
| ABI Version                | 0                              |
| Type                       | DYN (Shared object file)       |
| Machine                    | Advanced RISC Machines ARM     |
| Version                    | 0x1                            |
| Entry address              | 0x00000000                     |
| Program headers offset     | 0x00000034                     |
| Section headers offset     | 0x00000000                     |
| Flags                      | 0x05000000                     |
| ELF header size            | 52                             |
| Program headers size       | 32                             |
| Number of program headers  | 6                              |
| Section headers size       | 40                             |
| Number of section headers  | 0                              |
| String table section index | 0                              |

Program Headers: 
| Num |   Type   | Offset | VirtAddr | PhysAddr | FileSize | MemSize | Flags | Align |
|-----|----------|--------|----------|----------|----------|---------|-------|-------|
|  1  | LOAD     |0x03000 | 0x00000  | 0x00000  | 0x156e5  | 0x156e5 |   RE  | 0x4   |
|  2  | LOAD     |0x186e8 | 0x16000  | 0x16000  | 0x000a0  | 0x000a0 |   RW  | 0x4   |
|  3  | LOAD     |0x18788 | 0x17000  | 0x17000  | 0x01ea8  | 0x01ea8 |   RW  | 0x4   |
|  4  | LOAD     |0x1a630 | 0x19000  | 0x19000  | 0x00130  | 0x00130 |   RW  | 0x4   |
|  5  | DYNAMIC  |0x1a760 | 0x1a000  | 0x1a000  | 0x00098  | 0x00098 |   RW  | 0x4   |
|  6  | LOAD     |0x1a760 | 0x1a000  | 0x1a000  | 0x02f81  | 0x02f81 |   RW  | 0x4   |

Hash Segment Properties: 
| Header Size     | 168B  |
| Hash Algorithm  | sha384|

Header: 
| cert_chain_ptr              | 0x0001b128  |
| cert_chain_size             | 0x00001800  |
| cert_chain_size_qti         | 0x00000000  |
| code_size                   | 0x00000180  |
| image_id                    | 0x00000004  |
| image_size                  | 0x00001a80  |
| metadata_major_version      | 0x00000000  |
| metadata_major_version_qti  | 0x00000000  |
| metadata_minor_version      | 0x00000000  |
| metadata_minor_version_qti  | 0x00000000  |
| metadata_size               | 0x00000078  |
| metadata_size_qti           | 0x00000000  |
| sig_ptr                     | 0x0001b128  |
| sig_size                    | 0x00000100  |
| sig_size_qti                | 0x00000000  |
| version                     | 0x00000006  |
Metadata:
| anti_rollback_version        | 0x00000000  |
| app_id                       | 0x5f1ef4e5  |
| debug                        | 0x00000001  |
| hw_id                        | 0x00000000  |
| in_use_soc_hw_version        | 0x00000001  |
| model_id                     | 0x00000000  |
| mrc_index                    | 0x00000000  |
| multi_serial_numbers         | 0x00000000  |
| oem_id                       | 0x00000000  |
| oem_id_independent           | 0x00000000  |
| root_revoke_activate_enable  | 0x00000000  |
| rot_en                       | 0x00000000  |
| soc_vers                     | 0x00006003  |
| sw_id                        | 0x0000000c  |
| uie_key_switch_enable        | 0x00000000  |
| use_serial_number_in_signing | 0x00000000  |


------------------------------------------------------

WARNING: Skipped adding Multi-Image Sign & Integrity entry for Trusted Application
------------------------------------------------------
Processing 16/22: /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/tz_8155/trustzone_images/build/ms/bin/YAQAANAA/hdcpsrm.mbn

OEM signed image with RSAPSS
ERROR: Following validations failed for the image:
       1. Following signing attributes do not match: 
          | Attribute |   Image    | Config |
          |-----------|------------|--------|
          |   app_id  | 0xb183d612 | 0x778  |
          |   debug   |    0x1     |  0x0   |
          
Image /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/tz_8155/trustzone_images/build/ms/bin/YAQAANAA/hdcpsrm.mbn is not encrypted

Base Properties: 
| Integrity Check             | True  |
| Signed                      | True  |
| Encrypted                   | False |
| Size of signature           | 256   |
| Size of one cert            | 2048  |
| Num of certs in cert chain  | 3     |
| Number of root certs        | 1     |
| Maximum number of root certs| None  |
| Cert chain size             | 6144  |

ELF Properties: 
Header: 
| Magic                      | ELF                           |
| Class                      | ELF32                          |
| Data                       | 2's complement, little endian  |
| Version                    | 1 (Current)                    |
| OS/ABI                     | No extensions or unspecified   |
| ABI Version                | 0                              |
| Type                       | DYN (Shared object file)       |
| Machine                    | Advanced RISC Machines ARM     |
| Version                    | 0x1                            |
| Entry address              | 0x00000000                     |
| Program headers offset     | 0x00000034                     |
| Section headers offset     | 0x00000000                     |
| Flags                      | 0x05000000                     |
| ELF header size            | 52                             |
| Program headers size       | 32                             |
| Number of program headers  | 6                              |
| Section headers size       | 40                             |
| Number of section headers  | 0                              |
| String table section index | 0                              |

Program Headers: 
| Num |   Type   | Offset | VirtAddr | PhysAddr | FileSize | MemSize | Flags | Align |
|-----|----------|--------|----------|----------|----------|---------|-------|-------|
|  1  | LOAD     | 0x3000 |  0x0000  |  0x0000  |  0x5146  |  0x5146 |   RE  | 0x4   |
|  2  | LOAD     | 0x8148 |  0x6000  |  0x6000  |  0x008a  |  0x008a |   RW  | 0x4   |
|  3  | LOAD     | 0x81d4 |  0x7000  |  0x7000  |  0x0228  |  0x0228 |   RW  | 0x4   |
|  4  | LOAD     | 0x83fc |  0x8000  |  0x8000  |  0x00e0  |  0x00e0 |   RW  | 0x4   |
|  5  | DYNAMIC  | 0x84dc |  0x9000  |  0x9000  |  0x0098  |  0x0098 |   RW  | 0x4   |
|  6  | LOAD     | 0x84dc |  0x9000  |  0x9000  |  0x0e84  |  0x0e84 |   RW  | 0x4   |

Hash Segment Properties: 
| Header Size     | 168B  |
| Hash Algorithm  | sha384|

Header: 
| cert_chain_ptr              | 0x0000a128  |
| cert_chain_size             | 0x00001800  |
| cert_chain_size_qti         | 0x00000000  |
| code_size                   | 0x00000180  |
| image_id                    | 0x00000004  |
| image_size                  | 0x00001a80  |
| metadata_major_version      | 0x00000000  |
| metadata_major_version_qti  | 0x00000000  |
| metadata_minor_version      | 0x00000000  |
| metadata_minor_version_qti  | 0x00000000  |
| metadata_size               | 0x00000078  |
| metadata_size_qti           | 0x00000000  |
| sig_ptr                     | 0x0000a128  |
| sig_size                    | 0x00000100  |
| sig_size_qti                | 0x00000000  |
| version                     | 0x00000006  |
Metadata:
| anti_rollback_version        | 0x00000000  |
| app_id                       | 0xb183d612  |
| debug                        | 0x00000001  |
| hw_id                        | 0x00000000  |
| in_use_soc_hw_version        | 0x00000001  |
| model_id                     | 0x00000000  |
| mrc_index                    | 0x00000000  |
| multi_serial_numbers         | 0x00000000  |
| oem_id                       | 0x00000000  |
| oem_id_independent           | 0x00000000  |
| root_revoke_activate_enable  | 0x00000000  |
| rot_en                       | 0x00000000  |
| soc_vers                     | 0x00006003  |
| sw_id                        | 0x0000000c  |
| uie_key_switch_enable        | 0x00000000  |
| use_serial_number_in_signing | 0x00000000  |


------------------------------------------------------

WARNING: Skipped adding Multi-Image Sign & Integrity entry for Trusted Application
------------------------------------------------------
Processing 17/22: /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/tz_8155/trustzone_images/build/ms/bin/YAQAANAA/uefi_sec.mbn

OEM signed image with RSAPSS
ERROR: Following validations failed for the image:
       1. Following signing attributes do not match: 
          | Attribute |   Image    | Config |
          |-----------|------------|--------|
          |   app_id  | 0x77b20929 | 0x311  |
          |   debug   |    0x1     |  0x0   |
          
Image /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/tz_8155/trustzone_images/build/ms/bin/YAQAANAA/uefi_sec.mbn is not encrypted

Base Properties: 
| Integrity Check             | True  |
| Signed                      | True  |
| Encrypted                   | False |
| Size of signature           | 256   |
| Size of one cert            | 2048  |
| Num of certs in cert chain  | 3     |
| Number of root certs        | 1     |
| Maximum number of root certs| None  |
| Cert chain size             | 6144  |

ELF Properties: 
Header: 
| Magic                      | ELF                           |
| Class                      | ELF32                          |
| Data                       | 2's complement, little endian  |
| Version                    | 1 (Current)                    |
| OS/ABI                     | No extensions or unspecified   |
| ABI Version                | 0                              |
| Type                       | DYN (Shared object file)       |
| Machine                    | Advanced RISC Machines ARM     |
| Version                    | 0x1                            |
| Entry address              | 0x00000000                     |
| Program headers offset     | 0x00000034                     |
| Section headers offset     | 0x00000000                     |
| Flags                      | 0x05000000                     |
| ELF header size            | 52                             |
| Program headers size       | 32                             |
| Number of program headers  | 6                              |
| Section headers size       | 40                             |
| Number of section headers  | 0                              |
| String table section index | 0                              |

Program Headers: 
| Num |   Type   | Offset | VirtAddr | PhysAddr | FileSize | MemSize | Flags | Align |
|-----|----------|--------|----------|----------|----------|---------|-------|-------|
|  1  | LOAD     |0x03000 | 0x00000  | 0x00000  |  0xd9b1  |  0xd9b1 |   RE  | 0x4   |
|  2  | LOAD     |0x109b4 | 0x0e000  | 0x0e000  |  0x0095  |  0x0095 |   RW  | 0x4   |
|  3  | LOAD     |0x10a50 | 0x0f000  | 0x0f000  |  0x7ac0  |  0x7ac0 |   RW  | 0x4   |
|  4  | LOAD     |0x18510 | 0x17000  | 0x17000  |  0x0200  |  0x0200 |   RW  | 0x4   |
|  5  | DYNAMIC  |0x18710 | 0x18000  | 0x18000  |  0x0098  |  0x0098 |   RW  | 0x4   |
|  6  | LOAD     |0x18710 | 0x18000  | 0x18000  |  0x3826  |  0x3826 |   RW  | 0x4   |

Hash Segment Properties: 
| Header Size     | 168B  |
| Hash Algorithm  | sha384|

Header: 
| cert_chain_ptr              | 0x00019128  |
| cert_chain_size             | 0x00001800  |
| cert_chain_size_qti         | 0x00000000  |
| code_size                   | 0x00000180  |
| image_id                    | 0x00000004  |
| image_size                  | 0x00001a80  |
| metadata_major_version      | 0x00000000  |
| metadata_major_version_qti  | 0x00000000  |
| metadata_minor_version      | 0x00000000  |
| metadata_minor_version_qti  | 0x00000000  |
| metadata_size               | 0x00000078  |
| metadata_size_qti           | 0x00000000  |
| sig_ptr                     | 0x00019128  |
| sig_size                    | 0x00000100  |
| sig_size_qti                | 0x00000000  |
| version                     | 0x00000006  |
Metadata:
| anti_rollback_version        | 0x00000000  |
| app_id                       | 0x77b20929  |
| debug                        | 0x00000001  |
| hw_id                        | 0x00000000  |
| in_use_soc_hw_version        | 0x00000001  |
| model_id                     | 0x00000000  |
| mrc_index                    | 0x00000000  |
| multi_serial_numbers         | 0x00000000  |
| oem_id                       | 0x00000000  |
| oem_id_independent           | 0x00000000  |
| root_revoke_activate_enable  | 0x00000000  |
| rot_en                       | 0x00000000  |
| soc_vers                     | 0x00006003  |
| sw_id                        | 0x0000000c  |
| uie_key_switch_enable        | 0x00000000  |
| use_serial_number_in_signing | 0x00000000  |


------------------------------------------------------

WARNING: Skipped adding Multi-Image Sign & Integrity entry for Trusted Application
------------------------------------------------------
Processing 18/22: /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/tz_8155/trustzone_images/build/ms/bin/YAQAANAA/storsec.mbn

OEM signed image with RSAPSS
ERROR: Following validations failed for the image:
       1. Following signing attributes do not match: 
          | Attribute |   Image    | Config |
          |-----------|------------|--------|
          |   app_id  | 0x18c120f5 | 0x116  |
          |   debug   |    0x1     |  0x0   |
          
Image /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/tz_8155/trustzone_images/build/ms/bin/YAQAANAA/storsec.mbn is not encrypted

Base Properties: 
| Integrity Check             | True  |
| Signed                      | True  |
| Encrypted                   | False |
| Size of signature           | 256   |
| Size of one cert            | 2048  |
| Num of certs in cert chain  | 3     |
| Number of root certs        | 1     |
| Maximum number of root certs| None  |
| Cert chain size             | 6144  |

ELF Properties: 
Header: 
| Magic                      | ELF                           |
| Class                      | ELF32                          |
| Data                       | 2's complement, little endian  |
| Version                    | 1 (Current)                    |
| OS/ABI                     | No extensions or unspecified   |
| ABI Version                | 0                              |
| Type                       | DYN (Shared object file)       |
| Machine                    | Advanced RISC Machines ARM     |
| Version                    | 0x1                            |
| Entry address              | 0x00000000                     |
| Program headers offset     | 0x00000034                     |
| Section headers offset     | 0x00000000                     |
| Flags                      | 0x05000000                     |
| ELF header size            | 52                             |
| Program headers size       | 32                             |
| Number of program headers  | 6                              |
| Section headers size       | 40                             |
| Number of section headers  | 0                              |
| String table section index | 0                              |

Program Headers: 
| Num |   Type   | Offset | VirtAddr | PhysAddr | FileSize | MemSize | Flags | Align |
|-----|----------|--------|----------|----------|----------|---------|-------|-------|
|  1  | LOAD     | 0x3000 |  0x0000  |  0x0000  |  0x1409  |  0x1409 |   RE  | 0x4   |
|  2  | LOAD     | 0x440c |  0x2000  |  0x2000  |  0x0095  |  0x0095 |   RW  | 0x4   |
|  3  | LOAD     | 0x44a4 |  0x3000  |  0x3000  |  0x0224  |  0x0224 |   RW  | 0x4   |
|  4  | LOAD     | 0x46c8 |  0x4000  |  0x4000  |  0x0098  |  0x0098 |   RW  | 0x4   |
|  5  | DYNAMIC  | 0x4760 |  0x5000  |  0x5000  |  0x0098  |  0x0098 |   RW  | 0x4   |
|  6  | LOAD     | 0x4760 |  0x5000  |  0x5000  |  0x083c  |  0x083c |   RW  | 0x4   |

Hash Segment Properties: 
| Header Size     | 168B  |
| Hash Algorithm  | sha384|

Header: 
| cert_chain_ptr              | 0x00006128  |
| cert_chain_size             | 0x00001800  |
| cert_chain_size_qti         | 0x00000000  |
| code_size                   | 0x00000180  |
| image_id                    | 0x00000004  |
| image_size                  | 0x00001a80  |
| metadata_major_version      | 0x00000000  |
| metadata_major_version_qti  | 0x00000000  |
| metadata_minor_version      | 0x00000000  |
| metadata_minor_version_qti  | 0x00000000  |
| metadata_size               | 0x00000078  |
| metadata_size_qti           | 0x00000000  |
| sig_ptr                     | 0x00006128  |
| sig_size                    | 0x00000100  |
| sig_size_qti                | 0x00000000  |
| version                     | 0x00000006  |
Metadata:
| anti_rollback_version        | 0x00000000  |
| app_id                       | 0x18c120f5  |
| debug                        | 0x00000001  |
| hw_id                        | 0x00000000  |
| in_use_soc_hw_version        | 0x00000001  |
| model_id                     | 0x00000000  |
| mrc_index                    | 0x00000000  |
| multi_serial_numbers         | 0x00000000  |
| oem_id                       | 0x00000000  |
| oem_id_independent           | 0x00000000  |
| root_revoke_activate_enable  | 0x00000000  |
| rot_en                       | 0x00000000  |
| soc_vers                     | 0x00006003  |
| sw_id                        | 0x0000000c  |
| uie_key_switch_enable        | 0x00000000  |
| use_serial_number_in_signing | 0x00000000  |


------------------------------------------------------

------------------------------------------------------
Processing 19/22: /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/npu/npu_proc/build/ms/signed/npu.mbn

OEM signed image with RSAPSS
ERROR: Following validations failed for the image:
       1. Following signing attributes do not match: 
          | Attribute | Image | Config |
          |-----------|-------|--------|
          |  soc_vers | 0x600c|Missing |
          |   debug   |  0x1  |  0x0   |
          
Image /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/npu/npu_proc/build/ms/signed/npu.mbn is not encrypted

Base Properties: 
| Integrity Check             | True  |
| Signed                      | True  |
| Encrypted                   | False |
| Size of signature           | 256   |
| Size of one cert            | 2048  |
| Num of certs in cert chain  | 3     |
| Number of root certs        | 1     |
| Maximum number of root certs| None  |
| Cert chain size             | 6144  |

ELF Properties: 
Header: 
| Magic                      | ELF                           |
| Class                      | ELF32                          |
| Data                       | 2's complement, little endian  |
| Version                    | 1 (Current)                    |
| OS/ABI                     | No extensions or unspecified   |
| ABI Version                | 0                              |
| Type                       | EXEC (Executable file)         |
| Machine                    | Advanced RISC Machines ARM     |
| Version                    | 0x1                            |
| Entry address              | 0x00000000                     |
| Program headers offset     | 0x00000034                     |
| Section headers offset     | 0x00000000                     |
| Flags                      | 0x05000002                     |
| ELF header size            | 52                             |
| Program headers size       | 32                             |
| Number of program headers  | 3                              |
| Section headers size       | 40                             |
| Number of section headers  | 0                              |
| String table section index | 0                              |

Program Headers: 
| Num | Type | Offset | VirtAddr | PhysAddr | FileSize | MemSize | Flags |  Align   |
|-----|------|--------|----------|----------|----------|---------|-------|----------|
|  1  | LOAD | 0x3000 | 0x00000  | 0x00000  |  0x9720  | 0x09720 |   RE  | 0x80000  |
|  2  | LOAD | 0xc720 | 0x0a000  | 0x0a000  |  0x1e74  | 0x06000 |   RW  | 0x10     |
|  3  | NULL | 0xe594 | 0x20000  | 0x20000  |  0x0000  | 0x10000 |   RW  | 0x4      |

Hash Segment Properties: 
| Header Size     | 168B  |
| Hash Algorithm  | sha384|

Header: 
| cert_chain_ptr              | 0xffffffff  |
| cert_chain_size             | 0x00001800  |
| cert_chain_size_qti         | 0x00000000  |
| code_size                   | 0x000000f0  |
| image_id                    | 0x00000000  |
| image_size                  | 0x000019f0  |
| metadata_major_version      | 0x00000000  |
| metadata_major_version_qti  | 0x00000000  |
| metadata_minor_version      | 0x00000000  |
| metadata_minor_version_qti  | 0x00000000  |
| metadata_size               | 0x00000078  |
| metadata_size_qti           | 0x00000000  |
| sig_ptr                     | 0xffffffff  |
| sig_size                    | 0x00000100  |
| sig_size_qti                | 0x00000000  |
| version                     | 0x00000006  |
Metadata:
| anti_rollback_version        | 0x00000000             |
| app_id                       | 0x00000000             |
| debug                        | 0x00000001             |
| hw_id                        | 0x00000000             |
| in_use_soc_hw_version        | 0x00000001             |
| model_id                     | 0x00000000             |
| mrc_index                    | 0x00000000             |
| multi_serial_numbers         | 0x00000000             |
| oem_id                       | 0x00000000             |
| oem_id_independent           | 0x00000000             |
| root_revoke_activate_enable  | 0x00000000             |
| rot_en                       | 0x00000000             |
| soc_vers                     | 0x0000600c 0x00006003  |
| sw_id                        | 0x00000028             |
| uie_key_switch_enable        | 0x00000000             |
| use_serial_number_in_signing | 0x00000000             |


------------------------------------------------------

WARNING: Skipped adding Multi-Image Sign & Integrity entry for Trusted Application
------------------------------------------------------
Processing 20/22: /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/tz_8155/trustzone_images/build/ms/bin/YAQAANAA/gptest.mbn

OEM signed image with RSAPSS
ERROR: Following validations failed for the image:
       1. Following signing attributes do not match: 
          | Attribute |   Image   | Config |
          |-----------|-----------|--------|
          |   app_id  | 0xffe9d03 | 0x162  |
          |   debug   |    0x1    |  0x0   |
          
Image /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/tz_8155/trustzone_images/build/ms/bin/YAQAANAA/gptest.mbn is not encrypted

Base Properties: 
| Integrity Check             | True  |
| Signed                      | True  |
| Encrypted                   | False |
| Size of signature           | 256   |
| Size of one cert            | 2048  |
| Num of certs in cert chain  | 3     |
| Number of root certs        | 1     |
| Maximum number of root certs| None  |
| Cert chain size             | 6144  |

ELF Properties: 
Header: 
| Magic                      | ELF                           |
| Class                      | ELF32                          |
| Data                       | 2's complement, little endian  |
| Version                    | 1 (Current)                    |
| OS/ABI                     | No extensions or unspecified   |
| ABI Version                | 0                              |
| Type                       | DYN (Shared object file)       |
| Machine                    | Advanced RISC Machines ARM     |
| Version                    | 0x1                            |
| Entry address              | 0x00000000                     |
| Program headers offset     | 0x00000034                     |
| Section headers offset     | 0x00000000                     |
| Flags                      | 0x05000000                     |
| ELF header size            | 52                             |
| Program headers size       | 32                             |
| Number of program headers  | 6                              |
| Section headers size       | 40                             |
| Number of section headers  | 0                              |
| String table section index | 0                              |

Program Headers: 
| Num |   Type   | Offset | VirtAddr | PhysAddr | FileSize | MemSize | Flags | Align |
|-----|----------|--------|----------|----------|----------|---------|-------|-------|
|  1  | LOAD     |0x03000 | 0x00000  | 0x00000  | 0x135d9  | 0x135d9 |   RE  | 0x4   |
|  2  | LOAD     |0x165dc | 0x14000  | 0x14000  | 0x000a0  | 0x000a0 |   RW  | 0x4   |
|  3  | LOAD     |0x16680 | 0x15000  | 0x15000  | 0x06d14  | 0x06d14 |   RW  | 0x4   |
|  4  | LOAD     |0x1d394 | 0x1c000  | 0x1c000  | 0x00428  | 0x00428 |   RW  | 0x4   |
|  5  | DYNAMIC  |0x1d7bc | 0x1d000  | 0x1d000  | 0x00098  | 0x00098 |   RW  | 0x4   |
|  6  | LOAD     |0x1d7bc | 0x1d000  | 0x1d000  | 0x069b7  | 0x069b7 |   RW  | 0x4   |

Hash Segment Properties: 
| Header Size     | 168B  |
| Hash Algorithm  | sha384|

Header: 
| cert_chain_ptr              | 0x0001e128  |
| cert_chain_size             | 0x00001800  |
| cert_chain_size_qti         | 0x00000000  |
| code_size                   | 0x00000180  |
| image_id                    | 0x00000004  |
| image_size                  | 0x00001a80  |
| metadata_major_version      | 0x00000000  |
| metadata_major_version_qti  | 0x00000000  |
| metadata_minor_version      | 0x00000000  |
| metadata_minor_version_qti  | 0x00000000  |
| metadata_size               | 0x00000078  |
| metadata_size_qti           | 0x00000000  |
| sig_ptr                     | 0x0001e128  |
| sig_size                    | 0x00000100  |
| sig_size_qti                | 0x00000000  |
| version                     | 0x00000006  |
Metadata:
| anti_rollback_version        | 0x00000000  |
| app_id                       | 0x0ffe9d03  |
| debug                        | 0x00000001  |
| hw_id                        | 0x00000000  |
| in_use_soc_hw_version        | 0x00000001  |
| model_id                     | 0x00000000  |
| mrc_index                    | 0x00000000  |
| multi_serial_numbers         | 0x00000000  |
| oem_id                       | 0x00000000  |
| oem_id_independent           | 0x00000000  |
| root_revoke_activate_enable  | 0x00000000  |
| rot_en                       | 0x00000000  |
| soc_vers                     | 0x00006003  |
| sw_id                        | 0x0000000c  |
| uie_key_switch_enable        | 0x00000000  |
| use_serial_number_in_signing | 0x00000000  |


------------------------------------------------------

------------------------------------------------------
Processing 21/22: /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/core_qupv3fw/sdm855/qupv3fw.elf

OEM signed image with RSAPSS
QTI PROD signed image with RSAPSS
ERROR: Following validations failed for the image:
       1. Following signing attributes do not match: 
          | Attribute | Image | Config |
          |-----------|-------|--------|
          |   debug   |  0x1  |  0x0   |
          
Image /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/core_qupv3fw/sdm855/qupv3fw.elf is not encrypted

Base Properties: 
| Integrity Check             | True  |
| Signed                      | True  |
| Encrypted                   | False |
| Size of signature           | 256   |
| Size of one cert            | 2048  |
| Num of certs in cert chain  | 3     |
| Number of root certs        | 1     |
| Maximum number of root certs| 4     |
| Cert chain size             | 12288 |

ELF Properties: 
Header: 
| Magic                      | ELF                           |
| Class                      | ELF32                          |
| Data                       | 2's complement, little endian  |
| Version                    | 1 (Current)                    |
| OS/ABI                     | No extensions or unspecified   |
| ABI Version                | 0                              |
| Type                       | EXEC (Executable file)         |
| Machine                    | 164                            |
| Version                    | 0x1                            |
| Entry address              | 0x00000000                     |
| Program headers offset     | 0x00000034                     |
| Section headers offset     | 0x00000000                     |
| Flags                      | 0x00000003                     |
| ELF header size            | 52                             |
| Program headers size       | 32                             |
| Number of program headers  | 13                             |
| Section headers size       | 0                              |
| Number of section headers  | 0                              |
| String table section index | 0                              |

Program Headers: 
| Num | Type | Offset | VirtAddr | PhysAddr | FileSize | MemSize | Flags | Align |
|-----|------|--------|----------|----------|----------|---------|-------|-------|
|  1  | LOAD |0x06000 |  0x1000  |  0x1000  |  0x3070  |  0x3070 |  RWE  | 0x8   |
|  2  | LOAD |0x09070 |  0x4070  |  0x4070  |  0x0622  |  0x0622 |  RWE  | 0x8   |
|  3  | LOAD |0x09698 |  0x4698  |  0x4698  |  0x1156  |  0x1156 |  RWE  | 0x8   |
|  4  | LOAD |0x0a7f0 |  0x57f0  |  0x57f0  |  0x08d2  |  0x08d2 |  RWE  | 0x8   |
|  5  | LOAD |0x0b0c8 |  0x60c8  |  0x60c8  |  0x02fa  |  0x02fa |  RWE  | 0x8   |
|  6  | LOAD |0x0b3c8 |  0x63c8  |  0x63c8  |  0x0746  |  0x0746 |  RWE  | 0x8   |
|  7  | LOAD |0x0bb10 |  0x6b10  |  0x6b10  |  0x3070  |  0x3070 |  RWE  | 0x8   |
|  8  | LOAD |0x0eb80 |  0x9b80  |  0x9b80  |  0x057b  |  0x057b |  RWE  | 0x8   |
|  9  | LOAD |0x0f100 |  0xa100  |  0xa100  |  0x0622  |  0x0622 |  RWE  | 0x8   |
|  10 | LOAD |0x0f728 |  0xa728  |  0xa728  |  0x1156  |  0x1156 |  RWE  | 0x8   |
|  11 | LOAD |0x10880 |  0xb880  |  0xb880  |  0x08d7  |  0x08d7 |  RWE  | 0x8   |
|  12 | LOAD |0x11158 |  0xc158  |  0xc158  |  0x02d2  |  0x02d2 |  RWE  | 0x8   |
|  13 | LOAD |0x11430 |  0xc430  |  0xc430  |  0x0746  |  0x0746 |  RWE  | 0x8   |

Hash Segment Properties: 
| Header Size     | 288B  |
| Hash Algorithm  | sha384|

Header: 
| cert_chain_ptr              | 0xffffffff  |
| cert_chain_size             | 0x00003000  |
| cert_chain_size_qti         | 0x00001800  |
| code_size                   | 0x000002d0  |
| image_id                    | 0x00000000  |
| image_size                  | 0x00004dd0  |
| metadata_major_version      | 0x00000000  |
| metadata_major_version_qti  | 0x00000000  |
| metadata_minor_version      | 0x00000000  |
| metadata_minor_version_qti  | 0x00000000  |
| metadata_size               | 0x00000078  |
| metadata_size_qti           | 0x00000078  |
| sig_ptr                     | 0xffffffff  |
| sig_size                    | 0x00000100  |
| sig_size_qti                | 0x00000100  |
| version                     | 0x00000006  |
Metadata:
| anti_rollback_version        | 0x00000000  |
| app_id                       | 0x00000000  |
| debug                        | 0x00000001  |
| hw_id                        | 0x000a50e1  |
| in_use_soc_hw_version        | 0x00000001  |
| model_id                     | 0x00000000  |
| mrc_index                    | 0x00000000  |
| multi_serial_numbers         | 0x00000000  |
| oem_id                       | 0x00000001  |
| oem_id_independent           | 0x00000001  |
| root_revoke_activate_enable  | 0x00000000  |
| rot_en                       | 0x00000000  |
| soc_vers                     | 0x00006003  |
| sw_id                        | 0x00000024  |
| uie_key_switch_enable        | 0x00000000  |
| use_serial_number_in_signing | 0x00000000  |
Metadata QTI:
| anti_rollback_version        | 0x00000000  |
| app_id                       | 0x00000000  |
| debug                        | 0x00000001  |
| hw_id                        | 0x000a50e1  |
| in_use_soc_hw_version        | 0x00000001  |
| model_id                     | 0x00000000  |
| mrc_index                    | 0x00000000  |
| multi_serial_numbers         | 0x00000000  |
| oem_id                       | 0x00000001  |
| oem_id_independent           | 0x00000001  |
| root_revoke_activate_enable  | 0x00000000  |
| rot_en                       | 0x00000000  |
| soc_vers                     | 0x00006003  |
| sw_id                        | 0x00000024  |
| uie_key_switch_enable        | 0x00000000  |
| use_serial_number_in_signing | 0x00000000  |


------------------------------------------------------

------------------------------------------------------
Creating & Processing 22/22: Multi-Image Sign & Integrity image

Added Multi-Image Sign & Integrity entry for image with SW_ID=0x1c
Added Multi-Image Sign & Integrity entry for image with SW_ID=0x2
Added Multi-Image Sign & Integrity entry for image with SW_ID=0x7
Added Multi-Image Sign & Integrity entry for image with SW_ID=0x15
Added Multi-Image Sign & Integrity entry for image with SW_ID=0x5
Added Multi-Image Sign & Integrity entry for image with SW_ID=0x21
Added Multi-Image Sign & Integrity entry for image with SW_ID=0xe
Added Multi-Image Sign & Integrity entry for image with SW_ID=0x1f and APP_ID=0x555
Added Multi-Image Sign & Integrity entry for image with SW_ID=0x1f and APP_ID=0x556
Added Multi-Image Sign & Integrity entry for image with SW_ID=0x28
Added Multi-Image Sign & Integrity entry for image with SW_ID=0x24
Performing OEM sign on image: Multi-Image Sign & Integrity image
Signed image is stored at /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/bin/8155_la/multi_image/sm8150/multi_image/multi_image.mbn
OEM signed image with RSAPSS
Image /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/bin/8155_la/multi_image/sm8150/multi_image/multi_image.mbn signature is valid
Image /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/bin/8155_la/multi_image/sm8150/multi_image/multi_image.mbn is not encrypted

Base Properties: 
| Integrity Check             | True  |
| Signed                      | True  |
| Encrypted                   | False |
| Size of signature           | 256   |
| Size of one cert            | 2048  |
| Num of certs in cert chain  | 3     |
| Number of root certs        | 1     |
| Maximum number of root certs| None  |
| Cert chain size             | 6144  |

ELF Properties: 
Header: 
| Magic                      | ELF                           |
| Class                      | ELF32                          |
| Data                       | 2's complement, little endian  |
| Version                    | 1 (Current)                    |
| OS/ABI                     | No extensions or unspecified   |
| ABI Version                | 0                              |
| Type                       | NONE (No file type)            |
| Machine                    | Advanced RISC Machines ARM     |
| Version                    | 0x1                            |
| Entry address              | 0x148fc000                     |
| Program headers offset     | 0x00000034                     |
| Section headers offset     | 0x00000000                     |
| Flags                      | 0x00000000                     |
| ELF header size            | 52                             |
| Program headers size       | 32                             |
| Number of program headers  | 1                              |
| Section headers size       | 0                              |
| Number of section headers  | 0                              |
| String table section index | 0                              |

Program Headers: 
| Num | Type | Offset | VirtAddr | PhysAddr | FileSize | MemSize | Flags | Align |
|-----|------|--------|----------|----------|----------|---------|-------|-------|
|  1  | LOAD | 0x3000 |0x148fc000|0x148fc000|  0x378   |  0x378  |  0x0  | 0x1000|

Hash Segment Properties: 
| Header Size     | 168B  |
| Hash Algorithm  | sha384|

Header: 
| cert_chain_ptr              | 0xffffffff  |
| cert_chain_size             | 0x00001800  |
| cert_chain_size_qti         | 0x00000000  |
| code_size                   | 0x00000090  |
| image_id                    | 0x00000000  |
| image_size                  | 0x00001990  |
| metadata_major_version      | 0x00000000  |
| metadata_major_version_qti  | 0x00000000  |
| metadata_minor_version      | 0x00000000  |
| metadata_minor_version_qti  | 0x00000000  |
| metadata_size               | 0x00000078  |
| metadata_size_qti           | 0x00000000  |
| sig_ptr                     | 0xffffffff  |
| sig_size                    | 0x00000100  |
| sig_size_qti                | 0x00000000  |
| version                     | 0x00000006  |
Metadata:
| anti_rollback_version        | 0x00000000  |
| app_id                       | 0x00000000  |
| debug                        | 0x00000000  |
| hw_id                        | 0x00000000  |
| in_use_soc_hw_version        | 0x00000001  |
| model_id                     | 0x00000000  |
| mrc_index                    | 0x00000000  |
| multi_serial_numbers         | 0x00000000  |
| oem_id                       | 0x00000000  |
| oem_id_independent           | 0x00000000  |
| root_revoke_activate_enable  | 0x00000000  |
| rot_en                       | 0x00000000  |
| soc_vers                     | 0x00006003  |
| sw_id                        | 0x00000022  |
| uie_key_switch_enable        | 0x00000000  |
| use_serial_number_in_signing | 0x00000000  |

Multi-Image Sign & Integrity Segment Properties: 
Header:
| Segment Size       | 888B  |
| Magic              | MULT  |
| Version            | 0     |
| Hash Algorithm     | sha384|
| Num Image Entries  | 11    |
Image Entries: 
| Entry 1  | SW_ID: 0x0000001c APP_ID: 0x00000000  |
| Entry 2  | SW_ID: 0x00000002 APP_ID: 0x00000000  |
| Entry 3  | SW_ID: 0x00000007 APP_ID: 0x00000000  |
| Entry 4  | SW_ID: 0x00000015 APP_ID: 0x00000000  |
| Entry 5  | SW_ID: 0x00000005 APP_ID: 0x00000000  |
| Entry 6  | SW_ID: 0x00000021 APP_ID: 0x00000000  |
| Entry 7  | SW_ID: 0x0000000e APP_ID: 0x00000000  |
| Entry 8  | SW_ID: 0x0000001f APP_ID: 0x00000555  |
| Entry 9  | SW_ID: 0x0000001f APP_ID: 0x00000556  |
| Entry 10 | SW_ID: 0x00000028 APP_ID: 0x00000000  |
| Entry 11 | SW_ID: 0x00000024 APP_ID: 0x00000000  |


------------------------------------------------------

SUMMARY:
Following actions were performed: "validate"
Following Multi-Image Sign & Integrity actions were performed: "sign, validate"
Output is saved at: /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/bin/8155_la/multi_image

| Idx |    SignId   | Parse | Integrity | Sign | Encrypt |              Validate              |
|     |             |       |           |      |         | Parse | Integrity | Sign | Encrypt |
|-----|-------------|-------|-----------|------|---------|-------|-----------|------|---------|
|  1. |     abl     |   NA  |     NA    |  NA  |    NA   |   T   |     T     |  F   |    NA   |
|  2. |    modem    |   NA  |     NA    |  NA  |    NA   |   T   |     T     |  F   |    NA   |
|  3. |      tz     |   NA  |     NA    |  NA  |    NA   |   T   |     T     |  F   |    NA   |
|  4. |     hyp     |   NA  |     NA    |  NA  |    NA   |   T   |     T     |  T   |    NA   |
|  5. |    devcfg   |   NA  |     NA    |  NA  |    NA   |   T   |     T     |  F   |    NA   |
|  6. |     aop     |   NA  |     NA    |  NA  |    NA   |   T   |     T     |  F   |    NA   |
|  7. |    venus    |   NA  |     NA    |  NA  |    NA   |   T   |     T     |  F   |    NA   |
|  8. | sampleapp32 |   NA  |     NA    |  NA  |    NA   |   T   |     T     |  F   |    NA   |
|  9. | sampleapp64 |   NA  |     NA    |  NA  |    NA   |   T   |     T     |  F   |    NA   |
| 10. |   widevine  |   NA  |     NA    |  NA  |    NA   |   T   |     T     |  F   |    NA   |
| 11. |    cmnlib   |   NA  |     NA    |  NA  |    NA   |   T   |     T     |  F   |    NA   |
| 12. |   cmnlib64  |   NA  |     NA    |  NA  |    NA   |   T   |     T     |  F   |    NA   |
| 13. |  keymaster  |   NA  |     NA    |  NA  |    NA   |   T   |     T     |  F   |    NA   |
| 14. |    hdcp1    |   NA  |     NA    |  NA  |    NA   |   T   |     T     |  F   |    NA   |
| 15. |   hdcp2p2   |   NA  |     NA    |  NA  |    NA   |   T   |     T     |  F   |    NA   |
| 16. |   hdcpsrm   |   NA  |     NA    |  NA  |    NA   |   T   |     T     |  F   |    NA   |
| 17. |  uefisecapp |   NA  |     NA    |  NA  |    NA   |   T   |     T     |  F   |    NA   |
| 18. |   storsec   |   NA  |     NA    |  NA  |    NA   |   T   |     T     |  F   |    NA   |
| 19. |     npu     |   NA  |     NA    |  NA  |    NA   |   T   |     T     |  F   |    NA   |
| 20. |    gptest   |   NA  |     NA    |  NA  |    NA   |   T   |     T     |  F   |    NA   |
| 21. |    qupv3    |   NA  |     NA    |  NA  |    NA   |   T   |     T     |  F   |    NA   |
| 22. | multi_image |   T   |     NA    |  T   |    NA   |   T   |     T     |  T   |    NA   |

