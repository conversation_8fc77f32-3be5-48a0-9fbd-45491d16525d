#! /bin/bash 

publishPath=`pwd`
packagePath=$publishPath/install/aarch64le
basePath=$publishPath/../../../..

Build_Shell_Path="http://**********/svn/Qualcomm/02_SA8155P/01_HS7001A/02_CodeLib/01_MainPath/1.2/02_Android/04_Shell/buildShell3.0/buildShell"

chmod -R 755 ./install
find  ./install/armle-v7/lib \(  -path "*/lib/*" \) -type f \( -iname "*.so" ! -iname "*.sym" \) >  liblist.txt
find  ./install/armle-v7/usr/lib/graphics/qc \(  -path "*/lib/*" \) -type f \( -iname "*.so" ! -iname "*.sym" \) >>  liblist.txt
find  ./install/aarch64le/lib \(  -path "*/lib/*" \) -type f \( -iname "*.so" ! -iname "*.sym" \) >>  liblist.txt
find  ./install/aarch64le/usr/lib/graphics/qc \(  -path "*/lib/*" \) -type f \( -iname "*.so" ! -iname "*.sym" \) >>  liblist.txt

find  ./install/armle-v7/bin \( -path "*/bin/*"  \) -type f \( -iname "*" ! -iname "*.sym" \) >  binlist.txt
find  ./install/aarch64le/bin \( -path "*/bin/*"  \) -type f \( -iname "*" ! -iname "*.sym" \) >>  binlist.txt
find  ./install/aarch64le/sbin \( -path "*/bin/*"  \) -type f \( -iname "*" ! -iname "*.sym" \) >>  binlist.txt

ls -hF ./AMSS/mpu/apps/ --show-control-chars | grep / |sed 's/\///g' > appslist.txt
ls -hF ./AMSS/mpu/mids/ --show-control-chars | grep / |sed 's/\///g' > midslist.txt
ls -hF ./AMSS/mpu/libs/ --show-control-chars | grep / |sed 's/\///g' > libslist.txt

mkdir ./install/aarch64le/base
cp -rf $basePath/SVN_PUBLISH/base/* $packagePath/base

projectLine=`sed -n '/mainmodel/=' $basePath/SVN_PUBLISH/base/fyapps/etc/mpu.version`
projectname=`sed -n "${projectLine}p" $basePath/SVN_PUBLISH/base/fyapps/etc/mpu.version | awk '{print $2}'`

DeleteMod=`$basePath/${Build_Shell_Path##*/}/readXMLFile.sh Qnx ${projectname} Images Delete Path`
SwitchMod=`$basePath/${Build_Shell_Path##*/}/readXMLFile.sh Qnx ${projectname} Images Switch Path`
DeleteMod=`echo ${DeleteMod} | sed 's/[[:space:]]//g'`
SwitchMod=`echo ${SwitchMod} | sed 's/[[:space:]]//g'`

#AWK分割的值无法赋予Shell中，转化到文件中读取
echo $DeleteMod | awk -F '##' '{ for(i=1;i<NF;i++) print $i > "DeleteModText"}'
echo $SwitchMod | awk -F '##' '{ for(i=1;i<NF;i++) print $i > "SwitchModText"}'
echo "read DeleteModText  ......."
cat DeleteModText

echo "read SwitchModText  ......."
cat SwitchModText
   
if [ "$CPULIST" = "aarch64" ];then
    perl strip-debug-and-store-64bit.pl liblist.txt
    perl strip-debug-and-store-64bit.pl binlist.txt
else
    perl strip-debug-and-store-32bit.pl liblist.txt
    perl strip-debug-and-store-32bit.pl binlist.txt
fi

if [ ! -f ./strip-debug-and-store-adayo-64bit.pl ];then
    svn --force export http://**********/svn/Qualcomm/02_SA8155P/01_HS7001A/02_CodeLib/01_MainPath/1.2/02_Android/04_Shell/baseConfigShell/strip-debug-and-store-adayo-64bit.pl ./ 
fi

if [ "$CPULIST" = "aarch64" ];then
    perl strip-debug-and-store-adayo-64bit.pl appslist.txt
    perl strip-debug-and-store-adayo-64bit.pl midslist.txt
    #perl strip-debug-and-store-adayo-64bit.pl libslist.txt
else
    perl strip-debug-and-store-adayo-32bit.pl appslist.txt
    perl strip-debug-and-store-adayo-32bit.pl midslist.txt
    # perl strip-debug-and-store-adayo-32bit.pl libslist.txt
fi

while read -r deleteStr
do
	echo "Stripdebug Qnx Delete $packagePath/$deleteStr"
	qnxDeletePath=${deleteStr}
	if [[ ${qnxDeletePath##*/} =~ \{ ]] && [[ ${qnxDeletePath##*/} =~ \} ]] && [[ ! ${qnxDeletePath%/*} =~ \{ ]];then
        qnxDeletePath2=`echo ${qnxDeletePath##*/} | awk -F '{' '{print $2}' | awk -F '}' '{print $1}'`
        qnxDeleteArr=(${qnxDeletePath2//,/ })
        for arri in ${qnxDeleteArr[@]}
        do 
			if [[ ${packagePath} != "" ]] && [[ ${qnxDeletePath%/*} != "" ]] && [[ ${arri} != "" ]];then
			    echo "Stripdebug Qnx Delete path1 --> rm -rf ${packagePath}/${qnxDeletePath%/*}/${arri}"
				rm -rf ${packagePath}/${qnxDeletePath%/*}/${arri}
			fi
        done
    else
		if [[ ${qnxDeletePath%/*} =~ \{ ]];then
			echo "Qnx Delete XML Fail format error !!!!"
			exit 2
		fi
		if [[ ${packagePath} != "" ]] && [[ ${qnxDeletePath} != "" ]];then
			echo "Stripdebug Qnx Delete path2 --> rm -rf ${packagePath}/${qnxDeletePath}"
            rm -rf ${packagePath}/${qnxDeletePath}
		fi
    fi
done < DeleteModText

while read -r switchStr
do
    SwitchMod1=`echo $switchStr | awk -F '->' '{print $1}'`
    SwitchMod2=`echo $switchStr | awk -F '->' '{print $2}'`
    if [[ -f ${packagePath}/${SwitchMod1} ]] && [[ ! -d ${packagePath}/${SwitchMod2%/*} ]];then
       echo "Stripdebug  switchStr ${packagePath}/${SwitchMod2%/*} DIR is not exist !!!!! mkdir dir ${packagePath}/${SwitchMod2%/*}"
	   mkdir -p ${packagePath}/${SwitchMod2%/*}
    fi
   
    if [[ -d ${packagePath}/${SwitchMod1} ]] && [[ ! -d ${packagePath}/${SwitchMod2} ]];then
       echo "Stripdebug  switchStr ${packagePath}/${SwitchMod2} DIR is not exist !!!!! mkdir dir ${packagePath}/${SwitchMod2}"
	   mkdir -p ${packagePath}/${SwitchMod2}
    fi
    echo "Stripdebug  switchStr cp $packagePath/$SwitchMod1 $packagePath/$SwitchMod2"
    cp -rf $packagePath/$SwitchMod1 $packagePath/$SwitchMod2
done < SwitchModText

if [ -d $publishPath ] && [ $publishPath != "" ];then
    rm -f $publishPath/{DeleteModText,SwitchModText}
    rm -f $publishPath/{liblist.txt,binlist.txt,appslist.txt,midslist.txt,libslist.txt}
fi

mpuPackageBL=`sed -n '/#MPU PACKAGE BEGIN/=' ./target/hypervisor/host/build_files/system.build.tmpl`
if [ $mpuPackageBL = '' ];then
    echo '############################################' >> ./target/hypervisor/host/build_files/system.build.tmpl
    echo '#MPU PACKAGE BEGIN' >> ./target/hypervisor/host/build_files/system.build.tmpl
    mpuPackageBL=`sed -n '/#MPU PACKAGE BEGIN/=' ./target/hypervisor/host/build_files/system.build.tmpl`
fi

mpuPackageEL=`sed -n '/#MPU PACKAGE END/=' ./target/hypervisor/host/build_files/system.build.tmpl`
if [[ $mpuPackageEL = '' ]];then
    sed -i ''${mpuPackageBL}'a '#MPU PACKAGE END'' ./target/hypervisor/host/build_files/system.build.tmpl
    mpuPackageEL=`sed -n '/#MPU PACKAGE END/=' ./target/hypervisor/host/build_files/system.build.tmpl`
    sed -i ''${mpuPackageEL}'a '############################################'' ./target/hypervisor/host/build_files/system.build.tmpl
fi

if [ ! -f ./writepackageconfig.sh ];then
    svn --force export http://**********/svn/Qualcomm/02_SA8155P/01_HS7001A/02_CodeLib/01_MainPath/1.2/02_Android/04_Shell/baseConfigShell/writepackageconfig.sh ./ 
fi

chmod 777 ./writepackageconfig.sh
./writepackageconfig.sh
