<?xml version="1.0" ?>
<data>
  <!--NOTE: This is an ** Autogenerated file **-->
  <!--NOTE: Sector size is 4096bytes-->
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="xbl.elf" label="xbl_a" num_partition_sectors="896" partofsingleimage="false" physical_partition_number="1" readbackverify="false" size_in_KB="3584.0" sparse="false" start_byte_hex="0x6000" start_sector="6"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="xbl_config.elf" label="xbl_config_a" num_partition_sectors="32" partofsingleimage="false" physical_partition_number="1" readbackverify="false" size_in_KB="128.0" sparse="false" start_byte_hex="0x386000" start_sector="902"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="xbl_config.elf" label="xbl_config_recovery_a" num_partition_sectors="32" partofsingleimage="false" physical_partition_number="1" readbackverify="false" size_in_KB="128.0" sparse="false" start_byte_hex="0x3a6000" start_sector="934"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="" label="last_parti" num_partition_sectors="0" partofsingleimage="false" physical_partition_number="1" readbackverify="false" size_in_KB="0" sparse="false" start_byte_hex="0x3c6000" start_sector="966"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="gpt_main1.bin" label="PrimaryGPT" num_partition_sectors="6" partofsingleimage="true" physical_partition_number="1" readbackverify="false" size_in_KB="24.0" sparse="false" start_byte_hex="0x0" start_sector="0"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="gpt_backup1.bin" label="BackupGPT" num_partition_sectors="5" partofsingleimage="true" physical_partition_number="1" readbackverify="false" size_in_KB="20.0" sparse="false" start_byte_hex="(4096*NUM_DISK_SECTORS)-20480." start_sector="NUM_DISK_SECTORS-5."/>
</data>
