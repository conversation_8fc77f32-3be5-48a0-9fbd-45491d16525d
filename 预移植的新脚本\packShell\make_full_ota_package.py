#!/usr/bin/python3
# encoding: utf-8
# @Author: Tahm
# @File: make_full_ota_package.py
#  .--,       .--,
# ( (  \.---./  ) )
#  '.__/o   o\__.'
#     {=  ^  =}
#      >  -  <

import os
import sys
import subprocess

def rat_system(cmd: str) -> int:
    errno = subprocess.run(cmd, shell = True)
    print("errno.returncode:", errno.returncode,   cmd)
    return errno.returncode
   
def replace_file_text(file: str, text: str, rep: str):
    with open(file, "r", encoding="utf-8") as f1:
        content = f1.read()
    
    # 替换文本
    content = content.replace(text, rep)
    
    # 将替换后的内容写回文件
    with open(file, "w", encoding="utf-8") as f2:
        f2.write(content)

def main():
    args = sys.argv

    # 打印出所有参数
    # for i in range(1, len(args)):
    #     print(args[i])

    # 如果你想要检查脚本是否在没有参数的情况下运行
    if len(args) <= 3:
        print('usage: python3 make_full_ota_package.py <input_file> <output_dir> <project>')
        exit(1)

    print('make full ota package V1.0 2024/7/6')
    input_file = os.path.abspath(args[1])
    print('input_file:', input_file)
    
    output_dir = os.path.abspath(args[2])
    print('output_dir:', output_dir)
    tmp_dir = output_dir + '/tmp'
    print('tmp_dir:', tmp_dir)
    
    project = args[3]
    print('project:', project)
    
    input_file_name = input_file.split('/')[-1]
    print('input_file_name:', input_file_name)
    
    prefix = input_file_name[2:5]
    origin_config = input_file_name[5:14]
    suffix = input_file_name[14:-4]
    print('prefix:', prefix)
    print('origin_config:', origin_config)
    print('suffix:', suffix)
    
    config_list = []
    if project == 'HS7006A':
        config_list = ['E00141245', 'E00147829', 'E00132011']
    elif project == 'HS7008A':
        config_list = ['E00143715', 'E00147879']

    cur_dir = os.getcwd()
    os.chdir(output_dir)
    
    rat_system('rm -rf ' + tmp_dir)
    rat_system('mkdir -p ' + tmp_dir)
    rat_system('unzip -P 123456 -j ' + input_file + ' -d ' + tmp_dir)
    
    for config in config_list:
        config_dir = '2-' + prefix + config + suffix
        rat_system('rm -rf ' + config_dir)
        rat_system('mkdir -p ' + config_dir)
        rat_system('cp -rf ' + tmp_dir + '/* ' + config_dir)
        
        # 文本替换
        versionCfg = config_dir + '/version.cfg'
        replace_file_text(versionCfg, origin_config, config)
        
        # 一层
        zip1_file = '2-' + prefix + config + suffix + '.zip'
        rat_system('zip -r -o ' + zip1_file + ' ' + config_dir)
        rat_system('rm -rf ' + config_dir)

        # 二层
        zip2_file = prefix + config + suffix + 'ALL.zip'
        rat_system('zip -r -o ' + zip2_file + ' ' + zip1_file)
        
        # 三层
        zip3_file = prefix + config + suffix + '.zip'
        rat_system('zip -r -o ' + zip3_file + ' ' + zip2_file)
        rat_system('rm -rf ' + zip2_file)
    
    rat_system('rm -rf ' + output_dir + '/2-*')
    rat_system('rm -rf ' + tmp_dir)
    
    os.chdir(cur_dir)

if __name__ == "__main__":
    main()