#! /bin/bash
#****************************
# QNX编译脚本
# QNX编译主脚本
# Author xizhuang.wu
# version 2.0
#**************************** 

BUILD_DATE=$(date +%m-%d-%Y)
LOCAL_PATH=`pwd`
MAINPROJECT=""
MAINBSPPROJECT=""
MAINPROJECT_BRA=""
SVN_ROOT=http://10.2.4.101/svn/Qualcomm/02_SA8155P
Build_Shell_Path="http://10.2.4.101/svn/Qualcomm/02_SA8155P/01_HS7001A/02_CodeLib/01_MainPath/1.2/02_Android/04_Shell/buildShell3.0/buildShell"
Base_Shell_Path="http://10.2.4.101/svn/Qualcomm/02_SA8155P/01_HS7001A/02_CodeLib/01_MainPath/1.2/02_Android/04_Shell/baseConfigShell"

projectname="test"
projectversion=""
compare_version=""
build_paramNum=""

#Jenkins上启动脚本定义的全局变量
ISSRCTAG=$JENKINS_SVN_TAG_PATH
SRCTAG_VERSION=$JENKINS_MODEL_NAME
SUB_VERSION=$JENKINS_SVN_INT_VERSION
PROJECT_NAME=$JENKINS_PRJNAME
PROJECT_VERSION=$JENKINS_SVN_VERSION  

is_svn_commit=1
svn_checkout_ver=0
build_branch=0

#保存环境变量
FGE_PATH=$PATH
FGE_JAVA_HOME=$JAVA_HOME

build_amss="0"
coverity_scan="false"
rmOutputDir=""
issueVersion="0"
updatePackage=""

#init coverity argument.
HOST=**********
user=myhao
password=123456
CoverityPath=/qnx/compiler/cov-analysis-linux64/
# cp17 coverity构建工具地址
CoverityPath1=/home/<USER>/e-cockpit/qnx_coverity/cov-analysis-linux64
COVCONFIGURE=$CoverityPath1/bin/cov-configure
COVBUILD=$CoverityPath1/bin/cov-build
COVRESET=$CoverityPath/bin/cov-manage-emit
COVANAL=$CoverityPath/bin/cov-analyze
COVCMT=$CoverityPath/bin/cov-commit-defects
COVPAGE=$CoverityPath/bin/cov-format-errors
COVADDSTREAM=$CoverityPath/bin/cov-manage-im
COVADDPROJECT=$CoverityPath/bin/cov-manage-im

# cp17 软安静兮构建工具地址
RA_license=/mnt/home/<USER>/e-cockpit/qnx-android/codescan/nuw/StatiCode-Analysis-Linux64-5.1.**********/bin
RA_token=/mnt/home/<USER>/e-cockpit/qnx-android/codescan/nuw/StatiCode-Analysis-Linux64-5.1.**********/token


SVN_CMD="svn"
SVN_AUTH="--username $USERNAME --password $PASSWORD --no-auth-cache --trust-server-cert"

ControlSys=null
PN=null
SoftNum=null
ProjectNum=null
IfsVersion=null

DEF_PROJECT_TRUNK_PATH=0
DEF_PROJECT_TAG_PATH=1
DEF_PROJECT_SOURCE_PATH=2

REV_PROJECT_TRUNK_PATH=0
REV_PROJECT_TAG_PATH=1

PACKAGE_MODULE_PATH=0
PACKAGE_MODULE_NAME=1

normal_Version="0"
packageConfig="packageConfig.cfg"

mpuversion_svntag=0

isBuildC1="1"
isBuildSecure="0"
isBuildXuGao="0"

while getopts "v:p:a:y:n:f:c:u:t:q:b:d:g:s:x:" opt
do
    case $opt in
            v ) echo "v "
            	projectversion=$OPTARG;;
            p ) echo "p "
            	projectname=$OPTARG;;
			a ) echo "a "
                build_amss=$OPTARG;;
		    y ) echo "y "
			    coverity_scan=$OPTARG;;
			n ) echo "n "
			    normal_Version=$OPTARG;;
			f ) echo "f "
			    issueVersion=$OPTARG;;
		    c ) echo "c "
			    isBuildC1=$OPTARG;;
			u ) echo "u "
			    isBuildUFS=$OPTARG;;
		    t ) echo "t "
			    buildType=$OPTARG;;
			q ) echo "q "
			    PACKAGE_BUILD_DATE=$OPTARG;;
			b ) echo "b "
			    build_paramNum=$OPTARG;;
			d ) echo "d "
			    android_SubPid=$OPTARG;;
			g ) echo "g "
			    package_type=$OPTARG;;
			s ) echo "s "
			    isBuildSecure=$OPTARG;;
			x ) echo "x "
			    isBuildXuGao=$OPTARG;;
		    ? ) echo "$0 error argument."
			exit 1;;
    esac
done

Qualcomm8155BashPath=/mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155

output_name=Coverity_${projectname}_$(date -d today +"%Y%m%d_%H%M%S")
OUTPUT=$Qualcomm8155BashPath/${projectname}_Package/Coverity_Build/${output_name}
PUSHTODIR=/qnx/builder/Coverity
PUSHTO=$PUSHTODIR/$output_name
compiler_path=$Qualcomm8155BashPath/${projectname}_Package/Coverity_Build
applist=${OUTPUT}/App.list

MAIN_BSP=""
MAIN_MPU=""
MAINPROJECT=$projectname

Jenkins_Source_Path="http://10.2.4.101/svn/Qualcomm/02_SA8155P/"${projectname%%_*}"/02_CodeLib/01_ProjectPath/03_Config/Jenkins_Source_Shell"
if [[ ${projectname} =~ _ ]];then
    GLOBAL_SVN_SOURCE_TAG_MAIN_HYP="http://10.2.4.101/svn/Qualcomm/02_SA8155P/"${projectname%%_*}"/02_CodeLib/01_ProjectPath/01_Qnx/02_Mpu_Public/04_tag/"${projectname}
else
    GLOBAL_SVN_SOURCE_TAG_MAIN_HYP="http://10.2.4.101/svn/Qualcomm/02_SA8155P/"${projectname%%_*}"/02_CodeLib/01_ProjectPath/01_Qnx/02_Mpu_Public/04_tag"
fi

#项目SVN TAG版本路径

if [[ ${isBuildSecure} = "1" ]];then
	SVN_SOURCE_TAG_PROJ_HPY=${GLOBAL_SVN_SOURCE_TAG_MAIN_HYP}/${projectname}"_Tag-"${projectversion}_safety
else
	SVN_SOURCE_TAG_PROJ_HPY=${GLOBAL_SVN_SOURCE_TAG_MAIN_HYP}/${projectname}"_Tag-"${projectversion}
fi


if [ ${#projectname} -lt 5 ];then
	echo "not set project, please check!"
	_build_error_exit $RUNING_ERROR_PARAM
fi

QualcommPathNum=4
QualcommLastPathName='HQX1.2.1.C1_R00004.2'
QualcommPathName='hqx1.2.1.c1_r00004.2'
QualcommQNXPath='qnx_bsp'
QualcommSDPPath=$QualcommPathName'/qnx_sdp'

LOCALQUA_BSP_PATH=$LOCAL_PATH/$QualcommPathName/$QualcommQNXPath
echo "LOCALQUA_BSP_PATH: $LOCALQUA_BSP_PATH"
LOCALQNX_SDP_PATH=$LOCAL_PATH/$QualcommSDPPath
LOCALOUT_ANDROID=$LOCAL_PATH/$QualcommPathName/android
LOCALQUA_AMSS_PATH=$LOCAL_PATH/$QualcommPathName/amss

#编译错误参数   
#            51 BSP编译报错   52 MPU APP报错    53 AMSS编译报错    54 AMSS Meta build报错   56 QNX打包失败
#            55 编译配置错误
#            26 服务器空间异常    28 SVN错误    29 Tag 版本错误
ERROR_PARAM_BUILD_BSP=51
ERROR_PARAM_BUILD_MPU=52
ERROR_PARAM_BUILD_AMSS=53
ERROR_PARAM_AMSS_Meta=54
ERROR_PARAM_CONFIG=55
ERROR_PARAM_PACKAGE=56
ERROR_PARAM_CLEAN=26
ERROR_PARAM_SVN=28
ERROR_PARAM_TAG=29

ERROR_BUILD_MODULE_BSP_PATH="qnx_bsp/apps/qnx_ap/AMSS"
ERROR_BUILD_MODULE_MPU_PATH="qnx_bsp/apps/qnx_ap/AMSS/mpu"

RUNING_ERROR_PARAM=0

_build_error_exit ()
{
    EXIT_ERROR_PARAM=$1
	echo "QNX build Error,Error param is $EXIT_ERROR_PARAM,exit ........"
	exit $EXIT_ERROR_PARAM
}

async_build_inspect ()
{
    if [[ -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/${projectname}_Package/RunningFlag_${build_paramNum}/asyncBuildFlag ]];then
	    asyncFlag=`cat /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/${projectname}_Package/RunningFlag_${build_paramNum}/asyncBuildFlag`
		if [[ $asyncFlag -eq 1 ]];then
		    echo "Android Async Build Error before,End Build Qnx and exit 0" 
		    _build_error_exit 0
		fi
	fi
}

mecho ()
{
	echo "======> [debug] $1"
}

myexit ()
{
    if [[ $coverity_scan = "true" ]]; then
		if [ $1 -eq 0 ]; then
			cov_type=`$LOCAL_PATH/${Build_Shell_Path##*/}/readXMLFile.sh Qnx ${projectname} Package Coverity`
			echo "begin qnx coverity !!! read cov_type is $cov_type ........"
		    if [[ $cov_type = "bsp" ]];then
		       cov_stream=${projectname}_bsp
			   output_name=Coverity_${projectname}_bsp_$(date -d today +"%Y%m%d_%H%M%S")
		    else
		       cov_stream=${projectname}_mpu
		    fi
		
            ls ${LOCALQUA_BSP_PATH}/apps/qnx_ap/AMSS/mpu/apps -l | grep ^d | awk '{print $9}' | grep -v "public" > ${applist}
			ls ${LOCALQUA_BSP_PATH}/apps/qnx_ap/AMSS/mpu/mids -l | grep ^d | awk '{print $9}' | grep -v "public" >> ${applist}
			
			scp -r $OUTPUT <EMAIL>:$PUSHTODIR
ssh <EMAIL> << remotessh
			sudo /qnx/compiler/cov-analysis-linux64/datechange.sh datebefore
			$COVRESET --dir $PUSHTO reset-host-name
            $COVRESET --dir $PUSHTO --tu-pattern "file('.*/hqx1.2.1.c1_r00004.2/qnx_sdp/.*')" delete
			if [[ "${cov_stream}" =~ mpu ]];then
               $COVRESET --dir $PUSHTO --tu-pattern "file('.*/hqx1.2.1.c1_r00004.2/qnx_bsp/apps/qnx_ap/boards/.*')" delete
			   $COVRESET --dir $PUSHTO --tu-pattern "file('.*/hqx1.2.1.c1_r00004.2/qnx_bsp/apps/qnx_ap/install/.*')" delete
			   $COVRESET --dir $PUSHTO --tu-pattern "file('.*/hqx1.2.1.c1_r00004.2/qnx_bsp/apps/qnx_ap/target/.*')" delete
			   $COVRESET --dir $PUSHTO --tu-pattern "file('.*/hqx1.2.1.c1_r00004.2/qnx_bsp/apps/qnx_ap/src700/.*')" delete
			   $COVRESET --dir $PUSHTO --tu-pattern "file('.*/hqx1.2.1.c1_r00004.2/qnx_bsp/apps/qnx_ap/AMSS/ML/NPU/.*')" delete
			   $COVRESET --dir $PUSHTO --tu-pattern "file('.*/hqx1.2.1.c1_r00004.2/qnx_bsp/apps/qnx_ap/AMSS/amc/.*')" delete
			   $COVRESET --dir $PUSHTO --tu-pattern "file('.*/hqx1.2.1.c1_r00004.2/qnx_bsp/apps/qnx_ap/AMSS/inc/.*')" delete
			   $COVRESET --dir $PUSHTO --tu-pattern "file('.*/hqx1.2.1.c1_r00004.2/qnx_bsp/apps/qnx_ap/AMSS/multimedia/.*')" delete
			   $COVRESET --dir $PUSHTO --tu-pattern "file('.*/hqx1.2.1.c1_r00004.2/qnx_bsp/apps/qnx_ap/AMSS/platform/.*')" delete
			fi
			
			$COVANAL --dir $PUSHTO           \
					 --concurrency           \
					 --enable-constraint-fpp \
					 --preview               \
					 --checker-option OVERRUN:report_underrun:true
					 
			$COVPAGE --dir $PUSHTO --lang zh-cn
			sudo /qnx/compiler/cov-analysis-linux64/datechange.sh datenow
			
			# 建立一个数据流
			# $COVADDSTREAM  --mode streams\
					# --add --set name:"${cov_stream}" \
					# --host ${HOST}         \
					# --user ${user}         \
					# --password ${password}
			# 建立一个项目
			# $COVADDPROJECT --mode projects\
					# --add --set name:"${cov_stream}" \
					# --host ${HOST}         \
					# --user ${user}         \
					# --password ${password}
			# 将数据流插入到项目中
			# $COVADDPROJECT --mode projects\
					# --update --name "${cov_stream}" --insert stream:${cov_stream} \
					# --host ${HOST}         \
					# --user ${user}         \
					# --password ${password}
						
			# 上传到
			# $COVCMT  --dir $PUSHTO          \
				# --version $projectversion	\
				# --stream ${cov_stream} \
				# --host ${HOST}         \
				# --user ${user}         \
				# --password ${password}
			exit
remotessh
			scp -r <EMAIL>:$PUSHTO $compiler_path
			
			$LOCAL_PATH/${Build_Shell_Path##*/}/coverity_error_checkout.sh ${projectname} $OUTPUT
		fi
	fi
	exit 
}

prompt ()
{
	#echo "=======================================================================" 
	echo "$1" 
	#echo "=======================================================================" 
} 

function set_version()
{
    async_build_inspect
	
	CarType=`$LOCAL_PATH/${Build_Shell_Path##*/}/readXMLFile.sh Qnx ${projectname} Package CarType`
	if [[ $CarType = "Beiqi" ]];then
		sv=`echo ${projectversion//./} | awk -F 'T' '{print $1}'`
		tv="T"`echo ${projectversion//./} | awk -F 'T' '{print $2}'`
   
		ControlSys=`$LOCAL_PATH/${Build_Shell_Path##*/}/readXMLFile.sh Qnx ${projectname} Package Version ControlSys`
		PN=`$LOCAL_PATH/${Build_Shell_Path##*/}/readXMLFile.sh Qnx ${projectname} Package Version PN`
		SoftNum=`$LOCAL_PATH/${Build_Shell_Path##*/}/readXMLFile.sh Qnx ${projectname} Package Version SoftNum`
		ProjectNum=`$LOCAL_PATH/${Build_Shell_Path##*/}/readXMLFile.sh Qnx ${projectname} Package Version ProjectNum`
		IfsVersion=`$LOCAL_PATH/${Build_Shell_Path##*/}/readXMLFile.sh Qnx ${projectname} Package Version IfsVersion`
   
		if [[ "${issueVersion}" = "0" ]];then
			updatePackage=${ControlSys}${PN}${sv}${SoftNum}${ProjectNum}${tv}${PACKAGE_BUILD_DATE:2}
		else
			updatePackage=${ControlSys}${PN}${sv}${SoftNum}${ProjectNum}
		fi
	else
		updatePackage=${projectversion}
	fi
}

function get_field_value()  
{  
    if [ ! -f $1 ] || [ $# -ne 3 ];then   
        return 1  
    fi  
	blockname=$2  
	fieldname=$3  
	  
	begin_block=0  
	end_block=0  
  
    cat $1 | while read line  
    do  
      
        if [ "X$line" = "X[$blockname]" ];then  
            begin_block=1  
            continue  
        fi  
          
        if [ $begin_block -eq 1 ];then  
            end_block=$(echo $line | awk 'BEGIN{ret=0} /^.? $/{ret=1} END{print ret}')  
            if [ $end_block -eq 1 ];then  
                #echo "end block"   
                break  
            fi  
      
            need_ignore=$(echo $line | awk 'BEGIN{ret=0} /^#/{ret=1} /^$/{ret=1} END{print ret}')  
            if [ $need_ignore -eq 1 ];then  
                #echo "ignored line:" $line   
                continue  
            fi  
            field=$(echo $line | awk -F= '{gsub(" |\t","",$1); print $1}')  
            value=$(echo $line | awk -F= '{gsub(" |\t","",$2); print $2}')  
            #echo "'$field':'$value'"   
            if [ "X$fieldname" = "X$field" ];then     
                #echo "result value:'$result'"   
                echo $value  
                break  
            fi  
              
        fi  
    done  
    return 0  
} 

function write_config()
{
    async_build_inspect
	
    if [[ ! -f $1 ]] || [[ $# -ne 1 ]];then
	    echo "write_config error!!!"
	    return 1
	fi
	configFile=$1
    for i in "${GLOBAL_PROJECT_DEF_SRCCODE[@]}"
    do
		root_path=($i)
			
		if [[ ${ISSRCTAG} = "2" ]];then
			if [[ ${isBuildSecure} = "1" ]];then
				svnInfoLogPath=${GLOBAL_SVN_SOURCE_TAG_MAIN_HYP}/${projectname}"_Tag-"${SRCTAG_VERSION}"_safety"/${root_path[${DEF_PROJECT_TAG_PATH}]}
			else
				svnInfoLogPath=${GLOBAL_SVN_SOURCE_TAG_MAIN_HYP}/${projectname}"_Tag-"${SRCTAG_VERSION}/${root_path[${DEF_PROJECT_TAG_PATH}]}
			fi
		else
			svnInfoLogPath=${root_path[${DEF_PROJECT_TRUNK_PATH}]}
		fi
		
        svnInfoLog=`$SVN_CMD info ${svnInfoLogPath} --username gjzhao --password gjz123 --no-auth-cache --trust-server-cert | grep "Last Changed Rev" | awk -F ':' '{print $2}' | sed 's/[[:space:]]//g'`
        svnInfoDate=`$SVN_CMD info ${svnInfoLogPath} --username gjzhao --password gjz123 --no-auth-cache --trust-server-cert | grep "Last Changed Date" | awk -F ': ' '{print $2}' | sed 's/^[ \t]*//g' | awk '{print $1" "$2}'`
        if [[ $svnInfoLog = "" ]] || [[ $svnInfoDate = "" ]];then
	        svnInfoLog=`$SVN_CMD info ${svnInfoLogPath} --username gjzhao --password gjz123 --no-auth-cache --trust-server-cert | grep "最后修改的版本" | awk -F ':' '{print $2}' | sed 's/[[:space:]]//g'`
            svnInfoDate=`$SVN_CMD info ${svnInfoLogPath} --username gjzhao --password gjz123 --no-auth-cache --trust-server-cert | grep "最后修改的时间" | awk -F ': ' '{print $2}' | sed 's/^[ \t]*//g' | awk '{print $1" "$2}'`
        fi
        echo "["${root_path[${DEF_PROJECT_TAG_PATH}]}"]" >> $configFile
        echo "tag="$svnInfoLog >> $configFile
        echo "path="$svnInfoLogPath >> $configFile
        echo -e "date="$svnInfoDate"\n" >> $configFile
    done

	return 0
}

sync_mainline ()
{
    async_build_inspect
	
    echo "Begin clean qnx build source ........"`date +%F" "%T`
    RUNING_ERROR_PARAM=$ERROR_PARAM_CLEAN
	
    cd $LOCAL_PATH
	
	#1.2 基线qnx源码包解压
	if [[ $LOCAL_PATH != "" ]] && [[ $QualcommPathName != "" ]] && [[ -d $LOCALQUA_AMSS_PATH ]] && [[ $LOCALQUA_AMSS_PATH =~ $LOCAL_PATH/$QualcommPathName ]];then
	    echo "QNX build source num less than $QualcommPathNum,delete $LOCALQUA_AMSS_PATH !!!!!"
	    rm -rf $LOCALQUA_AMSS_PATH
	fi
		
    if [[ $LOCAL_PATH != "" ]] && [[ $QualcommPathName != "" ]] && [[ -d $LOCALQNX_SDP_PATH ]] && [[ $LOCALQNX_SDP_PATH =~ $LOCAL_PATH/$QualcommPathName ]];then
	    echo "QNX build source num less than $QualcommPathNum,delete $LOCALQNX_SDP_PATH !!!!!"
	    rm -rf $LOCALQNX_SDP_PATH
	fi
		
    if [[ $LOCAL_PATH != "" ]] && [[ $QualcommPathName != "" ]] && [[ -d $LOCALQUA_BSP_PATH ]] && [[ $LOCALQUA_BSP_PATH =~ $LOCAL_PATH/$QualcommPathName ]];then
	    echo "QNX build source num less than $QualcommPathNum,delete $LOCALQUA_BSP_PATH !!!!!"
	    rm -rf $LOCALQUA_BSP_PATH
	fi

	mkdir -p $LOCALQUA_AMSS_PATH
	mkdir -p $LOCALQNX_SDP_PATH
	mkdir -p $LOCALQUA_BSP_PATH
		
	echo "cp -rf Qnx build source from $Qualcomm8155BashPath/Qualcomm8155C1Line/$QualcommPathName'_copy'/$QualcommPathName"
	cp -rf $Qualcomm8155BashPath/Qualcomm8155C1Line/$QualcommPathName'_copy'/$QualcommPathName/amss/* $LOCALQUA_AMSS_PATH
    cp -rf $Qualcomm8155BashPath/Qualcomm8155C1Line/$QualcommPathName'_copy'/$QualcommPathName/qnx_sdp/* $LOCALQNX_SDP_PATH
    cp -rf $Qualcomm8155BashPath/Qualcomm8155C1Line/$QualcommPathName'_copy'/$QualcommPathName/$QualcommQNXPath/* $LOCALQUA_BSP_PATH
	       
	if [[ $? -ne 0 ]] && (([[ `cat $Qualcomm8155BashPath/Qualcomm8155C1Line/tailFlag` -ne 99 ]] && [[ `cat $Qualcomm8155BashPath/Qualcomm8155C1Line/tailFlag` -ne 0 ]]) || [[ ! -f $Qualcomm8155BashPath/Qualcomm8155C1Line/tailFlag ]]);then
		echo "Qnx process begin tail /var/lib/jenkins/source/$QualcommLastPathName/$QualcommPathName.tar.gz"
		if [[ -e $Qualcomm8155BashPath/Qualcomm8155C1Line/$QualcommPathName'_copy' ]];then
		    echo "Last $Qualcomm8155BashPath/Qualcomm8155C1Line/$QualcommPathName'_copy' tar failed !!! rm and tar second time !"
			rm -rf $Qualcomm8155BashPath/Qualcomm8155C1Line/$QualcommPathName'_copy'
		fi
		mkdir -p $Qualcomm8155BashPath/Qualcomm8155C1Line/$QualcommPathName'_copy'
		echo 0 > $Qualcomm8155BashPath/Qualcomm8155C1Line/tailFlag
		tar -vxf /var/lib/jenkins/source/$QualcommLastPathName/$QualcommPathName.tar.gz -C $Qualcomm8155BashPath/Qualcomm8155C1Line/$QualcommPathName'_copy'
        if [[ $? -ne 0 ]];then
	        echo "tar -vxf /var/lib/jenkins/source/$QualcommLastPathName/$QualcommPathName.tar.gz failed !!!!"
	        rm -rf $Qualcomm8155BashPath/Qualcomm8155C1Line
			_build_error_exit $RUNING_ERROR_PARAM
	    fi
		echo 99 > $Qualcomm8155BashPath/Qualcomm8155C1Line/tailFlag
		
		echo "the twice time to cp -rf Qnx build source from $Qualcomm8155BashPath/Qualcomm8155C1Line/$QualcommPathName'_copy'/$QualcommPathName"
	    cp -rf $Qualcomm8155BashPath/Qualcomm8155C1Line/$QualcommPathName'_copy'/$QualcommPathName/amss/* $LOCALQUA_AMSS_PATH
        cp -rf $Qualcomm8155BashPath/Qualcomm8155C1Line/$QualcommPathName'_copy'/$QualcommPathName/qnx_sdp/* $LOCALQNX_SDP_PATH
        cp -rf $Qualcomm8155BashPath/Qualcomm8155C1Line/$QualcommPathName'_copy'/$QualcommPathName/$QualcommQNXPath/* $LOCALQUA_BSP_PATH
    fi			 	   
	
	if [[ $? -ne 0 ]];then
	    echo "Clean build source error,exit $RUNING_ERROR_PARAM"
	    _build_error_exit $RUNING_ERROR_PARAM
	fi
	
	if [[ -e $Qualcomm8155BashPath/${projectname}_Package/SVN_MCU_UPDATE ]];then
	    echo "delete $Qualcomm8155BashPath/${projectname}_Package/SVN_MCU_UPDATE path source !!!!!"
	    rm -rf $Qualcomm8155BashPath/${projectname}_Package/SVN_MCU_UPDATE
	fi
	mkdir -p $Qualcomm8155BashPath/${projectname}_Package/SVN_MCU_UPDATE
	
	if [[ $projectname =~ HS7012A ]] || [[ $projectname =~ "HS7023A" ]] || [[ $projectname =~ "HS7029A" ]];then
		if [[ -e $Qualcomm8155BashPath/${projectname}_Package/SVN_4G_MODULE_UPDATE ]];then
			echo "delete $Qualcomm8155BashPath/${projectname}_Package/SVN_4G_MODULE_UPDATE path source !!!!!"
			rm -rf $Qualcomm8155BashPath/${projectname}_Package/SVN_4G_MODULE_UPDATE
		fi
		mkdir -p $Qualcomm8155BashPath/${projectname}_Package/SVN_4G_MODULE_UPDATE
		
        rm -rf $Qualcomm8155BashPath/${projectname}_Package/OTA/AMSS
        rm -rf $Qualcomm8155BashPath/${projectname}_Package/OTA/Qnx
		mkdir -p $Qualcomm8155BashPath/${projectname}_Package/OTA/{Android,Qnx,AMSS}
	fi
	
	echo "Begin delect Qnx output img and update !!!!"
    if [[ $Qualcomm8155BashPath != "" ]] && [[ $projectname != "" ]] && [[ -d $Qualcomm8155BashPath/${projectname}_Package ]];then
        rm -f $Qualcomm8155BashPath/${projectname}_Package/amss.tar.gz
		rm -rf $Qualcomm8155BashPath/${projectname}_Package/output/Qnx
        rm -rf $Qualcomm8155BashPath/${projectname}_Package/Image/AMSS
        rm -rf $Qualcomm8155BashPath/${projectname}_Package/Image/Qnx
        rm -rf $Qualcomm8155BashPath/${projectname}_Package/Update/{AMSS.tar.gz,QNX.tar.gz}
    fi
  
    mkdir -p $Qualcomm8155BashPath/${projectname}_Package/Image/{Android,Qnx,AMSS}
    mkdir -p $Qualcomm8155BashPath/${projectname}_Package/Update
    mkdir -p $Qualcomm8155BashPath/${projectname}_Package/output/Qnx
	
	#1.2基线判断是否需要重新生成qnx700_4
	if [[ ! -d $LOCALQNX_SDP_PATH ]] && [[ "${isBuildC1}" = "0" ]]; then
	    echo "download sdp"
	    tar -vxf /var/lib/jenkins/source/qcom_base_line/sdp700_hqx1_2.tar.gz -C $LOCAL_PATH/
	fi
	
	#License register
	echo "Setting up QNX license files..."

	
	if [[ ! -d $LOCAL_PATH/.qnx/license ]]; then
	    echo "Extracting QNX SDP license to $LOCAL_PATH/.qnx/license"
	    tar -xf /var/lib/jenkins/source/sdp_license/qnx700_license/license.tar -C $LOCAL_PATH
	

	
	fi
	
	#重新配置coverity gcc配置
	cd $LOCALQNX_SDP_PATH/host/linux/x86_64/usr/lib/gcc/aarch64-unknown-nto-qnx7.0.0/5.4.0
	$COVCONFIGURE -co aarch64-unknown-nto-qnx7.0.0 -p gcc --template
	cd $LOCAL_PATH
	
	if [[ -e $LOCAL_PATH/SVN_CONFIG ]];then
	   rm -rf $LOCAL_PATH/{SVN_PUBLISH,SVN_MCU,SVN_CONFIG,SVN_4G_MODULE,SVN_LIGHT_SOUND}
	fi
	if [[ $projectname =~ HS7012A ]] || [[ $projectname =~ "HS7023A" ]] || [[ $projectname =~ "HS7029A" ]];then
		mkdir $LOCAL_PATH/{SVN_PUBLISH,SVN_MCU,SVN_CONFIG,SVN_4G_MODULE,SVN_LIGHT_SOUND}
	else
		mkdir $LOCAL_PATH/{SVN_PUBLISH,SVN_MCU,SVN_CONFIG}
	fi
	
	$SVN_CMD --force export $Base_Shell_Path ${LOCALQUA_BSP_PATH}/apps/qnx_ap --username gjzhao --password gjz123 --no-auth-cache --trust-server-cert
	echo "$SVN_CMD --force export $Base_Shell_Path ${LOCALQUA_BSP_PATH}/apps/qnx_ap"
	
	#统一设置脚本运行权限
	chmod -Rf 755 $LOCALQNX_SDP_PATH/target/qnx7/aarch64le
	chmod +777 $LOCALQUA_BSP_PATH/apps/qnx_ap/stripdebug.sh 
	chmod +777 $LOCALQUA_BSP_PATH/apps/qnx_ap/strip-debug-and-store-adayo-64bit.pl
	echo "End clean android build source ........"`date +%F" "%T`
}

_svn_make_tag ()
{
	async_build_inspect
	
    RUNING_ERROR_PARAM=$ERROR_PARAM_TAG
	echo "Qnx Begin make svn tag ......"`date +%F" "%T`
	if [ ${MAKETAG} -ne 1 ];then
		return 0
	fi

	if [ -z "$projectversion" ];then
		echo "You did not input a valid tag projectversion."
		return -1
	fi

	
	$SVN_CMD info $SVN_SOURCE_TAG_PROJ_HPY --username gjzhao --password gjz123 --no-auth-cache --trust-server-cert
	if [[ $? -eq 0 ]];then
	   echo "Qnx Tag $SVN_SOURCE_TAG_PROJ_HPY is exist !!!!! "
	   echo "delect Qnx previous Tag $SVN_SOURCE_TAG_PROJ_HPY,and build a new Tag $SVN_SOURCE_TAG_PROJ_HPY"
	   $SVN_CMD delete -m "delect Qnx previous Tag $SVN_SOURCE_TAG_PROJ_HPY,and build a new Tag $SVN_SOURCE_TAG_PROJ_HPY" $SVN_SOURCE_TAG_PROJ_HPY --username gjzhao --password gjz123 --no-auth-cache --trust-server-cert
	   if [ $? -ne "0" ];then
		   echo "SVN delect Qnx previous Tag $SVN_SOURCE_TAG_PROJ_HPY,and build a new Tag $SVN_SOURCE_TAG_PROJ_HPY ERROR, exit $RUNING_ERROR_PARAM !!!!!!!"
		   _build_error_exit $RUNING_ERROR_PARAM
	   fi
	fi

	$SVN_CMD mkdir --parents -m "add tag folder for $projectversion" $SVN_SOURCE_TAG_PROJ_HPY --username gjzhao --password gjz123 --no-auth-cache --trust-server-cert
	if [ $? -ne 0 ];then
		echo "SVN mkdir $projectversion ERROR, exit $RUNING_ERROR_PARAM !!!!!!"
		_build_error_exit $RUNING_ERROR_PARAM
	fi
	
	for i in "${GLOBAL_PROJECT_DEF_SRCCODE[@]}" ; do
		root_path=($i)
		tmp_message_hpy="create tag from ${root_path[${DEF_PROJECT_TRUNK_PATH}]} to  ${SVN_SOURCE_TAG_PROJ_HPY}/${root_path[${DEF_PROJECT_TAG_PATH}]}"
		echo "$tmp_message_hpy"
		$SVN_CMD cp -m "create tag from ${root_path[${DEF_PROJECT_TRUNK_PATH}]}"	${root_path[${DEF_PROJECT_TRUNK_PATH}]} 	${SVN_SOURCE_TAG_PROJ_HPY}/${root_path[${DEF_PROJECT_TAG_PATH}]} --username gjzhao --password gjz123 --no-auth-cache --trust-server-cert
		if [ $? -ne "0" ];then
			echo " SVN cp TAG ${root_path[${DEF_PROJECT_TRUNK_PATH}]} to ${projectname}_Tag-${projectversion} ERROR, exit $RUNING_ERROR_PARAM !!!!!!!"
			_build_error_exit $RUNING_ERROR_PARAM
		fi
	done
	echo "End make svn tag ......"`date +%F" "%T`
	return 0
}
_export_project_code ()
{
	echo "Qnx:Begin export svn code ........"`date +%F" "%T`
    RUNING_ERROR_PARAM=$ERROR_PARAM_SVN
	
	if [[ ${MAKETAG} -eq 1 ]] || [[ ${READTAG} -eq 1 ]];then
		tmp_index=${DEF_PROJECT_TAG_PATH}
		tmp_path_head_hyp=${SVN_SOURCE_TAG_PROJ_HPY}/
		
		$SVN_CMD info $tmp_path_head_hyp --username gjzhao --password gjz123 --no-auth-cache --trust-server-cert
		if [[ $? -ne 0 ]];then
			RUNING_ERROR_PARAM=$ERROR_PARAM_TAG
			echo "Qnx History Tag $SVN_SOURCE_TAG_PROJ_HPY didnt exist !!!!! exit $RUNING_ERROR_PARAM"
			_build_error_exit $RUNING_ERROR_PARAM
		fi
		echo "${MAKETAG} ${READTAG}  make tag"
	else
		tmp_index=${DEF_PROJECT_TRUNK_PATH}
		tmp_path_head_hyp=""
		echo "_export_project_code no make tag"
	fi
	
	for i in "${GLOBAL_PROJECT_DEF_SRCCODE[@]}" ; do
		async_build_inspect
		root_path=($i)
		
		svn_tag=$(get_field_value $configFile ${root_path[${DEF_PROJECT_TAG_PATH}]} tag)
		if [[ "${root_path[${DEF_PROJECT_TAG_PATH}]}" =~ .*MPU.* ]] && ([[ "${root_path[${DEF_PROJECT_TAG_PATH}]}" =~ ${projectname%%_*} ]] || [[ "${root_path[${DEF_PROJECT_TAG_PATH}]}" =~ ${projectname} ]]);then
		   mpuversion_svntag=$svn_tag
		fi
		if [[ $svn_tag = "" ]];then
			echo "${root_path[${DEF_PROJECT_TAG_PATH}]} path is not exist!!!"
			break;
		fi
		
		if [[ "${root_path[${DEF_PROJECT_TAG_PATH}]}" =~ .*MPU.* ]]; then
		   $SVN_CMD list ${tmp_path_head_hyp}${root_path[tmp_index]}/mpu/apps --username gjzhao --password gjz123 --no-auth-cache --trust-server-cert | grep -v "public" | awk -F '/' '{print $1}' >> ${LOCAL_PATH}/appsList.txt
		   $SVN_CMD list ${tmp_path_head_hyp}${root_path[tmp_index]}/mpu/mids --username gjzhao --password gjz123 --no-auth-cache --trust-server-cert | grep -v "public" | awk -F '/' '{print $1}' >> ${LOCAL_PATH}/midsList.txt
		   $SVN_CMD list ${tmp_path_head_hyp}${root_path[tmp_index]}/mpu/libs --username gjzhao --password gjz123 --no-auth-cache --trust-server-cert | awk -F '/' '{print $1}' >> ${LOCAL_PATH}/libsList.txt
		
		   while read -r appLine
		   do 
			  if [[ -d ${LOCAL_PATH}/${root_path[${DEF_PROJECT_SOURCE_PATH}]}/mpu/apps/$appLine ]] && [[ ${LOCAL_PATH}/${root_path[${DEF_PROJECT_SOURCE_PATH}]} =~ $Qualcomm8155BashPath ]];then
				 echo "delect same Apps "${LOCAL_PATH}/${root_path[${DEF_PROJECT_SOURCE_PATH}]}"/mpu/apps/"$appLine
				 rm -rf ${LOCAL_PATH}/${root_path[${DEF_PROJECT_SOURCE_PATH}]}/mpu/apps/$appLine
			  fi
		   done < ${LOCAL_PATH}/appsList.txt
		
		   while read -r midLine
		   do 
			  if [[ -d ${LOCAL_PATH}/${root_path[${DEF_PROJECT_SOURCE_PATH}]}/mpu/mids/$midLine ]] && [[ ${LOCAL_PATH}/${root_path[${DEF_PROJECT_SOURCE_PATH}]} =~ $Qualcomm8155BashPath ]];then
				 echo "delect same Mids "${LOCAL_PATH}/${root_path[${DEF_PROJECT_SOURCE_PATH}]}"/mpu/mids/"$midLine
				 rm -rf ${LOCAL_PATH}/${root_path[${DEF_PROJECT_SOURCE_PATH}]}/mpu/mids/$midLine
			  fi
		   done < ${LOCAL_PATH}/midsList.txt
		   
		   while read -r libLine
		   do 
			  if [[ -d ${LOCAL_PATH}/${root_path[${DEF_PROJECT_SOURCE_PATH}]}/mpu/libs/$libLine ]] && [[ ${LOCAL_PATH}/${root_path[${DEF_PROJECT_SOURCE_PATH}]} =~ $Qualcomm8155BashPath ]];then
				 echo "delect same Mids "${LOCAL_PATH}/${root_path[${DEF_PROJECT_SOURCE_PATH}]}"/mpu/libs/"$libLine
				 rm -rf ${LOCAL_PATH}/${root_path[${DEF_PROJECT_SOURCE_PATH}]}/mpu/libs/$libLine
			  fi
		   done < ${LOCAL_PATH}/libsList.txt
		   
		fi
		rm -rf ${LOCAL_PATH}/{appsList.txt,midsList.txt,libsList.txt}
		
		mkdir -p ${LOCAL_PATH}/temDir
		
		#拉取各模块代码
		echo "QNX Export ${root_path[${DEF_PROJECT_TAG_PATH}]} code from ${svn_tag} ${tmp_path_head_hyp}${root_path[tmp_index]} to ${LOCAL_PATH}/${root_path[${DEF_PROJECT_SOURCE_PATH}]}"
		$SVN_CMD --force export ${tmp_path_head_hyp}${root_path[tmp_index]} ${LOCAL_PATH}/temDir --username gjzhao --password gjz123 --no-auth-cache --trust-server-cert
		
		if [[ $? -ne 0 ]];then
			echo "QNX SVN export ERROR ,exit 2 !!!!! "
			_build_error_exit $RUNING_ERROR_PARAM
		fi
		
		#删除多余继承的代码
		for j in "${GLOBAL_PROJECT_DEF_REVCODE[@]}"
		do
			rev_path=($j)
			
			if [[ ${root_path[${DEF_PROJECT_TAG_PATH}]} = ${rev_path[${DEF_PROJECT_TAG_PATH}]} ]];then
				echo "Qnx Remove temDir ${rev_path[${REV_PROJECT_SOURCE_PATH}]} after download ${root_path[DEF_PROJECT_TAG_PATH]}"
				qnxRemovePath=${rev_path[${REV_PROJECT_SOURCE_PATH}]}
				if [[ ${qnxRemovePath##*/} =~ \{ ]] && [[ ${qnxRemovePath##*/} =~ \} ]] && [[ ! ${qnxRemovePath%/*} =~ \{ ]];then
					qnxRemovePath2=`echo ${qnxRemovePath##*/} | awk -F '{' '{print $2}' | awk -F '}' '{print $1}'`
					qnxRemoveArr=(${qnxRemovePath2//,/ })
					for arri in ${qnxRemoveArr[@]}
					do 
						if [[ ${qnxRemovePath%/*} != "" ]] && [[ ${arri} != "" ]];then
							echo "Qnx Remove path1 --> rm -rf ${LOCAL_PATH}/temDir/${qnxRemovePath%/*}/${arri}"
							rm -rf ${LOCAL_PATH}/temDir/${qnxRemovePath%/*}/${arri}
						fi
					done
				else
					if [[ ${qnxRemovePath%/*} =~ \{ ]];then
						echo "Qnx Remove XML Fail format error !!!!"
						_build_error_exit $RUNING_ERROR_PARAM
					fi
					if [[ ${qnxRemovePath} != "" ]];then
						echo "Qnx Remove path2 --> rm -rf ${LOCAL_PATH}/temDir/${qnxRemovePath}"
						rm -rf ${LOCAL_PATH}/temDir/${qnxRemovePath}
					fi
				fi
			fi
		done
		cp -rf ${LOCAL_PATH}/temDir/* ${LOCAL_PATH}/${root_path[${DEF_PROJECT_SOURCE_PATH}]}
		
		if [[ -e ${LOCAL_PATH}/temDir ]] && [[ ${LOCAL_PATH} != "" ]];then
			rm -rf ${LOCAL_PATH}/temDir
		fi
		
		#MCU增加更新渠道
		if [[ "${root_path[${DEF_PROJECT_TAG_PATH}]}" =~ .*MCU.* ]];then
			if [[ `$SVN_CMD list ${tmp_path_head_hyp}${root_path[tmp_index]} --username gjzhao --password gjz123 --no-auth-cache --trust-server-cert` != "" ]];then
				if [[ `ls $Qualcomm8155BashPath/${projectname}_Package/SVN_MCU_UPDATE -l | grep "^-" | wc -l` -gt 0 ]];then
				   rm -rf $Qualcomm8155BashPath/${projectname}_Package/SVN_MCU_UPDATE
				   mkdir -p $Qualcomm8155BashPath/${projectname}_Package/SVN_MCU_UPDATE
				fi
				echo "QNX Export ${root_path[${DEF_PROJECT_TAG_PATH}]} code from ${svn_tag} ${tmp_path_head_hyp}${root_path[tmp_index]} to ${LOCAL_PATH}/${root_path[${DEF_PROJECT_SOURCE_PATH}]}"
				$SVN_CMD checkout ${tmp_path_head_hyp}${root_path[tmp_index]} $Qualcomm8155BashPath/${projectname}_Package/SVN_MCU_UPDATE --username gjzhao --password gjz123 --no-auth-cache --trust-server-cert
				if [[ $? -ne 0 ]];then
					echo "QNX SVN checkout MCU ERROR ,exit 2 !!!!! "
					_build_error_exit $RUNING_ERROR_PARAM
				fi
			else
				echo "svn tag ${SRCTAG_VERSION} ${tmp_path_head_hyp}${root_path[tmp_index]} is nothing , No Checkout to $Qualcomm8155BashPath/${projectname}_Package/SVN_MCU_UPDATE"
				# break
			fi
		fi	

		#4G模块增加更新渠道
		if [[ "${root_path[${DEF_PROJECT_TAG_PATH}]}" =~ .*4G_MODULE.* ]];then
			if [[ `$SVN_CMD list ${tmp_path_head_hyp}${root_path[tmp_index]} --username gjzhao --password gjz123 --no-auth-cache --trust-server-cert` != "" ]];then
				if [[ `ls $Qualcomm8155BashPath/${projectname}_Package/SVN_4G_MODULE_UPDATE -l | grep "^-" | wc -l` -gt 0 ]];then
				   rm -rf $Qualcomm8155BashPath/${projectname}_Package/SVN_4G_MODULE_UPDATE
				   mkdir -p $Qualcomm8155BashPath/${projectname}_Package/SVN_4G_MODULE_UPDATE
				fi
				echo "QNX Export ${root_path[${DEF_PROJECT_TAG_PATH}]} code from ${svn_tag} ${tmp_path_head_hyp}${root_path[tmp_index]} to ${LOCAL_PATH}/${root_path[${DEF_PROJECT_SOURCE_PATH}]}"
				$SVN_CMD checkout ${tmp_path_head_hyp}${root_path[tmp_index]} $Qualcomm8155BashPath/${projectname}_Package/SVN_4G_MODULE_UPDATE --username gjzhao --password gjz123 --no-auth-cache --trust-server-cert
				if [[ $? -ne 0 ]];then
					echo "QNX SVN checkout 4G_MODULE ERROR ,exit 2 !!!!! "
					_build_error_exit $RUNING_ERROR_PARAM
				fi
			else
				echo "svn tag ${SRCTAG_VERSION} ${tmp_path_head_hyp}${root_path[tmp_index]} is nothing , No Checkout to $Qualcomm8155BashPath/${projectname}_Package/SVN_4G_MODULE_UPDATE"
				# break
			fi
		fi	
	
	done
	
	if [[ -f $LOCAL_PATH/SVN_PUBLISH/base/fyapps/bins/mcu_post.sh ]];then
		cp -f $LOCAL_PATH/SVN_PUBLISH/base/fyapps/bins/mcu_post.sh $LOCAL_PATH/SVN_MCU
	fi
	cp -f $LOCAL_PATH/SVN_PUBLISH/base/fyapps/etc/version.cfg $Qualcomm8155BashPath/${projectname}_Package
	cp -rf $LOCAL_PATH/SVN_MCU $Qualcomm8155BashPath/${projectname}_Package
	
	if [[ ${projectname} =~ "HS7012A" ]] || [[ ${projectname} =~ "HS7023A" ]] || [[ $projectname =~ "HS7029A" ]];then
		if [[ -e $Qualcomm8155BashPath/${projectname}_Package/SVN_4G_MODULE ]];then
			rm -rf $Qualcomm8155BashPath/${projectname}_Package/SVN_4G_MODULE
		fi
		cp -rf $LOCAL_PATH/SVN_4G_MODULE $Qualcomm8155BashPath/${projectname}_Package
		if [[ -e $Qualcomm8155BashPath/${projectname}_Package/SVN_LIGHT_SOUND ]];then
			rm -rf $Qualcomm8155BashPath/${projectname}_Package/SVN_LIGHT_SOUND
		fi
		cp -rf $LOCAL_PATH/SVN_LIGHT_SOUND $Qualcomm8155BashPath/${projectname}_Package
	fi
	
	if [[ -d $LOCAL_PATH/SVN_PUBLISH/base/fyapps/recovery ]];then
	   cp -rf $LOCAL_PATH/SVN_MCU/*.bin $LOCAL_PATH/SVN_PUBLISH/base/fyapps/recovery
	fi
	
	echo "Qnx:End export svn code ........"`date +%F" "%T`
	return 0
}

_upload_qnx_config ()
{
    async_build_inspect
	
    RUNING_ERROR_PARAM=$ERROR_PARAM_CONFIG
    echo "begin export version.cfg or upload qnx xml config,create source shell and add to svn!!!"
	
    #拉取qnx版本编译记录文件
	$SVN_CMD info http://10.2.4.101/svn/Qualcomm/02_SA8155P/${projectname%%_*}/02_CodeLib/01_ProjectPath/03_Config/Version --username gjzhao --password gjz123 --no-auth-cache --trust-server-cert
	if [[ $? -ne 0 ]];then
	   echo "QNX Version DIR didnt exit , mkdir in svn !!!!! "
	   $SVN_CMD mkdir --parents -m "QNX Version DIR didnt exit , mkdir in SVN " http://10.2.4.101/svn/Qualcomm/02_SA8155P/${projectname%%_*}/02_CodeLib/01_ProjectPath/03_Config/Version --username gjzhao --password gjz123 --no-auth-cache --trust-server-cert
	fi
	$SVN_CMD checkout "http://10.2.4.101/svn/Qualcomm/02_SA8155P"/${projectname%%_*}/"02_CodeLib/01_ProjectPath/03_Config/Version" $LOCAL_PATH/SVN_CONFIG --username gjzhao --password gjz123 --no-auth-cache --trust-server-cert
	
	if [[ -e $LOCAL_PATH/${Build_Shell_Path##*/}/Local_Source_Path ]];then
	   rm -rf $LOCAL_PATH/${Build_Shell_Path##*/}/Local_Source_Path
	fi
	mkdir -p $LOCAL_PATH/${Build_Shell_Path##*/}/Local_Source_Path
	$SVN_CMD checkout $Jenkins_Source_Path $LOCAL_PATH/${Build_Shell_Path##*/}/Local_Source_Path --username gjzhao --password gjz123 --no-auth-cache --trust-server-cert
	
	$LOCAL_PATH/${Build_Shell_Path##*/}/readXMLFile.sh Qnx $projectname Install $isBuildSecure $isBuildXuGao
	$LOCAL_PATH/${Build_Shell_Path##*/}/readXMLFile.sh Package $projectname
	if [[ $? -eq 0 ]];then
	    if [[ ${projectname} =~ _Release ]];then
	       ReleaseVersion=`$LOCAL_PATH/${Build_Shell_Path##*/}/readXMLFile.sh Qnx ${projectname} Release Version`
	       echo "read ${Build_Shell_Path##*/}_Qnx.xml ReleaseVersion = $ReleaseVersion"
	       sed -i "s/ReleaseVersion/"$ReleaseVersion"/g" $LOCAL_PATH/${Build_Shell_Path##*/}/"QNX_Source_"${projectname}.sh
	    fi
	
	    mv $LOCAL_PATH/${Build_Shell_Path##*/}/"QNX_Source_"${projectname}.sh $LOCAL_PATH/${Build_Shell_Path##*/}/Local_Source_Path
	    mv $LOCAL_PATH/${Build_Shell_Path##*/}/"QNX_Source_"${projectname}"_Remove".sh $LOCAL_PATH/${Build_Shell_Path##*/}/Local_Source_Path
	    mv $LOCAL_PATH/${Build_Shell_Path##*/}/"Package_Source_"${projectname}.sh $LOCAL_PATH/${Build_Shell_Path##*/}/Local_Source_Path
	
	    $SVN_CMD add $LOCAL_PATH/${Build_Shell_Path##*/}/Local_Source_Path/* --no-ignore --force --username gjzhao --password gjz123 --no-auth-cache --trust-server-cert
        $SVN_CMD commit -m "${projectname%%_*} ${projectversion} QNX_Source_${projectname}.sh or QNX_Source_${projectname}_Remove.sh Package_Source_${projectname}.sh Upload Success !!!" $LOCAL_PATH/${Build_Shell_Path##*/}/Local_Source_Path --username gjzhao --password gjz123 --no-auth-cache --trust-server-cert
	    if [ $? != 0 ]; then
	        prompt "write_source_shell QNX_Source_Shell failed!"
	        _build_error_exit $RUNING_ERROR_PARAM
	    fi
    else
	    echo "QNX XML Config didnot exit ,inload source shell whitch shell add svn by myself !!!"
	    if [[ ! -f $LOCAL_PATH/${Build_Shell_Path##*/}/Local_Source_Path/QNX_Source_${projectname}.sh ]];then
		    echo "QNX_Source_${projectname}.sh shell whitch add svn by myself didnt exit , inload QNX_Source_${projectname%%_*}.sh shell !! "
		    cp -rf $LOCAL_PATH/${Build_Shell_Path##*/}/Local_Source_Path/QNX_Source_${projectname%%_*}.sh $LOCAL_PATH/${Build_Shell_Path##*/}/Local_Source_Path/QNX_Source_${projectname}.sh
			if [[ $? -ne 0 ]];then
			    echo "$LOCAL_PATH/${Build_Shell_Path##*/}/Local_Source_Path/QNX_Source_${projectname}.sh is not exitst !!!!! exit $RUNING_ERROR_PARAM!"
		        _build_error_exit $RUNING_ERROR_PARAM
			fi
		fi
	fi
	
	source $LOCAL_PATH/${Build_Shell_Path##*/}/Local_Source_Path/QNX_Source_${projectname}.sh
    source $LOCAL_PATH/${Build_Shell_Path##*/}/Local_Source_Path/QNX_Source_${projectname}_Remove.sh
	source $LOCAL_PATH/${Build_Shell_Path##*/}/Local_Source_Path/Package_Source_${projectname}.sh
	
	if [[ ! -d $LOCAL_PATH/SVN_CONFIG/${projectname} ]];then
	    mkdir -p $LOCAL_PATH/SVN_CONFIG/${projectname}
	fi
	
    if [[ ! -f $LOCAL_PATH/SVN_CONFIG/${projectname}/${projectversion}.cfg ]];then
        touch $LOCAL_PATH/SVN_CONFIG/${projectname}/${projectversion}.cfg
        write_config $LOCAL_PATH/SVN_CONFIG/${projectname}/${projectversion}.cfg
	    if [ $? != 0 ]; then
		    prompt "write_config ${projectversion}.cfg failed!"
		    myexit 1
	    fi
        $SVN_CMD add $LOCAL_PATH/SVN_CONFIG/* --no-ignore --force --username gjzhao --password gjz123 --no-auth-cache --trust-server-cert
        $SVN_CMD commit -m "${projectname} ${projectversion} build config Upload Success !!!" $LOCAL_PATH/SVN_CONFIG --username gjzhao --password gjz123 --no-auth-cache --trust-server-cert
	    if [[ $? -ne 0 ]];then
	        echo "QNX SVN Upload Config File ERROR ,exit 2 !!!!! "
	        _build_error_exit $RUNING_ERROR_PARAM
	    fi
    else
		rm -rf $LOCAL_PATH/SVN_CONFIG/${projectname}/${projectversion}.cfg
		touch $LOCAL_PATH/SVN_CONFIG/${projectname}/${projectversion}.cfg
		write_config $LOCAL_PATH/SVN_CONFIG/${projectname}/${projectversion}.cfg
		if [ $? -ne 0 ]; then
		   prompt "write_config ${projectversion}.cfg failed!"
		   _build_error_exit $RUNING_ERROR_PARAM
		fi
		$SVN_CMD add $LOCAL_PATH/SVN_CONFIG/* --no-ignore --force --username gjzhao --password gjz123 --no-auth-cache --trust-server-cert
		$SVN_CMD commit -m "${projectname} ${projectversion} build config Update Success !!!" $LOCAL_PATH/SVN_CONFIG --username gjzhao --password gjz123 --no-auth-cache --trust-server-cert
		if [[ $? -ne 0 ]];then
		   echo "QNX SVN Update Config File ERROR ,exit 2 !!!!! "
		   _build_error_exit $RUNING_ERROR_PARAM
		fi
    fi
	
	
	if [[ ${ISSRCTAG} = "0" ]];then
		_export_project_code
		echo "${ISSRCTAG}"
	elif [[ ${ISSRCTAG} = "1" ]];then
		MAKETAG=1
		_svn_make_tag
		_export_project_code				
    elif [[ ${ISSRCTAG} = "2" ]];then
		READTAG=1
		if [[ ${isBuildSecure} = "1" ]];then
			SVN_SOURCE_TAG_PROJ_HPY=${GLOBAL_SVN_SOURCE_TAG_MAIN_HYP}/${projectname}"_Tag-"${SRCTAG_VERSION}_safety
		else
			SVN_SOURCE_TAG_PROJ_HPY=${GLOBAL_SVN_SOURCE_TAG_MAIN_HYP}/${projectname}"_Tag-"${SRCTAG_VERSION}
		fi
		_export_project_code
	else
	    echo "Build Qnx Choose Type is inVailed ,please input from 0 to 2 !!!!! "
		_build_error_exit $RUNING_ERROR_PARAM
	fi
}

set_config_versions ()
{	
    async_build_inspect
	
	FILE=$LOCAL_PATH/SVN_PUBLISH/base/fyapps/etc/mpu.version
	if [[ $projectname =~ 535B ]];then
		cubProjetctName=${projectname%%_535B*}
		cubProjetctName=${cubProjetctName}535B
	else
	    cubProjetctName=${projectname%%_*}
	fi
	
	line=`sed -n '/\<mainversion\>/=' $FILE`
	sed -i -e ${line}s"/.*/mainversion:	${updatePackage}/" $FILE

	line=`sed -n '/\<mainmodel\>/=' $FILE`
	sed -i -e ${line}s"/.*/mainmodel:	${cubProjetctName}/" $FILE
	
	line=`sed -n '/\<version\>/=' $FILE`
	sed -i -e ${line}s"/.*/version:	${SUB_VERSION}/" $FILE
	
	line=`sed -n '/\<ifs_version\>/=' $FILE`
	sed -i -e ${line}s"/.*/ifs_version:	${IfsVersion}/" $FILE
	
	line=`sed -n '/\<fyversion\>/=' $FILE`
	sed -i -e ${line}s"/.*/fyversion:	${projectversion}/" $FILE
	times=`date`
	
	cubTime=`date +%F" "%T`
	line=`sed -n '/\<date\>/=' $FILE`
	sed -i -e ${line}s"/.*/date:${cubTime}/" $FILE
	
	line=`sed -n '/\<svn_tag\>/=' $FILE`
	if [[ $line != "" ]];then
	    echo "write svn_tag = $mpuversion_svntag to mpu.version"
	    sed -i -e ${line}s"/.*/svn_tag:${mpuversion_svntag}/" $FILE
	fi
}

_build ()
{ 
    async_build_inspect
	
    echo "Begin Build QNX Code and Make Image !!!!!!!"`date +%F" "%T`
    cd $LOCAL_PATH/$QualcommPathName
  
    if [ -d /usr/dtc_1.4.6 ]; then
       export PATH=/usr/dtc_1.4.6:$PATH
    fi
 
    rm -f $Qualcomm8155BashPath/${projectname}_Package/QNX_Build_Error_*.log
    cd $LOCALQUA_BSP_PATH/apps/qnx_ap
    source setenv_64.sh --external $LOCALQNX_SDP_PATH
    make clean
	async_build_inspect
	
    if [[ $coverity_scan = "true" ]]; then
        # 软安静兮代码扫描配置、在之前的coverity扫描上进行修改
		${RA_license}/nuw-config --qnx 
        if [[ -d $Qualcomm8155BashPath/${projectname}_Package/Coverity_Build ]];then
	        rm -rf $Qualcomm8155BashPath/${projectname}_Package/Coverity_Build
	        mkdir -p $Qualcomm8155BashPath/${projectname}_Package/Coverity_Build
	    fi

        ${RA_license}/nuw-build --dir $Qualcomm8155BashPath/${projectname}_Package/Coverity_Build make all 2>$Qualcomm8155BashPath/${projectname}_Package/QNX_Build_Error_${build_paramNum}.log
	    result=$?
		${RA_license}/nuw-analyze --dir $Qualcomm8155BashPath/${projectname}_Package/Coverity_Build 
		${RA_license}/nuw-commit-error --dir $Qualcomm8155BashPath/${projectname}_Package/Coverity_Build --host *********** --port 9900 --access-token `cat ${RA_token}` --project $PROJECT_NAME --project-version $PROJECT_VERSION
    else
	    make all 2>$Qualcomm8155BashPath/${projectname}_Package/QNX_Build_Error_${build_paramNum}.log
	    result=$?
    fi
    
	echo "End Build QNX Code and Make Image !!!!!!!"`date +%F" "%T`
    echo "Build Qnx Over!!!! Make result is "$result
    if [ $result -ne 0  ]; then
	    buildErrorModule=`cat QNX_Build_Error_${build_paramNum}.log grep -E 'failed|error'`
		if [[ $buildErrorModule =~ .*${ERROR_BUILD_MODULE_MPU_PATH}.* ]];then
		    echo "QNX MPU Module Build Error ....."
			RUNING_ERROR_PARAM=$ERROR_PARAM_BUILD_MPU
		    echo $buildErrorModule | awk -F ${ERROR_BUILD_MODULE_MPU_PATH} '{print $2}' | awk -F "/" '{print $3}' > $Qualcomm8155BashPath/${projectname}_Package/QNX_ERROR_MODULE
		    echo "模块报错" >> $Qualcomm8155BashPath/${projectname}_Package/QNX_ERROR_MODULE
		elif [[ $buildErrorModule =~ .*${ERROR_BUILD_MODULE_BSP_PATH}.* ]];then
		    echo "QNX BSP Module Build Error ....."
			RUNING_ERROR_PARAM=$ERROR_PARAM_BUILD_BSP
		    echo $buildErrorModule | awk -F ${ERROR_BUILD_MODULE_MPU_PATH} '{print $2}' | awk -F "/" '{print $2}' > $Qualcomm8155BashPath/${projectname}_Package/QNX_ERROR_MODULE
		    echo "模块报错" >> $Qualcomm8155BashPath/${projectname}_Package/QNX_ERROR_MODULE
		else
		    echo "QNX Other Module Build Error ....."
			RUNING_ERROR_PARAM=$ERROR_PARAM_BUILD_BSP
			echo "未知模块报错" >> $Qualcomm8155BashPath/${projectname}_Package/QNX_ERROR_MODULE
		fi
	    echo "Build Qnx ERROR !!!!!!!!"
	    _build_error_exit $RUNING_ERROR_PARAM
    else
	    if [[ -f $Qualcomm8155BashPath/${projectname}_Package/QNX_ERROR_MODULE ]];then
		    rm -f $Qualcomm8155BashPath/${projectname}_Package/QNX_ERROR_MODULE
		fi
		rm -f $Qualcomm8155BashPath/${projectname}_Package/QNX_Build_Error_${build_paramNum}.log
        mkdir -p $LOCALQUA_BSP_PATH/apps/qnx_ap/target/hypervisor/host/out_8155/update/$projectversion
	    for v in "${GLOBAL_PROJECT_DEF_PACKPATH[@]}"
        do
            package_path=($v)
	        if [[ ${package_path[${PACKAGE_MODULE_NAME}]} = "QNX" ]];then
		        echo "Qnx Package ${package_path[${PACKAGE_MODULE_NAME}]} from ${package_path[${PACKAGE_MODULE_PATH}]}"
		        qnxImgPath=${LOCAL_PATH}/${package_path[${PACKAGE_MODULE_PATH}]}
		        if [[ ${qnxImgPath##*/} =~ \{ ]] && [[ ${qnxImgPath##*/} =~ \} ]] && [[ ! ${qnxImgPath%/*} =~ \{ ]];then
                    qnxImgPath2=`echo ${qnxImgPath##*/} | awk -F '{' '{print $2}' | awk -F '}' '{print $1}'`
                    qnxImgArr=(${qnxImgPath2//,/ })
                    for arri in ${qnxImgArr[@]}
                    do
			           echo "Qnx Package Output1 --> cp -rf ${qnxImgPath%/*}/${arri} $LOCALQUA_BSP_PATH/apps/qnx_ap/target/hypervisor/host/out_8155/update/$projectversion"
			           cp -rf ${qnxImgPath%/*}/${arri} $LOCALQUA_BSP_PATH/apps/qnx_ap/target/hypervisor/host/out_8155/update/$projectversion
                    done
                else
		            echo "Qnx Package Output2 -->  cp -rf ${qnxImgPath} $LOCALQUA_BSP_PATH/apps/qnx_ap/target/hypervisor/host/out_8155/update/$projectversion"
                    cp -rf ${qnxImgPath} $LOCALQUA_BSP_PATH/apps/qnx_ap/target/hypervisor/host/out_8155/update/$projectversion
                fi	
            fi
        done
	 
	    cd $LOCALQUA_BSP_PATH/apps/qnx_ap/target/hypervisor/host/out_8155/update/$projectversion
		RUNING_ERROR_PARAM=$ERROR_PARAM_PACKAGE
	    tar -czf QNX.tar.gz *
		if [[ $? -ne 0 ]];then
		    _build_error_exit $RUNING_ERROR_PARAM
		fi
	 
	    cp $LOCALQUA_BSP_PATH/apps/qnx_ap/target/hypervisor/host/out_8155/update/$projectversion/QNX.tar.gz $Qualcomm8155BashPath/${projectname}_Package/Update
	    rsync -rv --exclude='QNX.tar.gz' $LOCALQUA_BSP_PATH/apps/qnx_ap/target/hypervisor/host/out_8155/update/$projectversion/* $Qualcomm8155BashPath/${projectname}_Package/Image/Qnx
		if [[ $projectname =~ "HS7012A" ]] || [[ $projectname =~ "HS7023A" ]] || [[ $projectname =~ "HS7029A" ]];then
			rsync -rv --exclude='QNX.tar.gz' $LOCALQUA_BSP_PATH/apps/qnx_ap/target/hypervisor/host/out_8155/update/$projectversion/* $Qualcomm8155BashPath/${projectname}_Package/OTA/Qnx
		fi
    fi	
    _build_amss
}

_build_amss ()
{
    async_build_inspect
  #选择编AMSS UFS烧录镜像 编译OTA时，编译AMSS
  #if [[ $build_amss == "1" ]] || [[ $isBuildUFS == "1" ]] || [[ $buildType == "3" ]];then
    echo "Begin Build AMSS !!!"`date +%F" "%T`
	RUNING_ERROR_PARAM=$ERROR_PARAM_BUILD_AMSS
	
    if [[ -f $LOCALQUA_AMSS_PATH.tar ]];then
	   rm -rf $LOCALQUA_AMSS_PATH.tar
	fi
  
	cd $LOCALQUA_AMSS_PATH
	
	PATH=$FGE_PATH
    JAVA_HOME=$FGE_JAVA_HOME
	
	if [[ ! -f $LOCALQUA_BSP_PATH/apps/qnx_ap/target/hypervisor/host/out_8155/mifs_hyp_la.img ]];then
       cp $LOCALQUA_BSP_PATH/apps/qnx_ap/target/hypervisor/host/out_8155/signed/default/qhee/mifs_hyp_la.img $LOCALQUA_BSP_PATH/apps/qnx_ap/target/hypervisor/host/out_8155/
    fi
	
	python ./adsp_8155/adsp_proc/build/build.py -c sm8150 -o all -f ADSP
    python ./cdsp_8155/cdsp_proc/build/build.py -c sm8150 -o all -f CDSP
    python ./boot_8155/boot_images/QcomPkg/buildex.py --variant AU -r RELEASE -t SDM855Pkg,QcomToolsPkg
    python ./tz_8155/trustzone_images/build/ms/build_all.py -b TZ.XF.5.0 CHIPSET=sm8150
	
    #build aop
    cd aop_8155
    ./aop_proc/build/build_855au.sh
	
	if [[ $? -ne 0 ]]; then
	   echo "build amss ERROR !!!!!!!!"
	   _build_error_exit $RUNING_ERROR_PARAM
    fi
	echo "End Build AMSS !!!"`date +%F" "%T`
	async_build_inspect
	
	RUNING_ERROR_PARAM=$ERROR_PARAM_AMSS_Meta
	if [[ $android_SubPid -ne -1 ]];then
	    echo "Android sub process didnt make over ,wait and begin amss meta build after andoird sub PID $android_SubPid over ......" 
		tail --pid=$android_SubPid -f /dev/null
	fi
	
	cd $LOCALQUA_AMSS_PATH
	#拷贝qnx产物
	rm -rf apps
	
	mkdir -p ./apps/qnx_ap/target/hypervisor/host/abl-image/signed/default/abl
	cp -f $LOCALQUA_BSP_PATH/apps/qnx_ap/target/hypervisor/host/abl-image/signed/default/abl/abl_fastboot.elf ./apps/qnx_ap/target/hypervisor/host/abl-image/signed/default/abl

    mkdir -p ./apps/qnx_ap/target/hypervisor/host/out_8155/signed/default/qhee
    cp -f $LOCALQUA_BSP_PATH/apps/qnx_ap/target/hypervisor/host/out_8155/signed/default/qhee/mifs_hyp_la.img ./apps/qnx_ap/target/hypervisor/host/out_8155/signed/default/qhee
    cp -f $LOCALQUA_BSP_PATH/apps/qnx_ap/target/hypervisor/host/out_8155/{boot_la.img,ifs2_la.img,mifs_hyp_la.img,persist.img,persist_qnx.img,system_la.img,system_la.img.sparse,qnxsota.img,share.img}	./apps/qnx_ap/target/hypervisor/host/out_8155/
	
    #拷贝安卓产物
	rm -rf ./lagvm/LINUX/android
	mkdir -p ./lagvm/LINUX/android/out/target/product/msmnile_gvmq/obj/KERNEL_OBJ
	cp -f $Qualcomm8155BashPath/${projectname}_Package/output/Android/vmlinux ./lagvm/LINUX/android/out/target/product/msmnile_gvmq/obj/KERNEL_OBJ/
	cp -f $Qualcomm8155BashPath/${projectname}_Package/Image/Android/*  ./lagvm/LINUX/android/out/target/product/msmnile_gvmq/
	
    #meta build
	async_build_inspect
	echo "Begin Build AMSS Meta build !!!"`date +%F" "%T`
    cd common/build
    ./build.py --flavors=8155_la
	result=$?
	echo "End Build AMSS Meta build !!!"`date +%F" "%T`
	echo "Build AMSS Meta build Over !!!! Make result is "$result

    if [ $result -ne 0  ]; then
	   echo "=========================================="
	   echo "AMSS Meta build failed with exit code: $result"
	   echo "This may be due to missing super.img file"
	   echo "Check the build log above for checksparse.py errors"
	   echo "=========================================="
	   _build_error_exit $RUNING_ERROR_PARAM
    fi
	
    cd ../..
    #build cdt
    cd boot_8155/boot_images/QcomPkg/Tools/
    python cdt_generator.py cdp_0_1.0.xml cdt_8155.bin
	
	RUNING_ERROR_PARAM=$ERROR_PARAM_CLEAN
	cd $LOCALQUA_AMSS_PATH
	mkdir -p $LOCALQUA_AMSS_PATH/$projectversion
	for a in "${GLOBAL_PROJECT_DEF_PACKPATH[@]}"
    do
        package_path=($a)
	    if [[ ${package_path[${PACKAGE_MODULE_NAME}]} = "AMSS" ]];then
			echo "AMSS Package ${package_path[${PACKAGE_MODULE_NAME}]} from ${package_path[${PACKAGE_MODULE_PATH}]}"
		    amssImgPath=${LOCAL_PATH}/${package_path[${PACKAGE_MODULE_PATH}]}
		    if [[ ${amssImgPath##*/} =~ \{ ]] && [[ ${amssImgPath##*/} =~ \} ]] && [[ ! ${amssImgPath%/*} =~ \{ ]];then
                amssImgPath2=`echo ${amssImgPath##*/} | awk -F '{' '{print $2}' | awk -F '}' '{print $1}'`
                amssImgArr=(${amssImgPath2//,/ })
                for arri in ${amssImgArr[@]}
                do
			       echo "AMSS Package Output1 --> cp -rf ${amssImgPath%/*}/${arri} $LOCALQUA_AMSS_PATH/$projectversion"
			       cp -rf ${amssImgPath%/*}/${arri} $LOCALQUA_AMSS_PATH/$projectversion
                done
            else
			    if [[ ${amssImgPath%/*} =~ \{ ]];then
				    echo "AMSS Pakage XML Fail format error !!!!"
				    _build_error_exit $RUNING_ERROR_PARAM
				fi
		        echo "AMSS Package Output2 -->  cp -rf ${amssImgPath} $LOCALQUA_AMSS_PATH/$projectversion"
                cp -rf ${amssImgPath} $LOCALQUA_AMSS_PATH/$projectversion
            fi
		elif [[ ${package_path[${PACKAGE_MODULE_NAME}]} = "QNX" ]];then
			echo "Qnx Package ${package_path[${PACKAGE_MODULE_NAME}]} from ${package_path[${PACKAGE_MODULE_PATH}]}"
		    qnxImgPath=${LOCAL_PATH}/${package_path[${PACKAGE_MODULE_PATH}]}
		    if [[ ${qnxImgPath##*/} =~ \{ ]] && [[ ${qnxImgPath##*/} =~ \} ]] && [[ ! ${qnxImgPath%/*} =~ \{ ]];then
                qnxImgPath2=`echo ${qnxImgPath##*/} | awk -F '{' '{print $2}' | awk -F '}' '{print $1}'`
                qnxImgArr=(${qnxImgPath2//,/ })
                for arri in ${qnxImgArr[@]}
                do
					echo "Qnx Package Output1 --> cp -rf ${qnxImgPath%/*}/${arri} $LOCALQUA_BSP_PATH/apps/qnx_ap/target/hypervisor/host/out_8155/update/$projectversion"
					cp -rf ${qnxImgPath%/*}/${arri} $LOCALQUA_BSP_PATH/apps/qnx_ap/target/hypervisor/host/out_8155/update/$projectversion
                done
            else
			    if [[ ${qnxImgPath%/*} =~ \{ ]];then
				    echo "Qnx Pakage XML Fail format error !!!!"
				    _build_error_exit $RUNING_ERROR_PARAM
				fi
		        echo "Qnx Package Output2 -->  cp -rf ${qnxImgPath} $LOCALQUA_BSP_PATH/apps/qnx_ap/target/hypervisor/host/out_8155/update/$projectversion"
                cp -rf ${qnxImgPath} $LOCALQUA_BSP_PATH/apps/qnx_ap/target/hypervisor/host/out_8155/update/$projectversion
            fi
        else
		    echo "Nothing Packgae to move !!!!"
        fi
    done
	cp -f $LOCAL_PATH/SVN_PUBLISH/base/scripts/amss.sh $LOCALQUA_AMSS_PATH/$projectversion
	
	cd $LOCALQUA_BSP_PATH/apps/qnx_ap/target/hypervisor/host/out_8155/update/$projectversion
	rm -rf $LOCALQUA_BSP_PATH/apps/qnx_ap/target/hypervisor/host/out_8155/update/$projectversion/QNX.tar.gz
	tar -czf QNX.tar.gz *
	 
	cp $LOCALQUA_BSP_PATH/apps/qnx_ap/target/hypervisor/host/out_8155/update/$projectversion/QNX.tar.gz $Qualcomm8155BashPath/${projectname}_Package/Update
	rsync -rv --exclude='QNX.tar.gz' $LOCALQUA_BSP_PATH/apps/qnx_ap/target/hypervisor/host/out_8155/update/$projectversion/* $Qualcomm8155BashPath/${projectname}_Package/Image/Qnx
	if [[ $projectname =~ "HS7012A" ]] || [[ $projectname =~ "HS7023A" ]] || [[ $projectname =~ "HS7029A" ]];then
		rsync -rv --exclude='QNX.tar.gz' $LOCALQUA_BSP_PATH/apps/qnx_ap/target/hypervisor/host/out_8155/update/$projectversion/* $Qualcomm8155BashPath/${projectname}_Package/OTA/Qnx
	fi
	
	cd $LOCALQUA_AMSS_PATH/$projectversion  
    tar -czf AMSS.tar.gz *
	cp $LOCALQUA_AMSS_PATH/$projectversion/AMSS.tar.gz $Qualcomm8155BashPath/${projectname}_Package/Update
	rsync -rv --exclude='AMSS.tar.gz' $LOCALQUA_AMSS_PATH/$projectversion/* $Qualcomm8155BashPath/${projectname}_Package/Image/AMSS
	if [[ $projectname =~ "HS7012A" ]] || [[ $projectname =~ "HS7023A" ]] || [[ $projectname =~ "HS7029A" ]];then
		cp -f $LOCALQUA_AMSS_PATH/$projectversion/NON-HLOS.bin $Qualcomm8155BashPath/${projectname}_Package/OTA/AMSS
		cp -f $LOCALQUA_BSP_PATH/apps/qnx_ap/target/hypervisor/host/out_8155/share.img	$Qualcomm8155BashPath/${projectname}_Package/Image/Qnx
	fi
	
	if [[ $? -ne 0 ]]; then
	    echo "copy and remove QNX build Image Error, please ensure sufficient space !!!!!!!!"
	    _build_error_exit $RUNING_ERROR_PARAM
    fi
  #fi
}

secure_boot_make ()
{
	RUNING_ERROR_PARAM=$ERROR_PARAM_BUILD_AMSS
	
	if [[ -f $LOCALQUA_AMSS_PATH.tar ]];then
	   rm -rf $LOCALQUA_AMSS_PATH.tar
	fi
	
    cd $LOCALQUA_AMSS_PATH
	echo "LOCALQUA_AMSS_PATH $PWD"
	cp -rf /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/SecureBoot/amss/* $LOCALQUA_AMSS_PATH

	#第一次AMSS
	export OEM_ROOT=${LOCALQUA_AMSS_PATH}/adsp_8155/adsp_proc/hap/oem
    python ./adsp_8155/adsp_proc/build/build.py -c sm8150 -o all -f ADSP
	if [ $? -ne 0 ]; then
		echo " ===> build.py -c sm8150 -o all -f ADSP fail ???"
	    _build_error_exit $RUNING_ERROR_PARAM
	fi
	
	export OEM_ROOT=${LOCALQUA_AMSS_PATH}/cdsp_8155/cdsp_proc/hap/oem
	python ./cdsp_8155/cdsp_proc/build/build.py -c sm8150 -o all -f CDSP
	if [ $? -ne 0 ]; then
		echo " ===> build.py -c sm8150 -o all -f CDSP fail ???"
	    _build_error_exit $RUNING_ERROR_PARAM
	fi
	python ./boot_8155/boot_images/QcomPkg/buildex.py --variant AU -r RELEASE -t SDM855Pkg,QcomToolsPkg
	if [ $? -ne 0 ]; then
		echo " ===> buildex.py --variant AU  fail ???"
	    _build_error_exit $RUNING_ERROR_PARAM
	fi
	python ./tz_8155/trustzone_images/build/ms/build_all.py -b TZ.XF.5.0 CHIPSET=sm8150
	if [ $? -ne 0 ]; then
		echo " ===> build_all.py -b TZ.XF.5.0  fail ???"
	    _build_error_exit $RUNING_ERROR_PARAM
	fi

	#build aop
	cd aop_8155
	echo "aop_8155 $PWD"
	./aop_proc/build/build_855au.sh    
	if [[ $? -ne 0 ]]; then
	   echo "build amss ERROR !!!!!!!!"
	   _build_error_exit $RUNING_ERROR_PARAM
    fi
	cd ../
	echo "after aop_8155 $PWD"
	
	#编QNX
	PATH=$FGE_PATH
	echo "FGE_PATH $PATH"
    JAVA_HOME=$FGE_JAVA_HOME
	
	echo "===>secure_boot_make   make qnx ---->>"
	rm -f $Qualcomm8155BashPath/${projectname}_Package/QNX_Build_Error_*.log
	cd  $LOCALQUA_BSP_PATH/apps/qnx_ap
	echo "FGE_PATH $PWD"
	source setenv_64.sh --external $LOCALQNX_SDP_PATH
	make clean 
	async_build_inspect
	
	if [[ $coverity_scan = "true" ]]; then
		if [[ -e $Qualcomm8155BashPath/${projectname}_Package/Coverity_Build ]];then
			rm -rf $Qualcomm8155BashPath/${projectname}_Package/Coverity_Build
			mkdir -p $Qualcomm8155BashPath/${projectname}_Package/Coverity_Build
		fi
		$COVBUILD  --dir $OUTPUT --add-arg --c++11 --add-arg -D__QNX_CPP11__ make all 2>$Qualcomm8155BashPath/${projectname}_Package/QNX_Build_Error_${build_paramNum}.log
		result=$?
    else
	    make all 2>$Qualcomm8155BashPath/${projectname}_Package/QNX_Build_Error_${build_paramNum}.log
	    result=$?
    fi
	
	echo "End Build QNX Code and Make Image !!!!!!!"`date +%F" "%T`
    echo "Build Qnx Over!!!! Make result is "$result
    if [ $result -ne 0  ]; then
	    buildErrorModule=`cat QNX_Build_Error_${build_paramNum}.log grep -E 'failed|error'`
		if [[ $buildErrorModule =~ .*${ERROR_BUILD_MODULE_MPU_PATH}.* ]];then
		    echo "QNX MPU Module Build Error ....."
			RUNING_ERROR_PARAM=$ERROR_PARAM_BUILD_MPU
		    echo $buildErrorModule | awk -F ${ERROR_BUILD_MODULE_MPU_PATH} '{print $2}' | awk -F "/" '{print $3}' > $Qualcomm8155BashPath/${projectname}_Package/QNX_ERROR_MODULE
		    echo "模块报错" >> $Qualcomm8155BashPath/${projectname}_Package/QNX_ERROR_MODULE
		elif [[ $buildErrorModule =~ .*${ERROR_BUILD_MODULE_BSP_PATH}.* ]];then
		    echo "QNX BSP Module Build Error ....."
			RUNING_ERROR_PARAM=$ERROR_PARAM_BUILD_BSP
		    echo $buildErrorModule | awk -F ${ERROR_BUILD_MODULE_MPU_PATH} '{print $2}' | awk -F "/" '{print $2}' > $Qualcomm8155BashPath/${projectname}_Package/QNX_ERROR_MODULE
		    echo "模块报错" >> $Qualcomm8155BashPath/${projectname}_Package/QNX_ERROR_MODULE
		else
		    echo "QNX Other Module Build Error ....."
			RUNING_ERROR_PARAM=$ERROR_PARAM_BUILD_BSP
			echo "未知模块报错" >> $Qualcomm8155BashPath/${projectname}_Package/QNX_ERROR_MODULE
		fi
	    echo "Build Qnx ERROR !!!!!!!!"
	    _build_error_exit $RUNING_ERROR_PARAM
    else
	    if [[ -f $Qualcomm8155BashPath/${projectname}_Package/QNX_ERROR_MODULE ]];then
		    rm -f $Qualcomm8155BashPath/${projectname}_Package/QNX_ERROR_MODULE
		fi
		rm -f $Qualcomm8155BashPath/${projectname}_Package/QNX_Build_Error_${build_paramNum}.log
    fi	
	

	PATH=$FGE_PATH
	echo "after build FGE_PATH $PATH"
    JAVA_HOME=$FGE_JAVA_HOME
	
	RUNING_ERROR_PARAM=$ERROR_PARAM_AMSS_Meta
	if [[ $android_SubPid -ne -1 ]];then
	    echo "Android sub process didnt make over ,wait and begin amss meta build after andoird sub PID $android_SubPid over ......" 
		tail --pid=$android_SubPid -f /dev/null
	fi
	
	#meta build
	async_build_inspect
	echo "Begin Build AMSS Meta build !!!"`date +%F" "%T`
	cd $LOCALQUA_AMSS_PATH
	echo "LOCALQUA_AMSS_PATH $PWD"
    cd common/build
	echo "common/build $PWD"
    ./build.py --flavors=8155_la
	result=$?
	echo "End Build AMSS Meta build !!!"`date +%F" "%T`
    if [ $result -ne 0  ]; then
	   echo "build amss meta build ERROR !!!!!!!!"
	   _build_error_exit $RUNING_ERROR_PARAM
    fi
	cd ../../
	echo "after build amss $PWD"
	
	#镜像签名
	cd common/sectools
	echo "common/sectools $PWD"
	rm -rf secdatdir signedMeta signedFmw
	python sectools.py fuseblower -p sm8150 -gad --sign -o secdatdir -v
	if [ $? -ne 0 ]; then
		echo " ===> sectools.py fuseblower  fail ???"
	    _build_error_exit $RUNING_ERROR_PARAM
	fi
	python sectools.py secimage -m ../../ -sad -p sm8150 -o signedMeta -v
	if [ $? -ne 0 ]; then
		echo " ===> sectools.py secimage  fail ???"
	    _build_error_exit $RUNING_ERROR_PARAM
	fi
	
	python sectools.py secimage -i ../build/bin/8155_la/multi_image.mbn -s -p sm8150 -o signedFmw
	if [ $? -ne 0 ]; then
		echo " ===> sectools.py secimage multi_image.mbn fail ???"
	    _build_error_exit $RUNING_ERROR_PARAM
	fi
	
	python sectools.py secimage -i ../../tz_8155/trustzone_images/build/ms/bin/YAQAANAA/km4.mbn -s -p sm8150 -o signedFmw
	if [ $? -ne 0 ]; then
		echo " ===> sectools.py secimage km4.mbn  fail ???"
	    _build_error_exit $RUNING_ERROR_PARAM
	fi
	
	#QNX编译依赖AMSS签名后的镜像
	cd signedMeta/sm8150 
	echo "signedMeta/sm8150  $PWD"
	cp  -f cdsp/cdsp.m*  $LOCALQUA_BSP_PATH/apps/qnx_ap/prebuilt_NHLOS/cdsp/asic_8150/
	cp  -f adsp/adsp.m*  $LOCALQUA_BSP_PATH/apps/qnx_ap/prebuilt_NHLOS/lpass/asic_8150/
	cp  -f modem/modem.mbn  $LOCALQUA_BSP_PATH/apps/qnx_ap/prebuilt_NHLOS/mpss/asic_8150/qdsp6sw.mbn
	cp  -f modem/modem.mdt  $LOCALQUA_BSP_PATH/apps/qnx_ap/prebuilt_NHLOS/mpss/asic_8150/modem.mdt
	cp  -f npu/npu.m*  $LOCALQUA_BSP_PATH/apps/qnx_ap/prebuilt_NHLOS/npu/asic_8150/
	cp  -f qupv3/qupv3fw.elf  $LOCALQUA_BSP_PATH/apps/qnx_ap/prebuilt_NHLOS/qup/asic_8150/
	cp  -f venus/venus.m*  $LOCALQUA_BSP_PATH/apps/qnx_ap/prebuilt_NHLOS/venus/asic_8150/
	cp  -f gfx_microcode/a640_zap.*  $LOCALQUA_BSP_PATH/apps/qnx_ap/prebuilt/aarch64le/usr/lib/graphics/qc/a6xx/
	

	PATH=$FGE_PATH
	echo "FGE_PATH  $PATH"
    JAVA_HOME=$FGE_JAVA_HOME
	
	echo "===>secure_boot_make   make qnx 2 ---->>"
	cd  $LOCALQUA_BSP_PATH/apps/qnx_ap
	echo "apps/qnx_ap  $PWD"
	source setenv_64.sh --external $LOCALQNX_SDP_PATH
	make clean 
	make all 2>$Qualcomm8155BashPath/${projectname}_Package/QNX_Build_Error_${build_paramNum}.log
    result=$?
	if [ $result -ne 0  ]; then
	    buildErrorModule=`cat QNX_Build_Error_${build_paramNum}.log grep -E 'failed|error'`
		if [[ $buildErrorModule =~ .*${ERROR_BUILD_MODULE_MPU_PATH}.* ]];then
		    echo "QNX MPU Module Build Error ....."
			RUNING_ERROR_PARAM=$ERROR_PARAM_BUILD_MPU
		    echo $buildErrorModule | awk -F ${ERROR_BUILD_MODULE_MPU_PATH} '{print $2}' | awk -F "/" '{print $3}' > $Qualcomm8155BashPath/${projectname}_Package/QNX_ERROR_MODULE
		    echo "模块报错" >> $Qualcomm8155BashPath/${projectname}_Package/QNX_ERROR_MODULE
		elif [[ $buildErrorModule =~ .*${ERROR_BUILD_MODULE_BSP_PATH}.* ]];then
		    echo "QNX BSP Module Build Error ....."
			RUNING_ERROR_PARAM=$ERROR_PARAM_BUILD_BSP
		    echo $buildErrorModule | awk -F ${ERROR_BUILD_MODULE_MPU_PATH} '{print $2}' | awk -F "/" '{print $2}' > $Qualcomm8155BashPath/${projectname}_Package/QNX_ERROR_MODULE
		    echo "模块报错" >> $Qualcomm8155BashPath/${projectname}_Package/QNX_ERROR_MODULE
		else
		    echo "QNX Other Module Build Error ....."
			RUNING_ERROR_PARAM=$ERROR_PARAM_BUILD_BSP
			echo "未知模块报错" >> $Qualcomm8155BashPath/${projectname}_Package/QNX_ERROR_MODULE
		fi
	    echo "Build Qnx ERROR !!!!!!!!"
	    _build_error_exit $RUNING_ERROR_PARAM
    else
	    if [[ -f $Qualcomm8155BashPath/${projectname}_Package/QNX_ERROR_MODULE ]];then
		    rm -f $Qualcomm8155BashPath/${projectname}_Package/QNX_ERROR_MODULE
		fi
		rm -f $Qualcomm8155BashPath/${projectname}_Package/QNX_Build_Error_${build_paramNum}.log
        mkdir -p $LOCALQUA_BSP_PATH/apps/qnx_ap/target/hypervisor/host/out_8155/update/$projectversion
	    for v in "${GLOBAL_PROJECT_DEF_PACKPATH[@]}"
        do
            package_path=($v)
	        if [[ ${package_path[${PACKAGE_MODULE_NAME}]} = "QNX" ]];then
		        echo "Qnx Package ${package_path[${PACKAGE_MODULE_NAME}]} from ${package_path[${PACKAGE_MODULE_PATH}]}"
		        qnxImgPath=${LOCAL_PATH}/${package_path[${PACKAGE_MODULE_PATH}]}
		        if [[ ${qnxImgPath##*/} =~ \{ ]] && [[ ${qnxImgPath##*/} =~ \} ]] && [[ ! ${qnxImgPath%/*} =~ \{ ]];then
                    qnxImgPath2=`echo ${qnxImgPath##*/} | awk -F '{' '{print $2}' | awk -F '}' '{print $1}'`
                    qnxImgArr=(${qnxImgPath2//,/ })
                    for arri in ${qnxImgArr[@]}
                    do
			           echo "Qnx Package Output1 --> cp -rf ${qnxImgPath%/*}/${arri} $LOCALQUA_BSP_PATH/apps/qnx_ap/target/hypervisor/host/out_8155/update/$projectversion"
			           cp -rf ${qnxImgPath%/*}/${arri} $LOCALQUA_BSP_PATH/apps/qnx_ap/target/hypervisor/host/out_8155/update/$projectversion
                    done
                else
		            echo "Qnx Package Output2 -->  cp -rf ${qnxImgPath} $LOCALQUA_BSP_PATH/apps/qnx_ap/target/hypervisor/host/out_8155/update/$projectversion"
                    cp -rf ${qnxImgPath} $LOCALQUA_BSP_PATH/apps/qnx_ap/target/hypervisor/host/out_8155/update/$projectversion
                fi	
            fi
        done
	 
	    cd $LOCALQUA_BSP_PATH/apps/qnx_ap/target/hypervisor/host/out_8155/update/$projectversion
		echo "$projectversion  $PWD"
		RUNING_ERROR_PARAM=$ERROR_PARAM_PACKAGE
	    tar -czf QNX.tar.gz *
		if [[ $? -ne 0 ]];then
		    _build_error_exit $RUNING_ERROR_PARAM
		fi
	 
	    cp $LOCALQUA_BSP_PATH/apps/qnx_ap/target/hypervisor/host/out_8155/update/$projectversion/QNX.tar.gz $Qualcomm8155BashPath/${projectname}_Package/Update
	    rsync -rv --exclude='QNX.tar.gz' $LOCALQUA_BSP_PATH/apps/qnx_ap/target/hypervisor/host/out_8155/update/$projectversion/* $Qualcomm8155BashPath/${projectname}_Package/Image/Qnx
		if [[ $projectname =~ "HS7012A" ]] || [[ $projectname =~ "HS7023A" ]] || [[ $projectname =~ "HS7029A" ]];then
			rsync -rv --exclude='QNX.tar.gz' $LOCALQUA_BSP_PATH/apps/qnx_ap/target/hypervisor/host/out_8155/update/$projectversion/* $Qualcomm8155BashPath/${projectname}_Package/OTA/Qnx
		fi
    fi	
	
	if [ -d $LOCALQUA_AMSS_PATH/common/sectools/from_qnx ];then
		rm -rf $LOCALQUA_AMSS_PATH/common/sectools/from_qnx/*
	else
		mkdir -p $LOCALQUA_AMSS_PATH/common/sectools/from_qnx
	fi
	
	cd  $LOCALQUA_BSP_PATH/apps/qnx_ap
	cp  -f  target/hypervisor/host/abl-image/signed/default/abl/abl_fastboot.elf $LOCALQUA_AMSS_PATH/common/sectools/from_qnx/abl.elf
	cp  -f  target/hypervisor/host/out_8155/signed/default/qhee/mifs_hyp_la.img $LOCALQUA_AMSS_PATH/common/sectools/from_qnx/hyp.mbn
	sync
	
	PATH=$FGE_PATH
    JAVA_HOME=$FGE_JAVA_HOME
	
	#QNX镜像签名
	cd $LOCALQUA_AMSS_PATH/common/sectools/
	echo "common/sectools/  $PWD"
	python sectools.py secimage -i from_qnx/abl.elf -s -p sm8150 -o signedFmw
	if [ $? -ne 0 ]; then
		echo " ===> sectools.py secimage  from_qnx/abl.elf  fail ???"
	    _build_error_exit $RUNING_ERROR_PARAM
	fi
	
	python sectools.py secimage -i from_qnx/hyp.mbn -s -p sm8150 -o signedFmw
	if [ $? -ne 0 ]; then
		echo " ===> sectools.py secimage  from_qnx/hyp.mbn  fail ???"
	    _build_error_exit $RUNING_ERROR_PARAM
	fi
	
	cp -f signedFmw/sm8150/abl/abl.elf   $LOCALQUA_BSP_PATH/apps/qnx_ap/target/hypervisor/host/abl-image/abl_fastboot.elf 
	cp -f signedFmw/sm8150/abl/abl.elf   $LOCALQUA_BSP_PATH/apps/qnx_ap/target/hypervisor/host/abl-image/signed/default/abl/abl_fastboot.elf 
	cp -f signedFmw/sm8150/hyp/hyp.mbn   $LOCALQUA_BSP_PATH/apps/qnx_ap/target/hypervisor/host/out_8155/signed/default/qhee/mifs_hyp_la.img
	sync
	
	cp -f signedFmw/sm8150/multi_image/multi_image.mbn  ../build/bin/8155_la/multi_image/sm8150/multi_image/
	cp -f signedFmw/sm8150/keymaster/km4.mbn  ../../tz_8155/trustzone_images/build/ms/bin/YAQAANAA/km4virt.mbn 
	cp -f signedFmw/sm8150/keymaster/km4.mdt  ../../tz_8155/trustzone_images/build/ms/bin/YAQAANAA/km4virt.mdt 
	cp -f secdatdir/fuseblower_output/v2/sec.elf  resources/build/fileversion2/sec.dat

	cp ../build/ufs/8155_la/bin/NON-HLOS.bin ./

	if [ -f ./expect_qualcomm.sh  ];then 
	   rm -rf ./expect_qualcomm.sh
	fi
	if [ -f ./test.sh  ];then 
	   rm -rf ./test.sh
	fi
	 
	echo "#! /usr/local/bin/expect ">>./expect_qualcomm.sh 
	echo " ">>./expect_qualcomm.sh 
	echo "spawn ./test.sh km4virt/km4virt ">>./expect_qualcomm.sh 
	echo "expect \"(aArRoOsSq):\" ">>./expect_qualcomm.sh 
	echo "send \"O\n\" ">>./expect_qualcomm.sh 
	echo "expect off ">>./expect_qualcomm.sh 
	echo " ">>./expect_qualcomm.sh 
	echo "spawn ./test.sh cmnlib/cmnlib ">>./expect_qualcomm.sh 
	echo "expect \"(aArRoOsSq):\" ">>./expect_qualcomm.sh 
	echo "send \"O\n\" ">>./expect_qualcomm.sh 
	echo "expect off ">>./expect_qualcomm.sh 
	echo " ">>./expect_qualcomm.sh 
	echo "spawn ./test.sh cmnlib64/cmnlib64 ">>./expect_qualcomm.sh 
	echo "expect \"(aArRoOsSq):\" ">>./expect_qualcomm.sh 
	echo "send \"O\n\" ">>./expect_qualcomm.sh 
	echo "expect off ">>./expect_qualcomm.sh 
	echo " ">>./expect_qualcomm.sh 
	echo "spawn ./test.sh sampleapp32/smplap32 ">>./expect_qualcomm.sh 
	echo "expect \"(aArRoOsSq):\" ">>./expect_qualcomm.sh 
	echo "send \"O\n\" ">>./expect_qualcomm.sh 
	echo "expect off ">>./expect_qualcomm.sh 
	echo " ">>./expect_qualcomm.sh 
	echo "spawn ./test.sh sampleapp64/smplap64 ">>./expect_qualcomm.sh 
	echo "expect \"(aArRoOsSq):\" ">>./expect_qualcomm.sh 
	echo "send \"O\n\" ">>./expect_qualcomm.sh 
	echo "expect off ">>./expect_qualcomm.sh 
	 
	echo "#!/bin/bash ">>./test.sh 
	echo " ">>./test.sh 
	echo 'mcopy -i ./NON-HLOS.bin ./signedMeta/sm8150/${1}.* ::image/'>>./test.sh 
	
	chmod 777 ./test.sh ./expect_qualcomm.sh
	./expect_qualcomm.sh
	rm -rf ./expect_qualcomm.sh
	rm -rf ./test.sh
	
	cp -f NON-HLOS.bin   ../build/ufs/8155_la/bin/NON-HLOS.bin
	
	cd signedMeta/sm8150 
	echo "signedMeta/sm8150 $PWD"
	
	cp -f aop/aop.mbn 	../../../../aop_8155/aop_proc/build/ms/bin/AAAAANAZO/
	cp -f cmnlib/cmnlib.m* ../../../../tz_8155/trustzone_images/build/ms/bin/YAQAANAA/
	cp -f cmnlib64/cmnlib64.m* ../../../../tz_8155/trustzone_images/build/ms/bin/YAQAANAA/
	cp -f devcfg/devcfg_auto.mbn ../../../../tz_8155/trustzone_images/build/ms/bin/YAQAANAA/
	cp -f npu/npu.* ../../../../npu/npu_proc/build/ms/signed/
	cp -f prog_firehose_ddr/prog_firehose_ddr.elf  ../../../../boot_8155/boot_images/QcomPkg/SDMPkg/855/Bin/AU/RELEASE/
	cp -f prog_firehouse_lite/prog_firehose_lite.elf  ../../../../boot_8155/boot_images/QcomPkg/SDMPkg/855/Bin/AU/RELEASE/
	cp -f sampleapp32/smplap32.m*   ../../../../tz_8155/trustzone_images/build/ms/bin/YAQAANAA/
	cp -f sampleapp64/smplap64.m*   ../../../../tz_8155/trustzone_images/build/ms/bin/YAQAANAA/
	cp -f storsec/storsec.m*   ../../../../tz_8155/trustzone_images/build/ms/bin/YAQAANAA/
	cp -f tz/tz.mbn    ../../../../tz_8155/trustzone_images/build/ms/bin/YAQAANAA/
	cp -f uefisecapp/uefi_sec.mbn     ../../../../tz_8155/trustzone_images/build/ms/bin/YAQAANAA/
	cp -f venus/venus.* ../../../../video/venus_proc/build/bsp/asic/build/PROD/mbn/reloc/signed/
	cp -f widevine/widevine.m*     ../../../../tz_8155/trustzone_images/build/ms/bin/YAQAANAA/
	cp -f xbl/xbl.elf  ../../../../boot_8155/boot_images/QcomPkg/SDMPkg/855/Bin/AU/RELEASE/
	cp -f xbl_config/xbl_config.elf  ../../../../boot_8155/boot_images/QcomPkg/SDMPkg/855/Bin/AU/RELEASE/
	
	echo "==>amss secure_make success"

	sync
	
	if [[ ! -f $LOCALQUA_BSP_PATH/apps/qnx_ap/target/hypervisor/host/out_8155/mifs_hyp_la.img ]];then
       cp $LOCALQUA_BSP_PATH/apps/qnx_ap/target/hypervisor/host/out_8155/signed/default/qhee/mifs_hyp_la.img $LOCALQUA_BSP_PATH/apps/qnx_ap/target/hypervisor/host/out_8155/
    fi
	
	cd $LOCALQUA_AMSS_PATH
	echo "LOCALQUA_AMSS_PATH 1 $PWD"
	#拷贝qnx产物
	rm -rf apps
	
	mkdir -p ./apps/qnx_ap/target/hypervisor/host/abl-image/signed/default/abl
	cp -f $LOCALQUA_BSP_PATH/apps/qnx_ap/target/hypervisor/host/abl-image/signed/default/abl/abl_fastboot.elf ./apps/qnx_ap/target/hypervisor/host/abl-image/signed/default/abl

    mkdir -p ./apps/qnx_ap/target/hypervisor/host/out_8155/signed/default/qhee
    cp -f $LOCALQUA_BSP_PATH/apps/qnx_ap/target/hypervisor/host/out_8155/signed/default/qhee/mifs_hyp_la.img ./apps/qnx_ap/target/hypervisor/host/out_8155/signed/default/qhee
    cp -f $LOCALQUA_BSP_PATH/apps/qnx_ap/target/hypervisor/host/out_8155/{boot_la.img,ifs2_la.img,mifs_hyp_la.img,persist.img,persist_qnx.img,system_la.img,system_la.img.sparse,qnxsota.img,share.img}	./apps/qnx_ap/target/hypervisor/host/out_8155/
	
    #拷贝安卓产物
	rm -rf ./lagvm/LINUX/android
	mkdir -p ./lagvm/LINUX/android/out/target/product/msmnile_gvmq/obj/KERNEL_OBJ
	cp -f $Qualcomm8155BashPath/${projectname}_Package/output/Android/vmlinux ./lagvm/LINUX/android/out/target/product/msmnile_gvmq/obj/KERNEL_OBJ/
	cp -f $Qualcomm8155BashPath/${projectname}_Package/Image/Android/*  ./lagvm/LINUX/android/out/target/product/msmnile_gvmq/

	
	RUNING_ERROR_PARAM=$ERROR_PARAM_CLEAN
	cd $LOCALQUA_AMSS_PATH
	echo "LOCALQUA_AMSS_PATH 2 $PWD"
	mkdir -p $LOCALQUA_AMSS_PATH/$projectversion
	for a in "${GLOBAL_PROJECT_DEF_PACKPATH[@]}"
    do
        package_path=($a)
	    if [[ ${package_path[${PACKAGE_MODULE_NAME}]} = "AMSS" ]];then
			echo "AMSS Package ${package_path[${PACKAGE_MODULE_NAME}]} from ${package_path[${PACKAGE_MODULE_PATH}]}"
		    amssImgPath=${LOCAL_PATH}/${package_path[${PACKAGE_MODULE_PATH}]}
		    if [[ ${amssImgPath##*/} =~ \{ ]] && [[ ${amssImgPath##*/} =~ \} ]] && [[ ! ${amssImgPath%/*} =~ \{ ]];then
                amssImgPath2=`echo ${amssImgPath##*/} | awk -F '{' '{print $2}' | awk -F '}' '{print $1}'`
                amssImgArr=(${amssImgPath2//,/ })
                for arri in ${amssImgArr[@]}
                do
			       echo "AMSS Package Output1 --> cp -rf ${amssImgPath%/*}/${arri} $LOCALQUA_AMSS_PATH/$projectversion"
			       cp -rf ${amssImgPath%/*}/${arri} $LOCALQUA_AMSS_PATH/$projectversion
                done
            else
			    if [[ ${amssImgPath%/*} =~ \{ ]];then
				    echo "AMSS Pakage XML Fail format error !!!!"
				    _build_error_exit $RUNING_ERROR_PARAM
				fi
		        echo "AMSS Package Output2 -->  cp -rf ${amssImgPath} $LOCALQUA_AMSS_PATH/$projectversion"
                cp -rf ${amssImgPath} $LOCALQUA_AMSS_PATH/$projectversion
            fi
		elif [[ ${package_path[${PACKAGE_MODULE_NAME}]} = "QNX" ]];then
			echo "Qnx Package ${package_path[${PACKAGE_MODULE_NAME}]} from ${package_path[${PACKAGE_MODULE_PATH}]}"
		    qnxImgPath=${LOCAL_PATH}/${package_path[${PACKAGE_MODULE_PATH}]}
		    if [[ ${qnxImgPath##*/} =~ \{ ]] && [[ ${qnxImgPath##*/} =~ \} ]] && [[ ! ${qnxImgPath%/*} =~ \{ ]];then
                qnxImgPath2=`echo ${qnxImgPath##*/} | awk -F '{' '{print $2}' | awk -F '}' '{print $1}'`
                qnxImgArr=(${qnxImgPath2//,/ })
                for arri in ${qnxImgArr[@]}
                do
					echo "Qnx Package Output1 --> cp -rf ${qnxImgPath%/*}/${arri} $LOCALQUA_BSP_PATH/apps/qnx_ap/target/hypervisor/host/out_8155/update/$projectversion"
					cp -rf ${qnxImgPath%/*}/${arri} $LOCALQUA_BSP_PATH/apps/qnx_ap/target/hypervisor/host/out_8155/update/$projectversion
                done
            else
			    if [[ ${qnxImgPath%/*} =~ \{ ]];then
				    echo "Qnx Pakage XML Fail format error !!!!"
				    _build_error_exit $RUNING_ERROR_PARAM
				fi
		        echo "Qnx Package Output2 -->  cp -rf ${qnxImgPath} $LOCALQUA_BSP_PATH/apps/qnx_ap/target/hypervisor/host/out_8155/update/$projectversion"
                cp -rf ${qnxImgPath} $LOCALQUA_BSP_PATH/apps/qnx_ap/target/hypervisor/host/out_8155/update/$projectversion
            fi
        else
		    echo "Nothing Packgae to move !!!!"
        fi
    done
	cp -f $LOCAL_PATH/SVN_PUBLISH/base/scripts/amss.sh $LOCALQUA_AMSS_PATH/$projectversion
	
	cd $LOCALQUA_BSP_PATH/apps/qnx_ap/target/hypervisor/host/out_8155/update/$projectversion
	echo "bsp $projectversion $PWD"
	rm -rf $LOCALQUA_BSP_PATH/apps/qnx_ap/target/hypervisor/host/out_8155/update/$projectversion/QNX.tar.gz
	tar -czf QNX.tar.gz *
	 
	cp $LOCALQUA_BSP_PATH/apps/qnx_ap/target/hypervisor/host/out_8155/update/$projectversion/QNX.tar.gz $Qualcomm8155BashPath/${projectname}_Package/Update
	rsync -rv --exclude='QNX.tar.gz' $LOCALQUA_BSP_PATH/apps/qnx_ap/target/hypervisor/host/out_8155/update/$projectversion/* $Qualcomm8155BashPath/${projectname}_Package/Image/Qnx
	if [[ $projectname =~ "HS7012A" ]] || [[ $projectname =~ "HS7023A" ]] || [[ $projectname =~ "HS7029A" ]];then
		rsync -rv --exclude='QNX.tar.gz' $LOCALQUA_BSP_PATH/apps/qnx_ap/target/hypervisor/host/out_8155/update/$projectversion/* $Qualcomm8155BashPath/${projectname}_Package/OTA/Qnx
	fi
	
	cd $LOCALQUA_AMSS_PATH/$projectversion  
	echo "amss $projectversion $PWD"
    tar -czf AMSS.tar.gz *
	cp $LOCALQUA_AMSS_PATH/$projectversion/AMSS.tar.gz $Qualcomm8155BashPath/${projectname}_Package/Update
	rsync -rv --exclude='AMSS.tar.gz' $LOCALQUA_AMSS_PATH/$projectversion/* $Qualcomm8155BashPath/${projectname}_Package/Image/AMSS
	if [[ $projectname =~ "HS7012A" ]] || [[ $projectname =~ "HS7023A" ]] || [[ $projectname =~ "HS7029A" ]];then
		cp -f $LOCALQUA_AMSS_PATH/$projectversion/NON-HLOS.bin $Qualcomm8155BashPath/${projectname}_Package/OTA/AMSS
		cp -f $LOCALQUA_BSP_PATH/apps/qnx_ap/target/hypervisor/host/out_8155/share.img	$Qualcomm8155BashPath/${projectname}_Package/Image/Qnx
	fi
	
	if [[ $? -ne 0 ]]; then
	    echo "copy and remove QNX build Image Error, please ensure sufficient space !!!!!!!!"
	    _build_error_exit $RUNING_ERROR_PARAM
    fi
}

_output_install()
{
   cd $LOCALQUA_BSP_PATH/apps/qnx_ap
  
   cp -f ./target/hypervisor/host/out_8155/el2-save-restore.qvmhost_hyp_la.sym $Qualcomm8155BashPath/${projectname}_Package/output/Qnx
   cp -f ./target/hypervisor/host/out_8155/procnto-smp-instr.qvmhost_hyp_la.sym $Qualcomm8155BashPath/${projectname}_Package/output/Qnx
   cp -f ./target/hypervisor/host/out_8155/startup-sdx.qvmhost_hyp_la.sym $Qualcomm8155BashPath/${projectname}_Package/output/Qnx
}

set_version

sync_mainline

_upload_qnx_config

set_config_versions

if [[ "${isBuildSecure}" = "1" ]];then
   secure_boot_make
else
   _build
fi

_output_install

myexit 0
