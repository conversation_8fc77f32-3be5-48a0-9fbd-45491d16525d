OEM signed image with RSAPSS
ERROR: Following validations failed for the image:
       1. Following signing attributes do not match: 
          | Attribute |   Image    | Config |
          |-----------|------------|--------|
          |   app_id  | 0xe8ea9532 | 0x333  |
          |   debug   |    0x1     |  0x0   |
          
Image /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/tz_apps/qtee_tas/build/ms/bin/YAQAANAA/widevine.mbn is not encrypted

Base Properties: 
| Integrity Check             | True  |
| Signed                      | True  |
| Encrypted                   | False |
| Size of signature           | 256   |
| Size of one cert            | 2048  |
| Num of certs in cert chain  | 3     |
| Number of root certs        | 1     |
| Maximum number of root certs| None  |
| Cert chain size             | 6144  |

ELF Properties: 
Header: 
| Magic                      | ELF                           |
| Class                      | ELF64                          |
| Data                       | 2's complement, little endian  |
| Version                    | 1 (Current)                    |
| OS/ABI                     | No extensions or unspecified   |
| ABI Version                | 0                              |
| Type                       | DYN (Shared object file)       |
| Machine                    | 183                            |
| Version                    | 0x1                            |
| Entry address              | 0x00000000                     |
| Program headers offset     | 0x00000040                     |
| Section headers offset     | 0x00000000                     |
| Flags                      | 0x00000000                     |
| ELF header size            | 64                             |
| Program headers size       | 56                             |
| Number of program headers  | 6                              |
| Section headers size       | 64                             |
| Number of section headers  | 0                              |
| String table section index | 0                              |

Program Headers: 
| Num |   Type   | Offset | VirtAddr | PhysAddr | FileSize | MemSize | Flags | Align |
|-----|----------|--------|----------|----------|----------|---------|-------|-------|
|  1  | LOAD     |0x03000 | 0x00000  | 0x00000  | 0x20921  | 0x20921 |   RE  | 0x1000|
|  2  | LOAD     |0x24000 | 0x21000  | 0x21000  | 0x000d9  | 0x000d9 |   RW  | 0x1000|
|  3  | LOAD     |0x25000 | 0x22000  | 0x22000  | 0x0181d  | 0x0181d |   RW  | 0x1000|
|  4  | LOAD     |0x27000 | 0x24000  | 0x24000  | 0x00300  | 0x00300 |   RW  | 0x1000|
|  5  | DYNAMIC  |0x28000 | 0x25000  | 0x25000  | 0x00130  | 0x00130 |   RW  | 0x1000|
|  6  | LOAD     |0x28000 | 0x25000  | 0x25000  | 0x052e8  | 0x052e8 |   RW  | 0x1000|

Hash Segment Properties: 
| Header Size     | 168B  |
| Hash Algorithm  | sha384|

Header: 
| cert_chain_ptr              | 0xffffffff  |
| cert_chain_size             | 0x00001800  |
| cert_chain_size_qti         | 0x00000000  |
| code_size                   | 0x00000180  |
| image_id                    | 0x00000004  |
| image_size                  | 0x00001a80  |
| metadata_major_version      | 0x00000000  |
| metadata_major_version_qti  | 0x00000000  |
| metadata_minor_version      | 0x00000000  |
| metadata_minor_version_qti  | 0x00000000  |
| metadata_size               | 0x00000078  |
| metadata_size_qti           | 0x00000000  |
| sig_ptr                     | 0xffffffff  |
| sig_size                    | 0x00000100  |
| sig_size_qti                | 0x00000000  |
| version                     | 0x00000006  |
Metadata:
| anti_rollback_version        | 0x00000000  |
| app_id                       | 0xe8ea9532  |
| debug                        | 0x00000001  |
| hw_id                        | 0x00000000  |
| in_use_soc_hw_version        | 0x00000001  |
| model_id                     | 0x00000000  |
| mrc_index                    | 0x00000000  |
| multi_serial_numbers         | 0x00000000  |
| oem_id                       | 0x00000000  |
| oem_id_independent           | 0x00000000  |
| root_revoke_activate_enable  | 0x00000000  |
| rot_en                       | 0x00000000  |
| soc_vers                     | 0x00006003  |
| sw_id                        | 0x0000000c  |
| uie_key_switch_enable        | 0x00000000  |
| use_serial_number_in_signing | 0x00000000  |


