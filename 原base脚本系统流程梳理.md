# 原base脚本系统流程梳理文档

## 📋 系统概述

原base脚本系统是Qualcomm8155 Jenkins自动编译系统的基础版本，采用进程池管理机制，支持最多3个并发构建，9个等待队列。系统支持Android、QNX、AMSS等多个模块的联合构建。

## 🏗️ 1. 总体架构流程图

```mermaid
graph TD
    A[Jenkins触发构建] --> B[E-Cockpit_Qualcomm8155_build.sh<br/>主入口脚本]
    B --> C{项目类型判断}
    
    C -->|HS7002/HS7010B/HS7016/HS7019| D[_start_build<br/>传统构建流程]
    C -->|HS7025| E[ratbuild.sh<br/>RAT构建流程]
    C -->|其他项目| F[_start_build_xml<br/>XML配置构建流程]
    
    F --> G[进程池管理<br/>Qualcomm8155BashPID.sh]
    G --> H{检查构建池状态}
    H -->|池满| I[等待队列<br/>最多9个等待]
    H -->|有空闲| J[分配构建池<br/>最多3个并发]
    
    I --> K[定期检查池状态<br/>每90秒检查一次]
    K --> H
    
    J --> L[_start_build_module<br/>启动各模块编译]
    L --> M[更新编译脚本<br/>SVN导出buildShell]
    
    M --> N{构建类型选择}
    N -->|SW1_NAME=0或1| O[Android + QNX联合构建]
    N -->|SW1_NAME=2| P[仅Android构建]
    N -->|SW1_NAME=3| Q[仅QNX构建]
    N -->|SW1_NAME=4| R[并行构建模式]
    
    O --> S[_start_build_android_mode<br/>Android构建]
    O --> T[_start_build_qnx_mode<br/>QNX构建]
    P --> S
    Q --> T
    R --> U[并行启动Android和QNX]
    
    S --> V[Batch_Build_1.2.sh<br/>Android批量构建]
    T --> W[Qualcomm8155QnxBuild.sh<br/>QNX构建]
    U --> V
    U --> W
    
    V --> X[Android构建流程]
    W --> Y[QNX构建流程]
    
    X --> Z{构建结果检查}
    Y --> Z
    
    Z -->|成功| AA[后处理阶段]
    Z -->|失败| BB[错误处理<br/>CollectBuildError.py]
    
    AA --> CC{是否需要OTA包}
    CC -->|SW3_NAME=2| DD[OTA包构建<br/>buildOtaPackage.sh]
    CC -->|否| EE[跳过OTA构建]
    
    DD --> FF[打包阶段<br/>MakePackage_3.0.sh]
    EE --> FF
    
    FF --> GG{是否需要UFS构建}
    GG -->|JENKINS_BUILD_UFS=1| HH[UFS构建<br/>Ufs_Qualcomm_Build.sh]
    GG -->|否| II[跳过UFS构建]
    
    HH --> JJ[最终打包和上传]
    II --> JJ
    
    JJ --> KK{是否上传JFROG}
    KK -->|是| LL[上传到JFROG仓库]
    KK -->|否| MM[本地存储]
    
    LL --> NN[构建完成通知]
    MM --> NN
    
    BB --> OO[错误日志收集]
    OO --> PP[邮件通知<br/>AutoSendEmail.py]
    
    NN --> QQ[清理临时文件<br/>删除hqx1.2.1.c1_r00004.2]
    QQ --> RR[释放构建池资源]
    RR --> SS[构建结束]
    
    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style V fill:#e8f5e8
    style W fill:#fff3e0
    style FF fill:#fce4ec
    style BB fill:#ffebee
```

## 🔄 2. 进程池管理详细流程图

```mermaid
graph TD
    A[Jenkins构建请求] --> B[Qualcomm8155BashPID.sh<br/>进程池管理器]
    B --> C[读取PidIDConfig.cfg配置]
    
    C --> D{检查构建池状态}
    D --> E[扫描Pool0-Pool2状态<br/>最多3个并发]
    E --> F[读取各池的PID文件]
    
    F --> G{Pool状态分析}
    G -->|所有池都忙| H[进入等待队列]
    G -->|有空闲池| I[分配空闲池]
    
    H --> J[等待队列管理<br/>最多9个等待]
    J --> K{等待队列状态}
    K -->|队列满| L[拒绝新请求<br/>返回错误码2]
    K -->|队列未满| M[加入等待队列]
    
    M --> N[设置等待标志<br/>waitDoqueNum++]
    N --> O[每90秒检查一次池状态]
    O --> P{重新检查池状态}
    P -->|仍然全忙| O
    P -->|有空闲| Q[从等待队列分配池]
    
    I --> R[选择空闲池<br/>Pool0优先]
    Q --> R
    R --> S[设置池状态为忙碌]
    S --> T[创建PID文件<br/>记录进程信息]
    
    T --> U[设置构建环境变量]
    U --> V[启动_start_build_module]
    
    V --> W[更新编译脚本<br/>SVN导出buildShell]
    W --> X[设置脚本权限<br/>chmod 777]
    
    X --> Y{构建模式选择}
    Y -->|SW1_NAME=0,1| Z[顺序构建<br/>Android→QNX]
    Y -->|SW1_NAME=2| AA[仅Android构建]
    Y -->|SW1_NAME=3| BB[仅QNX构建]
    Y -->|SW1_NAME=4| CC[并行构建<br/>Android||QNX]
    
    Z --> DD[启动Android构建]
    AA --> DD
    CC --> DD
    CC --> EE[启动QNX构建]
    
    DD --> FF[监控Android进程]
    EE --> GG[监控QNX进程]
    BB --> GG
    Z --> HH[等待Android完成后启动QNX]
    HH --> GG
    
    FF --> II{Android构建结果}
    GG --> JJ{QNX构建结果}
    
    II --> KK[写入AndroidResum标志]
    JJ --> LL[写入QnxResum标志]
    
    KK --> MM{构建完成检查}
    LL --> MM
    
    MM -->|成功| NN[后处理流程]
    MM -->|失败| OO[错误处理流程]
    
    NN --> PP[OTA包构建<br/>如需要]
    PP --> QQ[MakePackage打包]
    QQ --> RR[UFS构建<br/>如需要]
    RR --> SS[邮件通知]
    
    OO --> TT[错误日志收集]
    TT --> UU[错误邮件通知]
    
    SS --> VV[清理池资源]
    UU --> VV
    VV --> WW[删除PID文件]
    WW --> XX[重置池状态为空闲]
    
    XX --> YY{是否有等待队列}
    YY -->|有等待| ZZ[通知等待队列]
    YY -->|无等待| AAA[池管理完成]
    
    ZZ --> BBB[从等待队列取出下一个请求]
    BBB --> R
    
    L --> CCC[记录拒绝日志]
    AAA --> DDD[进程池管理器待命]
    CCC --> DDD
    
    style A fill:#e1f5fe
    style H fill:#fff3e0
    style L fill:#ffebee
    style V fill:#e8f5e8
    style CC fill:#fce4ec
```

## ⏱️ 3. 构建时序图

```mermaid
sequenceDiagram
    participant J as Jenkins
    participant M as 主脚本<br/>E-Cockpit_Qualcomm8155_build.sh
    participant P as 进程池管理<br/>Qualcomm8155BashPID.sh
    participant S as SVN服务器
    participant A as Android构建<br/>Batch_Build_1.2.sh
    participant Q as QNX构建<br/>Qualcomm8155QnxBuild.sh
    participant F as 文件系统

    J->>M: 触发构建(项目参数)
    M->>M: 解析构建参数和环境变量
    M->>M: 设置构建路径和配置

    alt 传统项目(HS7002/HS7010B等)
        M->>M: _start_build()
        M->>P: Qualcomm8155BashPID.sh检查PID
        P-->>M: 返回PID状态
    else XML配置项目
        M->>M: _start_build_xml()
        M->>P: 进程池管理检查

        alt 构建池已满
            P->>P: 等待队列(最多9个)
            P->>P: 每90秒检查一次
        else 有空闲池(最多3个)
            P->>M: 分配构建池(Pool0-2)
        end
    end

    M->>M: _start_build_module()
    M->>S: SVN导出buildShell脚本
    S-->>M: 脚本更新完成
    M->>M: 设置脚本权限(chmod 777)

    alt 构建模式选择
        alt SW1_NAME=0或1(顺序构建)
            M->>M: _start_build_android_mode()
            M->>A: 启动Android构建
            A->>A: Batch_Build_1.2.sh执行
            A-->>M: Android构建完成
            M->>M: _start_build_qnx_mode()
            M->>Q: 启动QNX构建
            Q->>Q: Qualcomm8155QnxBuild.sh执行
            Q-->>M: QNX构建完成
        else SW1_NAME=2(仅Android)
            M->>M: _start_build_android_mode()
            M->>A: 启动Android构建
            A-->>M: Android构建完成
        else SW1_NAME=3(仅QNX)
            M->>M: _start_build_qnx_mode()
            M->>Q: 启动QNX构建
            Q-->>M: QNX构建完成
        else SW1_NAME=4(并行构建)
            par Android并行构建
                M->>A: 启动Android构建
                A->>A: 并行执行构建
                A-->>M: Android构建结果
            and QNX并行构建
                M->>Q: 启动QNX构建
                Q->>Q: 并行执行构建
                Q-->>M: QNX构建结果
            end
        end
    end

    M->>M: 检查构建结果
    M->>M: 写入结果标志(AndroidResum/QnxResum)

    alt 构建成功
        alt 需要OTA包(SW3_NAME=2)
            M->>M: buildOtaPackage.sh
            M->>M: OTA包构建
        end

        alt 非传统项目
            M->>M: MakePackage.sh打包
            M->>F: 生成升级包
        end

        alt 需要UFS构建
            M->>M: Ufs_Qualcomm_Build.sh
            M->>M: UFS构建处理
        end

        M->>M: 清理临时文件
        M->>F: 删除hqx1.2.1.c1_r00004.2
        M->>M: AutoSendEmail.py发送成功通知
        M-->>J: 构建成功
    else 构建失败
        M->>M: CollectBuildError.py收集错误
        M->>M: AutoSendEmail.py发送失败通知
        M-->>J: 构建失败
    end

    M->>P: 释放构建池资源
    P->>P: 删除PID文件
    P->>P: 重置池状态为空闲
    P-->>M: 资源释放完成
```

## 🏗️ 4. 系统架构特点

### **核心组件**：
- **主入口脚本**: E-Cockpit_Qualcomm8155_build.sh
- **进程池管理器**: Qualcomm8155BashPID.sh
- **Android构建器**: Batch_Build_1.2.sh → AndroidR_GA.sh
- **QNX构建器**: Qualcomm8155QnxBuild.sh
- **打包器**: MakePackage_3.0.sh
- **错误收集器**: CollectBuildError.py
- **邮件通知器**: AutoSendEmail.py

### **并发控制**：
- **构建池数量**: 3个 (Pool0-Pool2) - 比新版本少1个
- **等待队列**: 最多9个任务
- **轮询间隔**: 90秒检查一次资源状态
- **PID管理**: 通过PidIDConfig.cfg配置文件管理

## 🔧 5. 关键技术差异

### **1. 构建池管理**：
```bash
# 原base版本
PoolNum=3  # 最多3个并发构建池
WaitPoolNum=9  # 等待队列数量

# 新版本
PoolNum=4  # 最多4个并发构建池
```

### **2. 路径配置**：
```bash
# 原base版本
Qualcomm8155BashPath=/mnt/home/<USER>/e-cockpit/qnx-android/jenkins_qnx-android_Qualcomm8155
BuildOutputPath=/mnt/BU2_NAS/Jenkins_output/SA8155

# 新版本
Qualcomm8155BashPath=/mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155
```

### **3. 构建模式**：
- **SW1_NAME=0或1**: Android + QNX顺序构建
- **SW1_NAME=2**: 仅Android构建
- **SW1_NAME=3**: 仅QNX构建
- **SW1_NAME=4**: Android + QNX并行构建

### **4. 脚本更新机制**：
```bash
# 原base版本 - 每次构建都更新脚本
svn --force export $Build_Shell_Path
chmod 777 ./${Build_Shell_Path##*/}/*.sh
chmod 777 ./${Build_Shell_Path##*/}/*.py
chmod 777 ./${Build_Shell_Path##*/}/*.pyc
```

### **5. SVN配置**：
```bash
# 原base版本SVN路径
SVN_ROOT=http://swsvn01.adayoge.com/svn/Qualcomm/02_SA8155P
Build_Shell_Path="http://10.2.4.101/svn/Qualcomm/02_SA8155P/01_HS7001A/02_CodeLib/01_MainPath/1.2/02_Android/04_Shell/buildShell3.0/buildShell"
```

## ⏱️ 6. 构建流程时序

### **阶段1: 构建初始化** (0-2分钟)
1. Jenkins触发构建请求
2. 主脚本解析构建参数
3. 项目类型判断 (传统项目 vs XML配置项目)
4. 进程池资源分配

### **阶段2: 脚本准备** (2-5分钟)
1. SVN导出最新buildShell脚本
2. 设置脚本执行权限
3. 环境变量配置

### **阶段3: 构建执行** (5-120分钟)
- **顺序构建**: Android完成后启动QNX
- **并行构建**: Android和QNX同时执行
- **单独构建**: 只执行指定模块

### **阶段4: 后处理** (120-140分钟)
1. OTA包构建 (如需要)
2. MakePackage打包
3. UFS构建 (如需要)
4. 邮件通知

## 📊 7. 与新版本的主要差异

| 特性 | 原base版本 | 新版本 |
|------|------------|--------|
| **构建池数量** | 3个 | 4个 |
| **脚本更新** | 每次SVN导出 | 本地维护 |
| **路径配置** | /mnt/home/<USER>/mnt/new_disk |
| **XML配置** | 基础支持 | 完整XML驱动 |
| **错误处理** | 基础处理 | 增强诊断 |
| **许可证管理** | 简单处理 | 双重部署 |
| **SVN认证** | 基础认证 | 硬编码凭据 |
| **动态分区** | 基础支持 | 完整配置 |

## 🎯 8. 系统优势

### **简洁性**
- 脚本结构相对简单，易于理解和维护
- 核心逻辑清晰，模块划分明确

### **稳定性**
- 经过长期使用验证的稳定架构
- 成熟的错误处理和恢复机制

### **灵活性**
- 支持多种构建模式组合
- 可配置的构建参数和选项

### **自动化**
- 完整的自动化构建和通知流程
- 自动资源管理和清理机制

## 🔄 9. 升级改进点

### **并发能力提升**
- 从3个池扩展到4个池，提高并发处理能力
- 更精细的资源分配和调度算法

### **错误诊断增强**
- 增强错误分析和处理能力
- 更详细的错误分类和报告机制

### **配置管理优化**
- 更完善的XML配置驱动系统
- 动态配置加载和验证机制

### **资源管理改进**
- 更精细的资源分配和清理
- 更可靠的QNX许可证管理

### **安全性增强**
- SVN认证凭据的安全管理
- 构建过程的安全性验证

## 📋 10. 总结

原base系统为新版本的发展奠定了坚实的基础，具有以下特点：

### **核心价值**
1. **架构稳定**: 经过长期验证的可靠架构
2. **功能完整**: 覆盖完整的构建生命周期
3. **易于维护**: 清晰的代码结构和模块划分
4. **扩展性好**: 为后续功能扩展提供了良好基础

### **演进方向**
新版本在保持原有架构优势的同时，进行了多方面的优化和增强：
- **性能提升**: 更高的并发能力和处理效率
- **功能增强**: 更完善的配置管理和错误处理
- **用户体验**: 更详细的诊断信息和状态反馈
- **系统稳定性**: 更可靠的资源管理和异常处理

原base系统的设计理念和核心架构在新版本中得到了很好的传承和发展，体现了系统演进的连续性和稳定性。
