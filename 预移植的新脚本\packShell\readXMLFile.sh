#! /bin/bash 
#****************************
# XML�����ļ������ű�
# Authot xizhuang.wu
# version 1.0
#****************************
BuildType=$1
Project_Branches=$2
ReadModule=$3

isBuildSecure=$4
isBuildXuGao=$5

Branches_Text=""
Code_Module=("SDP" "BSP" "MPU" "PUBLISH" "AMSS" "MCU" "4G_MODULE" "LIGHT_SOUND")
OutPut_Module=("ANDROID" "QNX" "AMSS")
Images_Module=("Delete" "Switch")
Package_Module=("Coverity" "Version" "Email")
Tools_Module=("Ailabi")
Android_Module=("AAOP_OS" "AAOP_MID" "SDK1_OS" "SDK2_OS" "APP" "MID")
Android_LocalPath_Module=("" "adayo/Middleware" "" "" "adayo/Application" "adayo/Middleware")
Block_Text=""

xmlFilePath=`dirname $0`

#ģ���������
function _read_module_block()
{
    block_name=$1
	xmlFile=$2
	
	if [[ -f $xmlFile ]];then
	   awk -v RS="<${block_name}>" 'NR==2{print}' $xmlFile | awk -v RS="</${block_name}>" 'NR==1{print}' > OutPut
	else
	   if [[ $block_name = "Path" ]];then
	      echo $xmlFile | awk -F '<'${block_name}'>|</'${block_name}'>' '{print $2}' > OutPut
	   else
	      echo $xmlFile | awk -F '<'${block_name}'|</'${block_name}'>' '{print $2}' | awk -F '>' '{print $2}' > OutPut
	   fi
	fi
	sed -i '/^[[:space:]]*$/d' OutPut
	
	return 0
}

#��ǩ���Խ�������
function _read_lable_value()
{
    lable_name=$1    #��ǩ��
	lable_attr=$2    #��ǩ����
	lable_str=$3     #��ǩ�ַ���
	
    lable_value=`echo $lable_str | awk -v RS="<${lable_name}" 'NR==2{print}' | awk -v RS=">" 'NR==1{print}' | grep -v "^$" | awk -F ${lable_attr}= '{print $2}' | awk '{print $1}' | sed "s/\"//g"`
	echo $lable_value
	return 0
}

#�����֧SVN·���ű����ɺ���
function _create_svnpath_shell()
{
   _create_project=$1
   _create_module=$2
   _create_svnpath_project=$3
   _create_svnpath_branches=$4
   
   default_svn_path=$_create_svnpath_branches
   
   default_branch_path=""
   
   if [[ $default_svn_path =~ Trunk ]] || [[ $default_svn_path =~ trunk ]];then
       default_branch_path="_Trunk"
   elif [[ $default_svn_path =~ Branch ]] || [[ $default_svn_path =~ branch ]];then
       default_branch_path="_Branches"
   elif [[ $default_svn_path =~ "03_develop_Tem" ]];then
       default_branch_path="_DevelopTem"
   elif [[ $default_svn_path =~ Develop ]] || [[ $default_svn_path =~ develop ]];then
       default_branch_path="_Develop"
   elif [[ $default_svn_path =~ Release ]] || [[ $default_svn_path =~ release ]];then
       default_branch_path="_Release"
   else
       echo "default_branch_path is null !!!!!"
   fi
   
   if [[ $_create_svnpath_project =~ $_create_project ]];then
      Extend="PROJECT"
   else
      Extend="BASE"
   fi
   
   case $_create_module in
        "SDP") 
		     echo "write SDP module config"
			 if [[ $default_svn_path =~ qnx_sdp  ]];then
			    default_svn_path=$_create_svnpath_branches
             else
                default_svn_path="01_ProjectPath/01_Qnx/03_qnx_sdp_hqx1.2.1.c1/"$_create_svnpath_branches			 
			 fi
			 echo "\"http://10.2.4.101/svn/Qualcomm/02_SA8155P/"${_create_svnpath_project}"/02_CodeLib/"$default_svn_path"        ${Extend}_${_create_module}_${_create_svnpath_project}${default_branch_path}     hqx1.2.1.c1_r00004.2/qnx_sdp\"" >> $xmlFilePath/${Path_Shell}.sh 
        ;;
		"BSP") 
		     echo "write BSP module config"
			 if [[ $default_svn_path =~ hlos_dev_qnx  ]];then
			    default_svn_path=$_create_svnpath_branches
             else
                default_svn_path="01_ProjectPath/01_Qnx/01_hlos_dev_qnx/"$_create_svnpath_branches			 
			 fi
			 if [[ $default_svn_path =~ qnx_bsp ]];then
			    echo "\"http://10.2.4.101/svn/Qualcomm/02_SA8155P/"${_create_svnpath_project}"/02_CodeLib/"$default_svn_path"        ${Extend}_${_create_module}_${_create_svnpath_project}${default_branch_path}     hqx1.2.1.c1_r00004.2/qnx_bsp\"" >> $xmlFilePath/${Path_Shell}.sh 
			 else
			    echo "\"http://10.2.4.101/svn/Qualcomm/02_SA8155P/"${_create_svnpath_project}"/02_CodeLib/"$default_svn_path"/qnx_bsp        ${Extend}_${_create_module}_${_create_svnpath_project}${default_branch_path}     hqx1.2.1.c1_r00004.2/qnx_bsp\"" >> $xmlFilePath/${Path_Shell}.sh 
			 fi
        ;;
        "MPU") 
		     echo "write MPU module config"
			 if [[ $default_svn_path =~ Mpu_Public  ]];then
			    default_svn_path=$_create_svnpath_branches 
			 else
			    default_svn_path="01_ProjectPath/01_Qnx/02_Mpu_Public/"$_create_svnpath_branches
			 fi
			 echo "\"http://10.2.4.101/svn/Qualcomm/02_SA8155P/"${_create_svnpath_project}"/02_CodeLib/"$default_svn_path"/03_src        ${Extend}_${_create_module}_${_create_svnpath_project}${default_branch_path}     hqx1.2.1.c1_r00004.2/qnx_bsp/apps/qnx_ap/AMSS\"" >> $xmlFilePath/${Path_Shell}.sh
        ;;
        "PUBLISH") 
		     echo "write PUBLISH module config"
			 if [[ $default_svn_path =~ Mpu_Public  ]];then
			    default_svn_path=$_create_svnpath_branches  
			 else
			    default_svn_path="01_ProjectPath/01_Qnx/02_Mpu_Public/"$_create_svnpath_branches
			 fi
			 echo "\"http://10.2.4.101/svn/Qualcomm/02_SA8155P/"${_create_svnpath_project}"/02_CodeLib/"$default_svn_path"/02_publish/mpu/packet        ${Extend}_${_create_module}_${_create_svnpath_project}${default_branch_path}     SVN_PUBLISH\"" >> $xmlFilePath/${Path_Shell}.sh
        ;;
        "AMSS") 
		     echo "write AMSS module config"
			 if [[ $default_svn_path =~ Amss  ]];then
			    default_svn_path=$_create_svnpath_branches
			 else
			    default_svn_path="01_ProjectPath/04_Amss/"$_create_svnpath_branches
			 fi
			 echo "\"http://10.2.4.101/svn/Qualcomm/02_SA8155P/"${_create_svnpath_project}"/02_CodeLib/"$default_svn_path"       ${Extend}_${_create_module}_${_create_svnpath_project}${default_branch_path}     hqx1.2.1.c1_r00004.2/amss\"" >> $xmlFilePath/${Path_Shell}.sh
        ;;
		"MCU") 
		     echo "write MCU module config"
			 if [[ $default_svn_path =~ Mpu_Public  ]];then
			    default_svn_path=$_create_svnpath_branches 
			 else
			    default_svn_path="01_ProjectPath/01_Qnx/02_Mpu_Public/"$_create_svnpath_branches 
			 fi
			 
			 if [[ $isBuildXuGao = "1" ]];then
				echo "\"http://10.2.4.101/svn/Qualcomm/02_SA8155P/"${_create_svnpath_project}"/02_CodeLib/"$default_svn_path"/02_publish/release/update/bins_OtaX        ${Extend}_${_create_module}_${_create_svnpath_project}${default_branch_path}     SVN_MCU\"" >> $xmlFilePath/${Path_Shell}.sh
			 else
				echo "\"http://10.2.4.101/svn/Qualcomm/02_SA8155P/"${_create_svnpath_project}"/02_CodeLib/"$default_svn_path"/02_publish/release/update/bins        ${Extend}_${_create_module}_${_create_svnpath_project}${default_branch_path}     SVN_MCU\"" >> $xmlFilePath/${Path_Shell}.sh
			 fi
        ;;
		"4G_MODULE") 
		     echo "write 4G module config"
			 if [[ $default_svn_path =~ 4G_MODULE  ]];then
			    default_svn_path=$_create_svnpath_branches 
			 else
			    default_svn_path="01_ProjectPath/06_4G_Module/"$_create_svnpath_branches 
			 fi
			 
			 if [[ $isBuildXuGao = "1" ]];then
				echo "\"http://10.2.4.101/svn/Qualcomm/02_SA8155P/"${_create_svnpath_project}"/02_CodeLib/"$default_svn_path"_OtaX        ${Extend}_${_create_module}_${_create_svnpath_project}${default_branch_path}     SVN_4G_MODULE\"" >> $xmlFilePath/${Path_Shell}.sh
			 elif [[ $isBuildSecure = "1" ]];then
				echo "\"http://10.2.4.101/svn/Qualcomm/02_SA8155P/"${_create_svnpath_project}"/02_CodeLib/"$default_svn_path"_SIGN        ${Extend}_${_create_module}_${_create_svnpath_project}${default_branch_path}     SVN_4G_MODULE\"" >> $xmlFilePath/${Path_Shell}.sh
			 else
				echo "\"http://10.2.4.101/svn/Qualcomm/02_SA8155P/"${_create_svnpath_project}"/02_CodeLib/"$default_svn_path"        ${Extend}_${_create_module}_${_create_svnpath_project}${default_branch_path}     SVN_4G_MODULE\"" >> $xmlFilePath/${Path_Shell}.sh
			 fi
        ;;
		"LIGHT_SOUND") 
		     echo "write lightsound config"
			 echo "\"http://10.2.4.101/svn/Qualcomm/02_SA8155P/"${_create_svnpath_project}"/02_CodeLib/"$default_svn_path"        ${Extend}_${_create_module}_${_create_svnpath_project}${default_branch_path}     SVN_LIGHT_SOUND\"" >> $xmlFilePath/${Path_Shell}.sh
        ;;
		*)
		     echo "this module is not define !!!!!"
		;;
   esac
}


#��׿�����֧SVN·���ű����ɺ���
function _create_android_svnpath_shell()
{
   _create_android_module=$1
   _create_android_svnpath=$2
   _create_android_localpath=$3
   
   default_local_path=""
   
   flagNum=0
   for android_module in ${Android_Module[@]}
   do
       if [[ $_create_android_localpath = $android_module ]];then
	      default_local_path=${Android_LocalPath_Module[flagNum]}
	   fi
	   flagNum=$((flagNum+1))
   done
   
   echo "\"${_create_android_svnpath}               ${_create_android_module}          ${default_local_path}\"" >> $xmlFilePath/${Path_Shell}.sh
}

#�����ļ��Ƴ��ű����ɺ���
function _create_remove_shell()
{
    _create_project=$1
    _create_module=$2
    _create_svnpath_project=$3
    _remove_svnpath_branches=$4
	_remove_svnpath_block=$5
   
    if [[ $_create_svnpath_project =~ $_create_project ]];then
       Extend="PROJECT"
    else
       Extend="BASE"
    fi
	
	default_branch_path=""
   
    if [[ $_remove_svnpath_block =~ Trunk ]] || [[ $_remove_svnpath_block =~ trunk ]];then
       default_branch_path="_Trunk"
    elif [[ $_remove_svnpath_block =~ Branch ]] || [[ $_remove_svnpath_block =~ branch ]];then
       default_branch_path="_Branches"
	elif [[ $_remove_svnpath_block =~ "03_develop_Tem" ]];then
       default_branch_path="_DevelopTem"
    elif [[ $_remove_svnpath_block =~ Develop ]] || [[ $_remove_svnpath_block =~ develop ]];then
       default_branch_path="_Develop"
    elif [[ $_remove_svnpath_block =~ Release ]] || [[ $_remove_svnpath_block =~ release ]];then
       default_branch_path="_Release"
    else
       echo "default_branch_path is null !!!!!"
    fi
   
    
	echo "write $_create_module remove config"
	echo "\"${_remove_svnpath_branches}        ${Extend}_${_create_module}_${_create_svnpath_project}${default_branch_path}\"" >> $xmlFilePath/${Path_Shell}_Remove.sh 
}

#����ű����ɺ���
function _create_package_shell()
{
    _create_module=$1
    _create_module_path=$2
 
	echo "write $_create_module $_create_module_path config"
	echo "\"${_create_module_path}        ${_create_module}\"" >> $xmlFilePath/${Path_Shell}.sh 
}


function _install_read_xml()
{
    echo "_install_read_xml ......"
	Path_Shell="QNX_Source_"${Project_Branches}
	
	if [[ ! -f $xmlFilePath/${Project_Branches%%_*}_${BuildType}.xml ]];then
	   svn --force export http://10.2.4.101/svn/Qualcomm/02_SA8155P/${Project_Branches%%_*}/02_CodeLib/01_ProjectPath/03_Config/${Project_Branches%%_*}_${BuildType}.xml $xmlFilePath --username gjzhao --password gjz123 --no-auth-cache --trust-server-cert &>/dev/null 2>&1
	fi
	_read_module_block ${Project_Branches} $xmlFilePath/${Project_Branches%%_*}_${BuildType}.xml
	# for i in $*
	# do
	    # if [[ $i = ${Project_Branches} ]] || [[ $i = ${BuildType} ]];then
	        # continue
	    # else
	        # if [[ ! `cat OutPut` =~ \<${i} ]];then
		       # installFlag=1
		    # fi
	    # fi
	# done
	
	# if [[ $installFlag -eq 1 ]];then
	   # _read_module_block ${Project_Branches%%_*} $xmlFilePath/${Project_Branches%%_*}_${BuildType}.xml
	# fi
	cat OutPut > $xmlFilePath/BranchesFileText
	
    _read_module_block Install $xmlFilePath/BranchesFileText
	cat OutPut > $xmlFilePath/InstallFileText
	echo "#!/bin/bash" > $xmlFilePath/${Path_Shell}.sh
    echo "export GLOBAL_PROJECT_DEF_SRCCODE=(" >> $xmlFilePath/${Path_Shell}.sh
	
	echo "#!/bin/bash" > $xmlFilePath/${Path_Shell}_Remove.sh
    echo "export GLOBAL_PROJECT_DEF_REVCODE=(" >> $xmlFilePath/${Path_Shell}_Remove.sh
	#��ȡ��ģ��̳й�ϵ  
	for code_module in ${Code_Module[@]}
	do
	    _read_module_block $code_module $xmlFilePath/InstallFileText
	    cat OutPut > $xmlFilePath/${code_module}FileText
	    while read -r moudleStr
	    do
	        if [[ $moudleStr =~ Split ]];then
		       spitVlue=$(_read_lable_value Split target "$moudleStr")
			   _read_module_block "Split" "${moudleStr}"
	           spitBranchesVlue=`cat OutPut`
			   _create_svnpath_shell ${Project_Branches%%_*} $code_module $spitVlue $spitBranchesVlue
		    elif [[ $moudleStr =~ Extend ]];then
			   _read_module_block "Extend" "${moudleStr}"
	           ExtendBlock=`cat OutPut`
			   ExtendXML=${ExtendBlock%%_*}
			   
			   if [[ ! -f $xmlFilePath/${ExtendXML}_${BuildType}.xml ]];then
			       svn --force export http://10.2.4.101/svn/Qualcomm/02_SA8155P/${ExtendXML}/02_CodeLib/01_ProjectPath/03_Config/${ExtendXML}_${BuildType}.xml $xmlFilePath --username gjzhao --password gjz123 --no-auth-cache --trust-server-cert &>/dev/null 2>&1
			   fi
			   
			   _read_module_block ${ExtendBlock} $xmlFilePath/${ExtendXML}_${BuildType}.xml
			   if [[ `cat OutPut` = "" ]];then
			       echo "Extend $ExtendBlock is not exit !!!! "
				   return
			   else
			       cat OutPut > $xmlFilePath/ExtendBlockFileText
			   fi
			   
			   _read_module_block Install $xmlFilePath/ExtendBlockFileText
	           cat OutPut > $xmlFilePath/ExtendInstallFileText
		
			   _read_module_block $code_module $xmlFilePath/ExtendInstallFileText
	           cat OutPut > $xmlFilePath/Extend${code_module}FileText
			   while read -r moudleExtendStr
			   do
			        if [[ $moudleExtendStr =~ Split ]];then
		               spitVlue=$(_read_lable_value Split target "$moudleExtendStr")
			           _read_module_block "Split" "${moudleExtendStr}"
	                   spitExtendBranchesVlue=`cat OutPut`
			           _create_svnpath_shell ${Project_Branches%%_*} $code_module $spitVlue $spitExtendBranchesVlue
					elif [[ $moudleExtendStr =~ Remove ]];then
                       removeVlue=$(_read_lable_value Remove target "$moudleExtendStr")
					   removeBlockVlue=$(_read_lable_value Remove branch "$moudleExtendStr")
			           _read_module_block "Remove" "${moudleExtendStr}"
	                   removeBranchesVlue=`cat OutPut`
			           _create_remove_shell ${Project_Branches%%_*} $code_module $removeVlue $removeBranchesVlue $removeBlockVlue
                    else
            			echo "nothing to do!!!!"		
					fi
			   done < $xmlFilePath/Extend${code_module}FileText
			elif [[ $moudleStr =~ Remove ]];then
			   removeVlue=$(_read_lable_value Remove target "$moudleStr")
			   removeBlockVlue=$(_read_lable_value Remove branch "$moudleStr")
			   _read_module_block "Remove" "${moudleStr}"
	           removeBranchesVlue=`cat OutPut`
			   _create_remove_shell ${Project_Branches%%_*} $code_module $removeVlue $removeBranchesVlue $removeBlockVlue
		    else
		       echo "nothing to do!!!!"
		    fi
	    done < $xmlFilePath/${code_module}FileText
	done
	echo ")" >> $xmlFilePath/${Path_Shell}.sh
	echo ")" >> $xmlFilePath/${Path_Shell}_Remove.sh
}

function _images_read_xml()
{
	if [[ ! -f $xmlFilePath/${Project_Branches%%_*}_${BuildType}.xml ]];then
	    svn --force export http://10.2.4.101/svn/Qualcomm/02_SA8155P/${Project_Branches%%_*}/02_CodeLib/01_ProjectPath/03_Config/${Project_Branches%%_*}_${BuildType}.xml $xmlFilePath --username gjzhao --password gjz123 --no-auth-cache --trust-server-cert &>/dev/null 2>&1
	fi
	_read_module_block ${Project_Branches} $xmlFilePath/${Project_Branches%%_*}_${BuildType}.xml
	for i in $*
	do
	    if [[ $i = ${Project_Branches} ]] || [[ $i = ${BuildType} ]];then
	        continue
	    else
	        if [[ ! `cat OutPut` =~ \<${i} ]];then
		       imgFlag=1
		    fi
	    fi
	done
	
	if [[ $imgFlag -eq 1 ]];then
	    _read_module_block ${Project_Branches%%_*} $xmlFilePath/${Project_Branches%%_*}_${BuildType}.xml
	fi 
	cat OutPut > $xmlFilePath/BranchesFileText
	
    _read_module_block Images $xmlFilePath/BranchesFileText
	cat OutPut > $xmlFilePath/ImagesFileText
	 
	#��ȡ��ģ��̳й�ϵ  
	for images_module in ${Images_Module[@]}
	do
	    if [[ $4 != $images_module ]];then
		    continue
		fi
	    _read_module_block $images_module $xmlFilePath/ImagesFileText
	    cat OutPut > $xmlFilePath/${images_module}FileText

	    while read -r moudleStr
	    do
	        if [[ $moudleStr =~ Path ]];then
			    _read_module_block "Path" "${moudleStr}"
	            pathBranchesVlue=`cat OutPut`
				echo $pathBranchesVlue'##'
		    fi
	    done < $xmlFilePath/${images_module}FileText
	done
}

#��ȡ��������ļ�ֵ
function _package_read_xml()
{
    if [[ ! -f $xmlFilePath/${Project_Branches%%_*}_${BuildType}.xml ]];then
	    svn --force export http://10.2.4.101/svn/Qualcomm/02_SA8155P/${Project_Branches%%_*}/02_CodeLib/01_ProjectPath/03_Config/${Project_Branches%%_*}_${BuildType}.xml $xmlFilePath --username gjzhao --password gjz123 --no-auth-cache --trust-server-cert &>/dev/null 2>&1
	fi
	_read_module_block ${Project_Branches} $xmlFilePath/${Project_Branches%%_*}_${BuildType}.xml
	for i in $*
	do
	    if [[ $i = ${Project_Branches} ]] || [[ $i = ${BuildType} ]];then
	        continue
	    else
	        if [[ ! `cat OutPut` =~ \<${i} ]];then
		       outFlag=1
		    fi
	    fi
	done
	
	if [[ $outFlag -eq 1 ]];then
	    _read_module_block ${Project_Branches%%_*} $xmlFilePath/${Project_Branches%%_*}_${BuildType}.xml
	fi
	cat OutPut > $xmlFilePath/PackageFileText

	for i in $*
	do
	   if [[ $i = ${Project_Branches} ]] || [[ $i = ${BuildType} ]];then
	       continue
	   elif [[ $i = ${!#} ]];then
	       _read_module_block $i $xmlFilePath/PackageFileText
		   echo `cat OutPut`
	   else
	       _read_module_block $i $xmlFilePath/PackageFileText
	       cat OutPut > $xmlFilePath/PackageFileText
	   fi
	done
}

#��ȡ��������ֵ
function _Tools_read_xml()
{
	if [[ ! -f $xmlFilePath/${Project_Branches%%_*}_${BuildType}.xml ]];then
	    svn --force export http://10.2.4.101/svn/Qualcomm/02_SA8155P/${Project_Branches%%_*}/02_CodeLib/01_ProjectPath/03_Config/${Project_Branches%%_*}_${BuildType}.xml $xmlFilePath --username gjzhao --password gjz123 --no-auth-cache --trust-server-cert &>/dev/null 2>&1
	fi
	_read_module_block ${Project_Branches} $xmlFilePath/${Project_Branches%%_*}_${BuildType}.xml
	
	cat OutPut > $xmlFilePath/ToolsFileText

	for i in $*
	do
	   if [[ $i = ${Project_Branches} ]] || [[ $i = ${BuildType} ]];then
	       continue
	   elif [[ $i = ${!#} ]];then
	       _read_module_block $i $xmlFilePath/ToolsFileText
		   echo `cat OutPut`
	   else
	       _read_module_block $i $xmlFilePath/ToolsFileText
	       cat OutPut > $xmlFilePath/ToolsFileText
	   fi
	done
}

function _output_read_xml()
{
    echo "_output_read_xml ......"
	Path_Shell="Package_Source_"${Project_Branches}
	
	if [[ ! -f $xmlFilePath/${Project_Branches%%_*}_${BuildType}.xml ]];then
	   svn --force export http://10.2.4.101/svn/Qualcomm/02_SA8155P/${Project_Branches%%_*}/02_CodeLib/01_ProjectPath/03_Config/${Project_Branches%%_*}_${BuildType}.xml $xmlFilePath --username gjzhao --password gjz123 --no-auth-cache --trust-server-cert &>/dev/null 2>&1
	fi
	_read_module_block ${Project_Branches} $xmlFilePath/${Project_Branches%%_*}_${BuildType}.xml
	if [[ `cat OutPut` = "" ]];then
	   _read_module_block ${Project_Branches%%_*} $xmlFilePath/${Project_Branches%%_*}_${BuildType}.xml
	fi
	cat OutPut > $xmlFilePath/BranchesFileText
	
	echo "#!/bin/bash" > $xmlFilePath/${Path_Shell}.sh
    echo "export GLOBAL_PROJECT_DEF_PACKPATH=(" >> $xmlFilePath/${Path_Shell}.sh
	#��ȡ��ģ��̳й�ϵ  
	for output_module in ${OutPut_Module[@]}
	do
	    _read_module_block $output_module $xmlFilePath/BranchesFileText
	    cat OutPut > $xmlFilePath/${output_module}FileText
	    while read -r moudleStr
	    do
	        if [[ $moudleStr =~ file_path ]];then
			   _read_module_block "file_path" "${moudleStr}"
	           modulePathVlue=`cat OutPut`
			   _create_package_shell $output_module $modulePathVlue
		    else
		       echo "nothing to do!!!!"
		    fi
	    done < $xmlFilePath/${output_module}FileText
	done
	echo ")" >> $xmlFilePath/${Path_Shell}.sh
}


function _install_read_android_xml()
{
    echo "_install_read_android_xml ......"
	Path_Shell="AndroidR_Source_"${Project_Branches}
	
	if [[ ! -f $xmlFilePath/${Project_Branches%%_*}_${BuildType}.xml ]];then
	   svn --force export http://10.2.4.101/svn/Qualcomm/02_SA8155P/${Project_Branches%%_*}/02_CodeLib/01_ProjectPath/03_Config/${Project_Branches%%_*}_${BuildType}.xml $xmlFilePath --username gjzhao --password gjz123 --no-auth-cache --trust-server-cert &>/dev/null 2>&1
	fi
	_read_module_block ${Project_Branches} $xmlFilePath/${Project_Branches%%_*}_${BuildType}.xml
	if [[ `cat OutPut` = "" ]];then
	   _read_module_block ${Project_Branches%%_*} $xmlFilePath/${Project_Branches%%_*}_${BuildType}.xml
	fi
	cat OutPut > $xmlFilePath/BranchesFileText
	
	echo "#!/bin/bash" > $xmlFilePath/${Path_Shell}.sh
    echo "export GLOBAL_PROJECT_DEF_SRCCODE=(" >> $xmlFilePath/${Path_Shell}.sh
	
	#��ȡ��ģ��̳й�ϵ  
	while read -r moudleStr
	do
		androidSvnPath=`echo $moudleStr | awk -F '\<*\>' '{print $2}' | awk -F '\<\/' '{print $1}'`
		androidBlock=`echo $moudleStr | awk -F '\<' '{print $2}' | awk -F '>' '{print $1}' | awk '{print $1}'`
		androidLocalPath=$(_read_lable_value $androidBlock target "$moudleStr")
		
		if [[ $androidBlock = "Extend" ]];then
		   AndroidExtendBlock=$androidSvnPath
		   AndroidExtendXML=${AndroidExtendBlock%%_*}
		   
		   if [[ ! -f $xmlFilePath/${AndroidExtendXML}_${BuildType}.xml ]];then
			  svn --force export http://10.2.4.101/svn/Qualcomm/02_SA8155P/${AndroidExtendXML}/02_CodeLib/01_ProjectPath/03_Config/${AndroidExtendXML}_${BuildType}.xml $xmlFilePath --username gjzhao --password gjz123 --no-auth-cache --trust-server-cert &>/dev/null 2>&1
		   fi
		   
		   _read_module_block ${AndroidExtendBlock} $xmlFilePath/${AndroidExtendXML%%_*}_${BuildType}.xml
	       if [[ `cat OutPut` = "" ]];then
		      echo "Android Extend $AndroidExtendBlock is not exit!!!"
	          return
	       else
		      cat OutPut > $xmlFilePath/AndroidExtendFileText
		   fi
		   
		   while read -r moudleTxtendStr
	       do
		      androidExtendSvnPath=`echo $moudleTxtendStr | awk -F '\<*\>' '{print $2}' | awk -F '\<\/' '{print $1}'`
		      androidExtendBlock=`echo $moudleTxtendStr | awk -F '\<' '{print $2}' | awk '{print $1}'`
		      androidExtendLocalPath=$(_read_lable_value $androidExtendBlock target "$moudleTxtendStr")
		
		      _create_android_svnpath_shell $androidExtendBlock $androidExtendSvnPath $androidExtendLocalPath
	       done < $xmlFilePath/AndroidExtendFileText 
	    else
		   _create_android_svnpath_shell $androidBlock $androidSvnPath $androidLocalPath
	    fi
	done < $xmlFilePath/BranchesFileText
	echo ")" >> $xmlFilePath/${Path_Shell}.sh
}

if [[ $BuildType = "Qnx" ]];then
    if [[ $ReadModule = "Install" ]];then
		_install_read_xml $@
    elif [[ $ReadModule = "Images" ]];then
		_images_read_xml $@
    elif [[ $ReadModule = "Package" ]] || [[ $ReadModule = "Release" ]];then
		_package_read_xml $@
	elif [[ $ReadModule = "Tools" ]];then
		_Tools_read_xml $@
    else
       echo "$BuildType $ReadModule is not exit !!!!"
       exit 2
    fi
elif [[ $BuildType = "Android" ]];then
   _install_read_android_xml
elif [[ $BuildType = "Package" ]];then
   _output_read_xml $@
else
   echo "$$BuildType is not exit !!!!"
   exit 2
fi

if [[ $xmlFilePath != "" ]];then
   rm -rf $xmlFilePath/*Text
fi
rm -rf OutPut
