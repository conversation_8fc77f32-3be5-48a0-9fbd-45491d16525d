<?xml version="1.0" ?>
<data>
  <!--NOTE: This is an ** Autogenerated file **-->
  <!--NOTE: Sector size is 4096bytes-->
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="aop.mbn" label="aop_a" num_partition_sectors="128" partofsingleimage="false" physical_partition_number="4" readbackverify="false" size_in_KB="512.0" sparse="false" start_byte_hex="0x6000" start_sector="6"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="aop.mbn" label="aop_recovery" num_partition_sectors="128" partofsingleimage="false" physical_partition_number="4" readbackverify="false" size_in_KB="512.0" sparse="false" start_byte_hex="0x86000" start_sector="134"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="tz.mbn" label="tz_a" num_partition_sectors="1024" partofsingleimage="false" physical_partition_number="4" readbackverify="false" size_in_KB="4096.0" sparse="false" start_byte_hex="0x106000" start_sector="262"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="tz.mbn" label="tz_recovery" num_partition_sectors="1024" partofsingleimage="false" physical_partition_number="4" readbackverify="false" size_in_KB="4096.0" sparse="false" start_byte_hex="0x506000" start_sector="1286"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="mifs_hyp_la.img" label="hyp_a" num_partition_sectors="1792" partofsingleimage="false" physical_partition_number="4" readbackverify="false" size_in_KB="7168.0" sparse="false" start_byte_hex="0x906000" start_sector="2310"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="mifs_hyp_la.img" label="hyp_recovery" num_partition_sectors="1792" partofsingleimage="false" physical_partition_number="4" readbackverify="false" size_in_KB="7168.0" sparse="false" start_byte_hex="0x1006000" start_sector="4102"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="abl_fastboot.elf" label="abl_a" num_partition_sectors="256" partofsingleimage="false" physical_partition_number="4" readbackverify="false" size_in_KB="1024.0" sparse="false" start_byte_hex="0x1706000" start_sector="5894"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="km4virt.mbn" label="keymaster_a" num_partition_sectors="128" partofsingleimage="false" physical_partition_number="4" readbackverify="false" size_in_KB="512.0" sparse="false" start_byte_hex="0x1806000" start_sector="6150"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="cmnlib.mbn" label="cmnlib_a" num_partition_sectors="128" partofsingleimage="false" physical_partition_number="4" readbackverify="false" size_in_KB="512.0" sparse="false" start_byte_hex="0x1886000" start_sector="6278"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="cmnlib64.mbn" label="cmnlib64_a" num_partition_sectors="128" partofsingleimage="false" physical_partition_number="4" readbackverify="false" size_in_KB="512.0" sparse="false" start_byte_hex="0x1906000" start_sector="6406"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="devcfg_auto.mbn" label="devcfg_a" num_partition_sectors="32" partofsingleimage="false" physical_partition_number="4" readbackverify="false" size_in_KB="128.0" sparse="false" start_byte_hex="0x1986000" start_sector="6534"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="devcfg_auto.mbn" label="devcfg_recovery" num_partition_sectors="32" partofsingleimage="false" physical_partition_number="4" readbackverify="false" size_in_KB="128.0" sparse="false" start_byte_hex="0x19a6000" start_sector="6566"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="qupv3fw.elf" label="qupfw_a" num_partition_sectors="19" partofsingleimage="false" physical_partition_number="4" readbackverify="false" size_in_KB="76.0" sparse="false" start_byte_hex="0x19c6000" start_sector="6598"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="" label="dtbo_a" num_partition_sectors="6144" partofsingleimage="false" physical_partition_number="4" readbackverify="false" size_in_KB="24576.0" sparse="false" start_byte_hex="0x19d9000" start_sector="6617"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="uefi_sec.mbn" label="uefisecapp_a" num_partition_sectors="512" partofsingleimage="false" physical_partition_number="4" readbackverify="false" size_in_KB="2048.0" sparse="false" start_byte_hex="0x31d9000" start_sector="12761"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="aop.mbn" label="aop_b" num_partition_sectors="128" partofsingleimage="false" physical_partition_number="4" readbackverify="false" size_in_KB="512.0" sparse="false" start_byte_hex="0x33d9000" start_sector="13273"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="tz.mbn" label="tz_b" num_partition_sectors="1024" partofsingleimage="false" physical_partition_number="4" readbackverify="false" size_in_KB="4096.0" sparse="false" start_byte_hex="0x3459000" start_sector="13401"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="mifs_hyp_la.img" label="hyp_b" num_partition_sectors="1792" partofsingleimage="false" physical_partition_number="4" readbackverify="false" size_in_KB="7168.0" sparse="false" start_byte_hex="0x3859000" start_sector="14425"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="abl_fastboot.elf" label="abl_b" num_partition_sectors="256" partofsingleimage="false" physical_partition_number="4" readbackverify="false" size_in_KB="1024.0" sparse="false" start_byte_hex="0x3f59000" start_sector="16217"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="km4virt.mbn" label="keymaster_b" num_partition_sectors="128" partofsingleimage="false" physical_partition_number="4" readbackverify="false" size_in_KB="512.0" sparse="false" start_byte_hex="0x4059000" start_sector="16473"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="cmnlib.mbn" label="cmnlib_b" num_partition_sectors="128" partofsingleimage="false" physical_partition_number="4" readbackverify="false" size_in_KB="512.0" sparse="false" start_byte_hex="0x40d9000" start_sector="16601"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="cmnlib64.mbn" label="cmnlib64_b" num_partition_sectors="128" partofsingleimage="false" physical_partition_number="4" readbackverify="false" size_in_KB="512.0" sparse="false" start_byte_hex="0x4159000" start_sector="16729"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="devcfg_auto.mbn" label="devcfg_b" num_partition_sectors="32" partofsingleimage="false" physical_partition_number="4" readbackverify="false" size_in_KB="128.0" sparse="false" start_byte_hex="0x41d9000" start_sector="16857"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="qupv3fw.elf" label="qupfw_b" num_partition_sectors="19" partofsingleimage="false" physical_partition_number="4" readbackverify="false" size_in_KB="76.0" sparse="false" start_byte_hex="0x41f9000" start_sector="16889"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="" label="dtbo_b" num_partition_sectors="6144" partofsingleimage="false" physical_partition_number="4" readbackverify="false" size_in_KB="24576.0" sparse="false" start_byte_hex="0x420c000" start_sector="16908"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="uefi_sec.mbn" label="uefisecapp_b" num_partition_sectors="512" partofsingleimage="false" physical_partition_number="4" readbackverify="false" size_in_KB="2048.0" sparse="false" start_byte_hex="0x5a0c000" start_sector="23052"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="" label="devinfo" num_partition_sectors="1" partofsingleimage="false" physical_partition_number="4" readbackverify="false" size_in_KB="4.0" sparse="false" start_byte_hex="0x5c0c000" start_sector="23564"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="" label="dip" num_partition_sectors="256" partofsingleimage="false" physical_partition_number="4" readbackverify="false" size_in_KB="1024.0" sparse="false" start_byte_hex="0x5c0d000" start_sector="23565"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="" label="apdp" num_partition_sectors="64" partofsingleimage="false" physical_partition_number="4" readbackverify="false" size_in_KB="256.0" sparse="false" start_byte_hex="0x5d0d000" start_sector="23821"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="" label="spunvm" num_partition_sectors="2048" partofsingleimage="false" physical_partition_number="4" readbackverify="false" size_in_KB="8192.0" sparse="false" start_byte_hex="0x5d4d000" start_sector="23885"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="" label="splash" num_partition_sectors="8356" partofsingleimage="false" physical_partition_number="4" readbackverify="false" size_in_KB="33424.0" sparse="false" start_byte_hex="0x654d000" start_sector="25933"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="" label="limits" num_partition_sectors="1" partofsingleimage="false" physical_partition_number="4" readbackverify="false" size_in_KB="4.0" sparse="false" start_byte_hex="0x85f1000" start_sector="34289"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="" label="toolsfv" num_partition_sectors="256" partofsingleimage="false" physical_partition_number="4" readbackverify="false" size_in_KB="1024.0" sparse="false" start_byte_hex="0x85f2000" start_sector="34290"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="logfs_ufs_8mb.bin" label="logfs" num_partition_sectors="2048" partofsingleimage="false" physical_partition_number="4" readbackverify="false" size_in_KB="8192.0" sparse="false" start_byte_hex="0x86f2000" start_sector="34546"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="" label="cateloader" num_partition_sectors="512" partofsingleimage="false" physical_partition_number="4" readbackverify="false" size_in_KB="2048.0" sparse="false" start_byte_hex="0x8ef2000" start_sector="36594"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="" label="rawdump" num_partition_sectors="128000" partofsingleimage="false" physical_partition_number="4" readbackverify="false" size_in_KB="512000.0" sparse="false" start_byte_hex="0x90f2000" start_sector="37106"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="" label="logdump" num_partition_sectors="16384" partofsingleimage="false" physical_partition_number="4" readbackverify="false" size_in_KB="65536.0" sparse="false" start_byte_hex="0x284f2000" start_sector="165106"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="storsec.mbn" label="storsec" num_partition_sectors="32" partofsingleimage="false" physical_partition_number="4" readbackverify="false" size_in_KB="128.0" sparse="false" start_byte_hex="0x2c4f2000" start_sector="181490"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="multi_image.mbn" label="multiimgoem" num_partition_sectors="8" partofsingleimage="false" physical_partition_number="4" readbackverify="false" size_in_KB="32.0" sparse="false" start_byte_hex="0x2c512000" start_sector="181522"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="" label="multiimgqti" num_partition_sectors="8" partofsingleimage="false" physical_partition_number="4" readbackverify="false" size_in_KB="32.0" sparse="false" start_byte_hex="0x2c51a000" start_sector="181530"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="sec.dat" label="secdata" num_partition_sectors="7" partofsingleimage="false" physical_partition_number="4" readbackverify="false" size_in_KB="28.0" sparse="false" start_byte_hex="0x2c522000" start_sector="181538"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="" label="catefv" num_partition_sectors="128" partofsingleimage="false" physical_partition_number="4" readbackverify="false" size_in_KB="512.0" sparse="false" start_byte_hex="0x2c529000" start_sector="181545"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="" label="catecontentfv" num_partition_sectors="256" partofsingleimage="false" physical_partition_number="4" readbackverify="false" size_in_KB="1024.0" sparse="false" start_byte_hex="0x2c5a9000" start_sector="181673"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="" label="last_parti" num_partition_sectors="0" partofsingleimage="false" physical_partition_number="4" readbackverify="false" size_in_KB="0" sparse="false" start_byte_hex="0x2c6a9000" start_sector="181929"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="gpt_main4.bin" label="PrimaryGPT" num_partition_sectors="6" partofsingleimage="true" physical_partition_number="4" readbackverify="false" size_in_KB="24.0" sparse="false" start_byte_hex="0x0" start_sector="0"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="gpt_backup4.bin" label="BackupGPT" num_partition_sectors="5" partofsingleimage="true" physical_partition_number="4" readbackverify="false" size_in_KB="20.0" sparse="false" start_byte_hex="(4096*NUM_DISK_SECTORS)-20480." start_sector="NUM_DISK_SECTORS-5."/>
</data>
