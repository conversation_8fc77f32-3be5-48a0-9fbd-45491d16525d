[19:17:57] - fat_creation.py: Specified container location:
[19:17:57] -                  ./ufs/8155_la/bin/temp_NON-HLOS.bin
[19:17:57] - fat_creation.py: Specified partition size: 180.0
[19:17:57] - fat_creation.py: Float partition can't be used for fat creation, hence truncating it. New partition size : 180
[19:17:57] -                  Using fatgen from /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatgen.py
[19:17:57] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatgen.py -f -s 180 -n ./ufs/8155_la/bin/temp_NON-HLOS.bin --sectorsize 4096
-c is deprecated for size in sectors. It now specifies the sector size (512, 4096 etc.)
Using cluster size 16384 bytes
Generated 180MB FAT16 container: ./ufs/8155_la/bin/temp_NON-HLOS.bin
[19:17:57] - fat_creation.py: Adding files into fat container:
[19:17:57] -                  ./ufs/8155_la/bin/temp_NON-HLOS.bin
[19:17:57] - fat_creation.py: Found 138 files
[19:17:57] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/cmnlib.b00
[19:17:57] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/cmnlib.b00 -d image --sectorsize 4096
Container size is 188743680 bytes
adding IMAGE
Added IMAGE
adding CMNLIB.B00
Added CMNLIB.B00
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/cmnlib.b00: 102400 bytes
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:57 2025
[19:17:57] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/cmnlib.b01
[19:17:57] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/cmnlib.b01 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding CMNLIB.B01
Added CMNLIB.B01
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/cmnlib.b01: 135168 bytes
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:57 2025
[19:17:57] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/cmnlib.b02
[19:17:57] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/cmnlib.b02 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding CMNLIB.B02
Added CMNLIB.B02
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/cmnlib.b02: 495616 bytes
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:57 2025
[19:17:57] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/cmnlib.b03
[19:17:57] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/cmnlib.b03 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding CMNLIB.B03
Added CMNLIB.B03
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/cmnlib.b03: 512000 bytes
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:57 2025
[19:17:57] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/cmnlib.b04
[19:17:57] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/cmnlib.b04 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding CMNLIB.B04
Added CMNLIB.B04
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/cmnlib.b04: 528384 bytes
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:57 2025
[19:17:57] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/cmnlib.b05
[19:17:57] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/cmnlib.b05 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding CMNLIB.B05
Added CMNLIB.B05
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/cmnlib.b05: 561152 bytes
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:57 2025
[19:17:57] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/cmnlib.mdt
[19:17:57] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/cmnlib.mdt -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding CMNLIB.MDT
Added CMNLIB.MDT
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/cmnlib.mdt: 593920 bytes
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:57 2025
[19:17:57] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/cmnlib64.b00
[19:17:57] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/cmnlib64.b00 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding CMNLIB64.B00
Added CMNLIB64.B00
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/cmnlib64.b00: 610304 bytes
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:57 2025
[19:17:57] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/cmnlib64.b01
[19:17:57] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/cmnlib64.b01 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding CMNLIB64.B01
Added CMNLIB64.B01
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/cmnlib64.b01: 643072 bytes
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:57 2025
[19:17:57] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/cmnlib64.b02
[19:17:57] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/cmnlib64.b02 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding CMNLIB64.B02
Added CMNLIB64.B02
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/cmnlib64.b02: 1101824 bytes
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:57 2025
[19:17:57] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/cmnlib64.b03
[19:17:57] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/cmnlib64.b03 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding CMNLIB64.B03
Added CMNLIB64.B03
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/cmnlib64.b03: 1118208 bytes
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:57 2025
[19:17:57] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/cmnlib64.b04
[19:17:57] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/cmnlib64.b04 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding CMNLIB64.B04
Added CMNLIB64.B04
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/cmnlib64.b04: 1134592 bytes
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:57 2025
[19:17:57] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/cmnlib64.b05
[19:17:57] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/cmnlib64.b05 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding CMNLIB64.B05
Added CMNLIB64.B05
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/cmnlib64.b05: 1167360 bytes
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:57 2025
[19:17:57] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/cmnlib64.mdt
[19:17:57] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/cmnlib64.mdt -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding CMNLIB64.MDT
Added CMNLIB64.MDT
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/cmnlib64.mdt: 1200128 bytes
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:57 2025
[19:17:57] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/gptest.b00
[19:17:57] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/gptest.b00 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding GPTEST.B00
Added GPTEST.B00
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/gptest.b00: 1216512 bytes
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:57 2025
[19:17:57] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/gptest.b01
[19:17:57] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/gptest.b01 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding GPTEST.B01
Added GPTEST.B01
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/gptest.b01: 1232896 bytes
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:57 2025
[19:17:57] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/gptest.b02
[19:17:57] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/gptest.b02 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding GPTEST.B02
Added GPTEST.B02
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/gptest.b02: 1314816 bytes
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:57 2025
[19:17:57] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/gptest.b03
[19:17:57] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/gptest.b03 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding GPTEST.B03
Added GPTEST.B03
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/gptest.b03: 1331200 bytes
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:57 2025
[19:17:57] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/gptest.b04
[19:17:57] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/gptest.b04 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding GPTEST.B04
Added GPTEST.B04
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/gptest.b04: 1363968 bytes
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:57 2025
[19:17:57] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/gptest.b05
[19:17:57] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/gptest.b05 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding GPTEST.B05
Added GPTEST.B05
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/gptest.b05: 1380352 bytes
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:57 2025
[19:17:57] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/gptest.b06
[19:17:57] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/gptest.b06 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding GPTEST.B06
Added GPTEST.B06
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/gptest.b06: 1396736 bytes
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:57 2025
[19:17:57] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/gptest.b07
[19:17:57] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/gptest.b07 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding GPTEST.B07
Added GPTEST.B07
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/gptest.b07: 1429504 bytes
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:57 2025
[19:17:57] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/gptest.mdt
[19:17:57] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/gptest.mdt -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding GPTEST.MDT
Added GPTEST.MDT
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/gptest.mdt: 1445888 bytes
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:57 2025
[19:17:57] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/hdcp1.b00
[19:17:57] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/hdcp1.b00 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding HDCP1.B00
Added HDCP1.B00
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/hdcp1.b00: 1462272 bytes
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:57 2025
[19:17:57] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/hdcp1.b01
[19:17:57] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/hdcp1.b01 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding HDCP1.B01
Added HDCP1.B01
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/hdcp1.b01: 1478656 bytes
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:57 2025
[19:17:57] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/hdcp1.b02
[19:17:57] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/hdcp1.b02 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding HDCP1.B02
Added HDCP1.B02
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/hdcp1.b02: 1511424 bytes
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:57 2025
[19:17:57] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/hdcp1.b03
[19:17:57] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/hdcp1.b03 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding HDCP1.B03
Added HDCP1.B03
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/hdcp1.b03: 1527808 bytes
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:57 2025
[19:17:57] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/hdcp1.b04
[19:17:57] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/hdcp1.b04 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding HDCP1.B04
Added HDCP1.B04
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/hdcp1.b04: 1544192 bytes
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:57 2025
[19:17:57] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/hdcp1.b05
[19:17:57] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/hdcp1.b05 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding HDCP1.B05
Added HDCP1.B05
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/hdcp1.b05: 1560576 bytes
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:57 2025
[19:17:57] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/hdcp1.b06
[19:17:57] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/hdcp1.b06 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding HDCP1.B06
Added HDCP1.B06
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/hdcp1.b06: 1576960 bytes
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:57 2025
[19:17:57] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/hdcp1.b07
[19:17:57] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/hdcp1.b07 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding HDCP1.B07
Added HDCP1.B07
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/hdcp1.b07: 1593344 bytes
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:57 2025
[19:17:57] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/hdcp1.mdt
[19:17:57] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/hdcp1.mdt -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding HDCP1.MDT
Added HDCP1.MDT
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/hdcp1.mdt: 1609728 bytes
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:57 2025
[19:17:57] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/hdcp2p2.b00
[19:17:57] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/hdcp2p2.b00 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding HDCP2P2.B00
Added HDCP2P2.B00
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/hdcp2p2.b00: 1626112 bytes
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:57 2025
[19:17:57] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/hdcp2p2.b01
[19:17:57] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/hdcp2p2.b01 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding HDCP2P2.B01
Added HDCP2P2.B01
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/hdcp2p2.b01: 1642496 bytes
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:57 2025
[19:17:57] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/hdcp2p2.b02
[19:17:57] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/hdcp2p2.b02 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding HDCP2P2.B02
Added HDCP2P2.B02
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/hdcp2p2.b02: 1740800 bytes
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:57 2025
[19:17:57] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/hdcp2p2.b03
[19:17:57] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/hdcp2p2.b03 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding HDCP2P2.B03
Added HDCP2P2.B03
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/hdcp2p2.b03: 1757184 bytes
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:57 2025
[19:17:57] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/hdcp2p2.b04
[19:17:57] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/hdcp2p2.b04 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding HDCP2P2.B04
Added HDCP2P2.B04
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/hdcp2p2.b04: 1773568 bytes
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:57 2025
[19:17:57] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/hdcp2p2.b05
[19:17:57] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/hdcp2p2.b05 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding HDCP2P2.B05
Added HDCP2P2.B05
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/hdcp2p2.b05: 1789952 bytes
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:57 2025
[19:17:57] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/hdcp2p2.b06
[19:17:57] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/hdcp2p2.b06 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding HDCP2P2.B06
Added HDCP2P2.B06
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/hdcp2p2.b06: 1806336 bytes
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:57 2025
[19:17:57] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/hdcp2p2.b07
[19:17:57] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/hdcp2p2.b07 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding HDCP2P2.B07
Added HDCP2P2.B07
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/hdcp2p2.b07: 1822720 bytes
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:57 2025
[19:17:57] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/hdcp2p2.mdt
[19:17:57] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/hdcp2p2.mdt -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding HDCP2P2.MDT
Added HDCP2P2.MDT
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/hdcp2p2.mdt: 1839104 bytes
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:57 2025
[19:17:57] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/hdcpsrm.b00
[19:17:57] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/hdcpsrm.b00 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding HDCPSRM.B00
Added HDCPSRM.B00
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/hdcpsrm.b00: 1855488 bytes
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:57 2025
[19:17:57] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/hdcpsrm.b01
[19:17:57] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/hdcpsrm.b01 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding HDCPSRM.B01
Added HDCPSRM.B01
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/hdcpsrm.b01: 1871872 bytes
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:57 2025
[19:17:57] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/hdcpsrm.b02
[19:17:57] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/hdcpsrm.b02 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding HDCPSRM.B02
Added HDCPSRM.B02
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/hdcpsrm.b02: 1904640 bytes
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:57 2025
[19:17:57] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/hdcpsrm.b03
[19:17:57] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/hdcpsrm.b03 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding HDCPSRM.B03
Added HDCPSRM.B03
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/hdcpsrm.b03: 1921024 bytes
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:57 2025
[19:17:57] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/hdcpsrm.b04
[19:17:57] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/hdcpsrm.b04 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding HDCPSRM.B04
Added HDCPSRM.B04
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/hdcpsrm.b04: 1937408 bytes
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:57 2025
[19:17:57] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/hdcpsrm.b05
[19:17:57] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/hdcpsrm.b05 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding HDCPSRM.B05
Added HDCPSRM.B05
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/hdcpsrm.b05: 1953792 bytes
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:57 2025
[19:17:57] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/hdcpsrm.b06
[19:17:57] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/hdcpsrm.b06 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding HDCPSRM.B06
Added HDCPSRM.B06
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/hdcpsrm.b06: 1970176 bytes
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:57 2025
[19:17:57] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/hdcpsrm.b07
[19:17:57] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/hdcpsrm.b07 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding HDCPSRM.B07
Added HDCPSRM.B07
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/hdcpsrm.b07: 1986560 bytes
[19:17:57] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:57 2025
[19:17:57] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/hdcpsrm.mdt
[19:17:57] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/hdcpsrm.mdt -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding HDCPSRM.MDT
Added HDCPSRM.MDT
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/hdcpsrm.mdt: 2002944 bytes
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:58 2025
[19:17:58] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/km4virt.b00
[19:17:58] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/km4virt.b00 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding KM4VIRT.B00
Added KM4VIRT.B00
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/km4virt.b00: 2019328 bytes
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:58 2025
[19:17:58] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/km4virt.b01
[19:17:58] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/km4virt.b01 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding KM4VIRT.B01
Added KM4VIRT.B01
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/km4virt.b01: 2035712 bytes
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:58 2025
[19:17:58] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/km4virt.b02
[19:17:58] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/km4virt.b02 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding KM4VIRT.B02
Added KM4VIRT.B02
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/km4virt.b02: 2265088 bytes
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:58 2025
[19:17:58] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/km4virt.b03
[19:17:58] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/km4virt.b03 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding KM4VIRT.B03
Added KM4VIRT.B03
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/km4virt.b03: 2281472 bytes
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:58 2025
[19:17:58] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/km4virt.b04
[19:17:58] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/km4virt.b04 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding KM4VIRT.B04
Added KM4VIRT.B04
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/km4virt.b04: 2314240 bytes
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:58 2025
[19:17:58] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/km4virt.b05
[19:17:58] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/km4virt.b05 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding KM4VIRT.B05
Added KM4VIRT.B05
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/km4virt.b05: 2330624 bytes
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:58 2025
[19:17:58] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/km4virt.b06
[19:17:58] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/km4virt.b06 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding KM4VIRT.B06
Added KM4VIRT.B06
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/km4virt.b06: 2347008 bytes
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:58 2025
[19:17:58] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/km4virt.b07
[19:17:58] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/km4virt.b07 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding KM4VIRT.B07
Added KM4VIRT.B07
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/km4virt.b07: 2363392 bytes
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:58 2025
[19:17:58] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/km4virt.mdt
[19:17:58] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/km4virt.mdt -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding KM4VIRT.MDT
Added KM4VIRT.MDT
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/km4virt.mdt: 2379776 bytes
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:58 2025
[19:17:58] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/modem.b00
[19:17:58] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/modem.b00 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding MODEM.B00
Added MODEM.B00
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/modem.b00: 2396160 bytes
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:58 2025
[19:17:58] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/modem.b01
[19:17:58] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/modem.b01 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding MODEM.B01
Added MODEM.B01
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/modem.b01: 2412544 bytes
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:58 2025
[19:17:58] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/modem.b02
[19:17:58] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/modem.b02 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding MODEM.B02
Added MODEM.B02
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/modem.b02: 2428928 bytes
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:58 2025
[19:17:58] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/modem.b03
[19:17:58] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/modem.b03 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding MODEM.B03
Added MODEM.B03
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/modem.b03: 2674688 bytes
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:58 2025
[19:17:58] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/modem.b04
[19:17:58] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/modem.b04 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding MODEM.B04
Added MODEM.B04
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/modem.b04: 2740224 bytes
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:58 2025
[19:17:58] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/modem.b05
[19:17:58] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/modem.b05 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding MODEM.B05
Added MODEM.B05
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/modem.b05: 2756608 bytes
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:58 2025
[19:17:58] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/modem.b06
[19:17:58] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/modem.b06 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding MODEM.B06
Added MODEM.B06
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/modem.b06: 2805760 bytes
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:58 2025
[19:17:58] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/modem.b07
[19:17:58] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/modem.b07 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding MODEM.B07
Added MODEM.B07
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/modem.b07: 2936832 bytes
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:58 2025
[19:17:58] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/modem.b08
[19:17:58] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/modem.b08 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding MODEM.B08
Added MODEM.B08
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/modem.b08: 6000640 bytes
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:58 2025
[19:17:58] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/modem.b10
[19:17:58] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/modem.b10 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding MODEM.B10
Added MODEM.B10
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/modem.b10: 6017024 bytes
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:58 2025
[19:17:58] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/modem.b11
[19:17:58] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/modem.b11 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding MODEM.B11
Added MODEM.B11
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/modem.b11: 7376896 bytes
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:58 2025
[19:17:58] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/modem.b12
[19:17:58] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/modem.b12 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding MODEM.B12
Added MODEM.B12
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/modem.b12: 9490432 bytes
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:58 2025
[19:17:58] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/modem.b14
[19:17:58] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/modem.b14 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding MODEM.B14
Added MODEM.B14
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/modem.b14: 9539584 bytes
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:58 2025
[19:17:58] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/modem.b15
[19:17:58] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/modem.b15 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding MODEM.B15
Added MODEM.B15
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/modem.b15: 9572352 bytes
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:58 2025
[19:17:58] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/modem.mdt
[19:17:58] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/modem.mdt -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding MODEM.MDT
Added MODEM.MDT
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/modem.mdt: 9588736 bytes
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:58 2025
[19:17:58] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/npu.b00
[19:17:58] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/npu.b00 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding NPU.B00
Added NPU.B00
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/npu.b00: 9605120 bytes
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:58 2025
[19:17:58] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/npu.b01
[19:17:58] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/npu.b01 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding NPU.B01
Added NPU.B01
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/npu.b01: 9621504 bytes
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:58 2025
[19:17:58] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/npu.b02
[19:17:58] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/npu.b02 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding NPU.B02
Added NPU.B02
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/npu.b02: 9670656 bytes
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:58 2025
[19:17:58] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/npu.b03
[19:17:58] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/npu.b03 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding NPU.B03
Added NPU.B03
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/npu.b03: 9687040 bytes
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:58 2025
[19:17:58] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/npu.mdt
[19:17:58] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/npu.mdt -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding NPU.MDT
Added NPU.MDT
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/npu.mdt: 9703424 bytes
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:58 2025
[19:17:58] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/smplap32.b00
[19:17:58] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/smplap32.b00 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding SMPLAP32.B00
Added SMPLAP32.B00
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/smplap32.b00: 9719808 bytes
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:58 2025
[19:17:58] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/smplap32.b01
[19:17:58] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/smplap32.b01 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding SMPLAP32.B01
Added SMPLAP32.B01
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/smplap32.b01: 9736192 bytes
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:58 2025
[19:17:58] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/smplap32.b02
[19:17:58] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/smplap32.b02 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding SMPLAP32.B02
Added SMPLAP32.B02
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/smplap32.b02: 10014720 bytes
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:58 2025
[19:17:58] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/smplap32.b03
[19:17:58] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/smplap32.b03 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding SMPLAP32.B03
Added SMPLAP32.B03
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/smplap32.b03: 10031104 bytes
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:58 2025
[19:17:58] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/smplap32.b04
[19:17:58] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/smplap32.b04 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding SMPLAP32.B04
Added SMPLAP32.B04
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/smplap32.b04: 11440128 bytes
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:58 2025
[19:17:58] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/smplap32.b05
[19:17:58] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/smplap32.b05 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding SMPLAP32.B05
Added SMPLAP32.B05
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/smplap32.b05: 11456512 bytes
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:58 2025
[19:17:58] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/smplap32.b06
[19:17:58] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/smplap32.b06 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding SMPLAP32.B06
Added SMPLAP32.B06
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/smplap32.b06: 11472896 bytes
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:58 2025
[19:17:58] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/smplap32.b07
[19:17:58] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/smplap32.b07 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding SMPLAP32.B07
Added SMPLAP32.B07
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/smplap32.b07: 11522048 bytes
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:58 2025
[19:17:58] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/smplap32.mdt
[19:17:58] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/smplap32.mdt -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding SMPLAP32.MDT
Added SMPLAP32.MDT
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/smplap32.mdt: 11538432 bytes
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:58 2025
[19:17:58] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/smplap64.b00
[19:17:58] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/smplap64.b00 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding SMPLAP64.B00
Added SMPLAP64.B00
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/smplap64.b00: 11554816 bytes
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:58 2025
[19:17:58] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/smplap64.b01
[19:17:58] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/smplap64.b01 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding SMPLAP64.B01
Added SMPLAP64.B01
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/smplap64.b01: 11571200 bytes
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:58 2025
[19:17:58] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/smplap64.b02
[19:17:58] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/smplap64.b02 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding SMPLAP64.B02
Added SMPLAP64.B02
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/smplap64.b02: 11898880 bytes
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:58 2025
[19:17:58] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/smplap64.b03
[19:17:58] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/smplap64.b03 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding SMPLAP64.B03
Added SMPLAP64.B03
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/smplap64.b03: 11915264 bytes
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:58 2025
[19:17:58] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/smplap64.b04
[19:17:58] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/smplap64.b04 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding SMPLAP64.B04
Added SMPLAP64.B04
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/smplap64.b04: 13324288 bytes
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:58 2025
[19:17:58] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/smplap64.b05
[19:17:58] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/smplap64.b05 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding SMPLAP64.B05
Added SMPLAP64.B05
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/smplap64.b05: 13340672 bytes
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:58 2025
[19:17:58] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/smplap64.b06
[19:17:58] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/smplap64.b06 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding SMPLAP64.B06
Added SMPLAP64.B06
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/smplap64.b06: 13357056 bytes
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:58 2025
[19:17:58] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/smplap64.b07
[19:17:58] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/smplap64.b07 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding SMPLAP64.B07
Added SMPLAP64.B07
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/smplap64.b07: 13422592 bytes
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:58 2025
[19:17:58] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/smplap64.mdt
[19:17:58] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/smplap64.mdt -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding SMPLAP64.MDT
Added SMPLAP64.MDT
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/smplap64.mdt: 13438976 bytes
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:58 2025
[19:17:58] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/venus.b00
[19:17:58] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/venus.b00 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding VENUS.B00
Added VENUS.B00
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/venus.b00: 13455360 bytes
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:58 2025
[19:17:58] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/venus.b01
[19:17:58] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/venus.b01 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding VENUS.B01
Added VENUS.B01
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/venus.b01: 13471744 bytes
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:58 2025
[19:17:58] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/venus.b02
[19:17:58] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/venus.b02 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding VENUS.B02
Added VENUS.B02
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/venus.b02: 14536704 bytes
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:58 2025
[19:17:58] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/venus.b03
[19:17:58] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/venus.b03 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding VENUS.B03
Added VENUS.B03
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/venus.b03: 14585856 bytes
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:58 2025
[19:17:58] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/venus.b04
[19:17:58] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/venus.b04 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding VENUS.B04
Added VENUS.B04
[19:17:58] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/venus.b04: 14602240 bytes
[19:17:59] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:58 2025
[19:17:59] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/venus.mdt
[19:17:59] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/venus.mdt -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding VENUS.MDT
Added VENUS.MDT
[19:17:59] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/venus.mdt: 14618624 bytes
[19:17:59] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:59 2025
[19:17:59] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/widevine.b00
[19:17:59] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/widevine.b00 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding WIDEVINE.B00
Added WIDEVINE.B00
[19:17:59] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/widevine.b00: 14635008 bytes
[19:17:59] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:59 2025
[19:17:59] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/widevine.b01
[19:17:59] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/widevine.b01 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding WIDEVINE.B01
Added WIDEVINE.B01
[19:17:59] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/widevine.b01: 14651392 bytes
[19:17:59] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:59 2025
[19:17:59] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/widevine.b02
[19:17:59] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/widevine.b02 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding WIDEVINE.B02
Added WIDEVINE.B02
[19:17:59] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/widevine.b02: 14798848 bytes
[19:17:59] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:59 2025
[19:17:59] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/widevine.b03
[19:17:59] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/widevine.b03 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding WIDEVINE.B03
Added WIDEVINE.B03
[19:17:59] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/widevine.b03: 14815232 bytes
[19:17:59] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:59 2025
[19:17:59] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/widevine.b04
[19:17:59] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/widevine.b04 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding WIDEVINE.B04
Added WIDEVINE.B04
[19:17:59] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/widevine.b04: 14831616 bytes
[19:17:59] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:59 2025
[19:17:59] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/widevine.b05
[19:17:59] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/widevine.b05 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding WIDEVINE.B05
Added WIDEVINE.B05
[19:17:59] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/widevine.b05: 14848000 bytes
[19:17:59] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:59 2025
[19:17:59] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/widevine.b06
[19:17:59] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/widevine.b06 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding WIDEVINE.B06
Added WIDEVINE.B06
[19:17:59] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/widevine.b06: 14864384 bytes
[19:17:59] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:59 2025
[19:17:59] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/widevine.b07
[19:17:59] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/widevine.b07 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding WIDEVINE.B07
Added WIDEVINE.B07
[19:17:59] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/widevine.b07: 14897152 bytes
[19:17:59] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:59 2025
[19:17:59] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/widevine.mdt
[19:17:59] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/widevine.mdt -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding WIDEVINE.MDT
Added WIDEVINE.MDT
[19:17:59] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/pil_split_bins/widevine.mdt: 14913536 bytes
[19:17:59] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:59 2025
[19:17:59] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/mpss_8155/modem_proc/build/ms/bin/sm8150.gennmgw.prod/qdsp6m.qdb
[19:17:59] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/mpss_8155/modem_proc/build/ms/bin/sm8150.gennmgw.prod/qdsp6m.qdb -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding QDSP6M.QDB
Added QDSP6M.QDB
[19:17:59] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/mpss_8155/modem_proc/build/ms/bin/sm8150.gennmgw.prod/qdsp6m.qdb: 15077376 bytes
[19:17:59] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:59 2025
[19:17:59] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/mpss_8155/modem_proc/build/ms/servreg/sm8150.gennmgw.prodQ/modemr.jsn
[19:17:59] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/mpss_8155/modem_proc/build/ms/servreg/sm8150.gennmgw.prodQ/modemr.jsn -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding MODEMR.JSN
Added MODEMR.JSN
[19:17:59] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/mpss_8155/modem_proc/build/ms/servreg/sm8150.gennmgw.prodQ/modemr.jsn: 15093760 bytes
[19:17:59] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:59 2025
[19:17:59] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/wlan_gen/wlan_proc/build/ms/bin/7605.wlanfw.eval_v2_TO_ll/amss.bin
[19:17:59] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/wlan_gen/wlan_proc/build/ms/bin/7605.wlanfw.eval_v2_TO_ll/amss.bin -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding AMSS.BIN
Added AMSS.BIN
[19:17:59] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/wlan_gen/wlan_proc/build/ms/bin/7605.wlanfw.eval_v2_TO_ll/amss.bin: 18010112 bytes
[19:17:59] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:59 2025
[19:17:59] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/wlan_gen/wlan_proc/build/ms/bin/7605.wlanfw.eval_v2_TO_ll_ftm/genoaftm.bin
[19:17:59] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/wlan_gen/wlan_proc/build/ms/bin/7605.wlanfw.eval_v2_TO_ll_ftm/genoaftm.bin -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding GENOAFTM.BIN
Added GENOAFTM.BIN
[19:17:59] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/wlan_gen/wlan_proc/build/ms/bin/7605.wlanfw.eval_v2_TO_ll_ftm/genoaftm.bin: 20353024 bytes
[19:17:59] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:59 2025
[19:17:59] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/wlan_gen/wlan_proc/wlan/halphy_tools/host/bdfUtil/Genoa/bdf/bdwlan02.b03
[19:17:59] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/wlan_gen/wlan_proc/wlan/halphy_tools/host/bdfUtil/Genoa/bdf/bdwlan02.b03 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding BDWLAN02.B03
Added BDWLAN02.B03
[19:17:59] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/wlan_gen/wlan_proc/wlan/halphy_tools/host/bdfUtil/Genoa/bdf/bdwlan02.b03: 20385792 bytes
[19:17:59] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:59 2025
[19:17:59] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/wlan_gen/wlan_proc/wlan/halphy_tools/host/bdfUtil/Genoa/bdf/bdwlan03.b03
[19:17:59] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/wlan_gen/wlan_proc/wlan/halphy_tools/host/bdfUtil/Genoa/bdf/bdwlan03.b03 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding BDWLAN03.B03
Added BDWLAN03.B03
[19:17:59] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/wlan_gen/wlan_proc/wlan/halphy_tools/host/bdfUtil/Genoa/bdf/bdwlan03.b03: 20418560 bytes
[19:17:59] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:59 2025
[19:17:59] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/wlan_gen/wlan_proc/wlan/halphy_tools/host/bdfUtil/Genoa/bdf/bdwlan03.b04
[19:17:59] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/wlan_gen/wlan_proc/wlan/halphy_tools/host/bdfUtil/Genoa/bdf/bdwlan03.b04 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding BDWLAN03.B04
Added BDWLAN03.B04
[19:17:59] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/wlan_gen/wlan_proc/wlan/halphy_tools/host/bdfUtil/Genoa/bdf/bdwlan03.b04: 20451328 bytes
[19:17:59] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:59 2025
[19:17:59] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/wlan_gen/wlan_proc/wlan/halphy_tools/host/bdfUtil/Genoa/bdf/bdwlan03.b05
[19:17:59] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/wlan_gen/wlan_proc/wlan/halphy_tools/host/bdfUtil/Genoa/bdf/bdwlan03.b05 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding BDWLAN03.B05
Added BDWLAN03.B05
[19:17:59] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/wlan_gen/wlan_proc/wlan/halphy_tools/host/bdfUtil/Genoa/bdf/bdwlan03.b05: 20484096 bytes
[19:17:59] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:59 2025
[19:17:59] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/wlan_gen/wlan_proc/wlan/halphy_tools/host/bdfUtil/Genoa/bdf/bdwlan03.b06
[19:17:59] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/wlan_gen/wlan_proc/wlan/halphy_tools/host/bdfUtil/Genoa/bdf/bdwlan03.b06 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding BDWLAN03.B06
Added BDWLAN03.B06
[19:17:59] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/wlan_gen/wlan_proc/wlan/halphy_tools/host/bdfUtil/Genoa/bdf/bdwlan03.b06: 20516864 bytes
[19:17:59] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:59 2025
[19:17:59] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/wlan_gen/wlan_proc/wlan/halphy_tools/host/bdfUtil/Genoa/bdf/bdwlan04.b01
[19:17:59] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/wlan_gen/wlan_proc/wlan/halphy_tools/host/bdfUtil/Genoa/bdf/bdwlan04.b01 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding BDWLAN04.B01
Added BDWLAN04.B01
[19:17:59] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/wlan_gen/wlan_proc/wlan/halphy_tools/host/bdfUtil/Genoa/bdf/bdwlan04.b01: 20549632 bytes
[19:17:59] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:59 2025
[19:17:59] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/wlan_hst/wlan_proc/config/bsp/cnss_ram_v1_TO_link_patched/build/6390.wlanfw.eval_v1_TO/amss.bin
[19:17:59] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/wlan_hst/wlan_proc/config/bsp/cnss_ram_v1_TO_link_patched/build/6390.wlanfw.eval_v1_TO/amss.bin -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding AMSS.BIN
Added AMSS.BIN
[19:17:59] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/wlan_hst/wlan_proc/config/bsp/cnss_ram_v1_TO_link_patched/build/6390.wlanfw.eval_v1_TO/amss.bin: 24776704 bytes
[19:17:59] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:59 2025
[19:17:59] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/wlan_hst/wlan_proc/config/bsp/cnss_ram_v2_TO_link_patched/build/6390.wlanfw.eval_v2_TO/amss20.bin
[19:17:59] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/wlan_hst/wlan_proc/config/bsp/cnss_ram_v2_TO_link_patched/build/6390.wlanfw.eval_v2_TO/amss20.bin -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding AMSS20.BIN
Added AMSS20.BIN
[19:17:59] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/wlan_hst/wlan_proc/config/bsp/cnss_ram_v2_TO_link_patched/build/6390.wlanfw.eval_v2_TO/amss20.bin: 28987392 bytes
[19:17:59] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:59 2025
[19:17:59] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/wlan_hst/wlan_proc/wlan/phyrf_svc/tools/bdfUtil/device/bdf/qca639x/bdwlan02.e01
[19:17:59] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/wlan_hst/wlan_proc/wlan/phyrf_svc/tools/bdfUtil/device/bdf/qca639x/bdwlan02.e01 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding BDWLAN02.E01
Added BDWLAN02.E01
[19:17:59] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/wlan_hst/wlan_proc/wlan/phyrf_svc/tools/bdfUtil/device/bdf/qca639x/bdwlan02.e01: 29052928 bytes
[19:17:59] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:59 2025
[19:17:59] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/wlan_hst/wlan_proc/wlan/phyrf_svc/tools/bdfUtil/device/bdf/qca639x/bdwlan02.e02
[19:17:59] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/wlan_hst/wlan_proc/wlan/phyrf_svc/tools/bdfUtil/device/bdf/qca639x/bdwlan02.e02 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding BDWLAN02.E02
Added BDWLAN02.E02
[19:17:59] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/wlan_hst/wlan_proc/wlan/phyrf_svc/tools/bdfUtil/device/bdf/qca639x/bdwlan02.e02: 29118464 bytes
[19:17:59] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:59 2025
[19:17:59] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/wlan_hst/wlan_proc/wlan/phyrf_svc/tools/bdfUtil/device/bdf/qca639x/bdwlan02.e03
[19:17:59] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/wlan_hst/wlan_proc/wlan/phyrf_svc/tools/bdfUtil/device/bdf/qca639x/bdwlan02.e03 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding BDWLAN02.E03
Added BDWLAN02.E03
[19:17:59] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/wlan_hst/wlan_proc/wlan/phyrf_svc/tools/bdfUtil/device/bdf/qca639x/bdwlan02.e03: 29184000 bytes
[19:17:59] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:59 2025
[19:17:59] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/wlan_rome/cnss_proc/wlan/fw/target/.output/AR6320/hw.3/bin/Data.msc
[19:17:59] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/wlan_rome/cnss_proc/wlan/fw/target/.output/AR6320/hw.3/bin/Data.msc -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding DATA.MSC
Added DATA.MSC
[19:17:59] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/wlan_rome/cnss_proc/wlan/fw/target/.output/AR6320/hw.3/bin/Data.msc: 29282304 bytes
[19:17:59] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:59 2025
[19:17:59] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/wlan_rome/cnss_proc/wlan/fw/target/halphy/ftm/host/systemtools/tools/eepromUtil/qc6174/bdwlan30.b01
[19:17:59] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/wlan_rome/cnss_proc/wlan/fw/target/halphy/ftm/host/systemtools/tools/eepromUtil/qc6174/bdwlan30.b01 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding BDWLAN30.B01
Added BDWLAN30.B01
[19:17:59] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/wlan_rome/cnss_proc/wlan/fw/target/halphy/ftm/host/systemtools/tools/eepromUtil/qc6174/bdwlan30.b01: 29298688 bytes
[19:17:59] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:59 2025
[19:17:59] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/wlan_rome/cnss_proc/wlan/fw/target/halphy/ftm/host/systemtools/tools/eepromUtil/qc6174/bdwlan30.b03
[19:17:59] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/wlan_rome/cnss_proc/wlan/fw/target/halphy/ftm/host/systemtools/tools/eepromUtil/qc6174/bdwlan30.b03 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding BDWLAN30.B03
Added BDWLAN30.B03
[19:17:59] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/wlan_rome/cnss_proc/wlan/fw/target/halphy/ftm/host/systemtools/tools/eepromUtil/qc6174/bdwlan30.b03: 29315072 bytes
[19:17:59] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:59 2025
[19:17:59] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/wlan_rome/cnss_proc/wlan/fw/target/halphy/ftm/host/systemtools/tools/eepromUtil/qc6174/bdwlan30.b06
[19:17:59] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/wlan_rome/cnss_proc/wlan/fw/target/halphy/ftm/host/systemtools/tools/eepromUtil/qc6174/bdwlan30.b06 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding BDWLAN30.B06
Added BDWLAN30.B06
[19:17:59] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/wlan_rome/cnss_proc/wlan/fw/target/halphy/ftm/host/systemtools/tools/eepromUtil/qc6174/bdwlan30.b06: 29331456 bytes
[19:17:59] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:59 2025
[19:17:59] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/wlan_rome/cnss_proc/wlan/fw/target/halphy/ftm/host/systemtools/tools/eepromUtil/qc6174/bdwlan30.b21
[19:17:59] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/wlan_rome/cnss_proc/wlan/fw/target/halphy/ftm/host/systemtools/tools/eepromUtil/qc6174/bdwlan30.b21 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding BDWLAN30.B21
Added BDWLAN30.B21
[19:17:59] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/wlan_rome/cnss_proc/wlan/fw/target/halphy/ftm/host/systemtools/tools/eepromUtil/qc6174/bdwlan30.b21: 29347840 bytes
[19:17:59] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:59 2025
[19:17:59] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/wlan_rome/cnss_proc/wlan/fw/target/halphy/ftm/host/systemtools/tools/eepromUtil/qc6174/bdwlan30.b25
[19:17:59] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/wlan_rome/cnss_proc/wlan/fw/target/halphy/ftm/host/systemtools/tools/eepromUtil/qc6174/bdwlan30.b25 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding BDWLAN30.B25
Added BDWLAN30.B25
[19:17:59] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/wlan_rome/cnss_proc/wlan/fw/target/halphy/ftm/host/systemtools/tools/eepromUtil/qc6174/bdwlan30.b25: 29364224 bytes
[19:17:59] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:59 2025
[19:17:59] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/wlan_rome/cnss_proc/wlan/fw/target/halphy/ftm/host/systemtools/tools/eepromUtil/qc6174/bdwlan30.b31
[19:17:59] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/wlan_rome/cnss_proc/wlan/fw/target/halphy/ftm/host/systemtools/tools/eepromUtil/qc6174/bdwlan30.b31 -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding BDWLAN30.B31
Added BDWLAN30.B31
[19:17:59] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/wlan_rome/cnss_proc/wlan/fw/target/halphy/ftm/host/systemtools/tools/eepromUtil/qc6174/bdwlan30.b31: 29380608 bytes
[19:17:59] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:17:59 2025
[19:17:59] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/wlan_rome/cnss_proc/wlan/fw/target/halphy/ftm/host/systemtools/tools/eepromUtil/qc6174/bdwlan30.bin
[19:17:59] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/wlan_rome/cnss_proc/wlan/fw/target/halphy/ftm/host/systemtools/tools/eepromUtil/qc6174/bdwlan30.bin -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding BDWLAN30.BIN
Added BDWLAN30.BIN
[19:18:00] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/wlan_rome/cnss_proc/wlan/fw/target/halphy/ftm/host/systemtools/tools/eepromUtil/qc6174/bdwlan30.bin: 29396992 bytes
[19:18:00] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:18:00 2025
[19:18:00] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/wlan_rome/cnss_proc/wlan/fw/target/pcie_dst/AR6320/hw.3/bin/otp30.bin
[19:18:00] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/wlan_rome/cnss_proc/wlan/fw/target/pcie_dst/AR6320/hw.3/bin/otp30.bin -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding OTP30.BIN
Added OTP30.BIN
[19:18:00] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/wlan_rome/cnss_proc/wlan/fw/target/pcie_dst/AR6320/hw.3/bin/otp30.bin: 29429760 bytes
[19:18:00] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:18:00 2025
[19:18:00] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/wlan_rome/cnss_proc/wlan/fw/target/pcie_dst/AR6320/hw.3/bin/qwlan30.bin
[19:18:00] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/wlan_rome/cnss_proc/wlan/fw/target/pcie_dst/AR6320/hw.3/bin/qwlan30.bin -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding QWLAN30.BIN
Added QWLAN30.BIN
[19:18:00] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/wlan_rome/cnss_proc/wlan/fw/target/pcie_dst/AR6320/hw.3/bin/qwlan30.bin: 30085120 bytes
[19:18:00] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:18:00 2025
[19:18:00] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/wlan_rome/cnss_proc/wlan/fw/target/pcie_dst/AR6320/hw.3/bin/utf30.bin
[19:18:00] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/wlan_rome/cnss_proc/wlan/fw/target/pcie_dst/AR6320/hw.3/bin/utf30.bin -d image --sectorsize 4096
Container size is 188743680 bytes
Added IMAGE
adding UTF30.BIN
Added UTF30.BIN
[19:18:00] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/wlan_rome/cnss_proc/wlan/fw/target/pcie_dst/AR6320/hw.3/bin/utf30.bin: 30494720 bytes
[19:18:00] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:18:00 2025
[19:18:00] - Creating the file ver_info.txt  to hold the Meta information at ./ufs/8155_la/bin/ver_info.txt
[19:18:00] - fat_creation.py: Adding ver_info.txt into fat container:
[19:18:00] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_NON-HLOS.bin -f ./ufs/8155_la/bin/ver_info.txt -d verinfo --sectorsize 4096
Container size is 188743680 bytes
adding VERINFO
Added VERINFO
adding VER_INFO.TXT
Added VER_INFO.TXT
[19:18:00] - ./ufs/8155_la/bin/temp_NON-HLOS.bin size after adding   ./ufs/8155_la/bin/ver_info.txt: 30527488 bytes
[19:18:00] - ./ufs/8155_la/bin/temp_NON-HLOS.bin last modified time stamp   : Tue Aug  5 19:18:00 2025
