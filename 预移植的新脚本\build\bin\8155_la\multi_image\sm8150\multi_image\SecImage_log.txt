Added Multi-Image Sign & Integrity entry for image with SW_ID=0x1c
Added Multi-Image Sign & Integrity entry for image with SW_ID=0x2
Added Multi-Image Sign & Integrity entry for image with SW_ID=0x7
Added Multi-Image Sign & Integrity entry for image with SW_ID=0x15
Added Multi-Image Sign & Integrity entry for image with SW_ID=0x5
Added Multi-Image Sign & Integrity entry for image with SW_ID=0x21
Added Multi-Image Sign & Integrity entry for image with SW_ID=0xe
Added Multi-Image Sign & Integrity entry for image with SW_ID=0x1f and APP_ID=0x555
Added Multi-Image Sign & Integrity entry for image with SW_ID=0x1f and APP_ID=0x556
Added Multi-Image Sign & Integrity entry for image with SW_ID=0x28
Added Multi-Image Sign & Integrity entry for image with SW_ID=0x24
Performing OEM sign on image: Multi-Image Sign & Integrity image
Signed image is stored at /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/bin/8155_la/multi_image/sm8150/multi_image/multi_image.mbn
OEM signed image with RSAPSS
Image /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/bin/8155_la/multi_image/sm8150/multi_image/multi_image.mbn signature is valid
Image /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/build/bin/8155_la/multi_image/sm8150/multi_image/multi_image.mbn is not encrypted

Base Properties: 
| Integrity Check             | True  |
| Signed                      | True  |
| Encrypted                   | False |
| Size of signature           | 256   |
| Size of one cert            | 2048  |
| Num of certs in cert chain  | 3     |
| Number of root certs        | 1     |
| Maximum number of root certs| None  |
| Cert chain size             | 6144  |

ELF Properties: 
Header: 
| Magic                      | ELF                           |
| Class                      | ELF32                          |
| Data                       | 2's complement, little endian  |
| Version                    | 1 (Current)                    |
| OS/ABI                     | No extensions or unspecified   |
| ABI Version                | 0                              |
| Type                       | NONE (No file type)            |
| Machine                    | Advanced RISC Machines ARM     |
| Version                    | 0x1                            |
| Entry address              | 0x148fc000                     |
| Program headers offset     | 0x00000034                     |
| Section headers offset     | 0x00000000                     |
| Flags                      | 0x00000000                     |
| ELF header size            | 52                             |
| Program headers size       | 32                             |
| Number of program headers  | 1                              |
| Section headers size       | 0                              |
| Number of section headers  | 0                              |
| String table section index | 0                              |

Program Headers: 
| Num | Type | Offset | VirtAddr | PhysAddr | FileSize | MemSize | Flags | Align |
|-----|------|--------|----------|----------|----------|---------|-------|-------|
|  1  | LOAD | 0x3000 |0x148fc000|0x148fc000|  0x378   |  0x378  |  0x0  | 0x1000|

Hash Segment Properties: 
| Header Size     | 168B  |
| Hash Algorithm  | sha384|

Header: 
| cert_chain_ptr              | 0xffffffff  |
| cert_chain_size             | 0x00001800  |
| cert_chain_size_qti         | 0x00000000  |
| code_size                   | 0x00000090  |
| image_id                    | 0x00000000  |
| image_size                  | 0x00001990  |
| metadata_major_version      | 0x00000000  |
| metadata_major_version_qti  | 0x00000000  |
| metadata_minor_version      | 0x00000000  |
| metadata_minor_version_qti  | 0x00000000  |
| metadata_size               | 0x00000078  |
| metadata_size_qti           | 0x00000000  |
| sig_ptr                     | 0xffffffff  |
| sig_size                    | 0x00000100  |
| sig_size_qti                | 0x00000000  |
| version                     | 0x00000006  |
Metadata:
| anti_rollback_version        | 0x00000000  |
| app_id                       | 0x00000000  |
| debug                        | 0x00000000  |
| hw_id                        | 0x00000000  |
| in_use_soc_hw_version        | 0x00000001  |
| model_id                     | 0x00000000  |
| mrc_index                    | 0x00000000  |
| multi_serial_numbers         | 0x00000000  |
| oem_id                       | 0x00000000  |
| oem_id_independent           | 0x00000000  |
| root_revoke_activate_enable  | 0x00000000  |
| rot_en                       | 0x00000000  |
| soc_vers                     | 0x00006003  |
| sw_id                        | 0x00000022  |
| uie_key_switch_enable        | 0x00000000  |
| use_serial_number_in_signing | 0x00000000  |

Multi-Image Sign & Integrity Segment Properties: 
Header:
| Segment Size       | 888B  |
| Magic              | MULT  |
| Version            | 0     |
| Hash Algorithm     | sha384|
| Num Image Entries  | 11    |
Image Entries: 
| Entry 1  | SW_ID: 0x0000001c APP_ID: 0x00000000  |
| Entry 2  | SW_ID: 0x00000002 APP_ID: 0x00000000  |
| Entry 3  | SW_ID: 0x00000007 APP_ID: 0x00000000  |
| Entry 4  | SW_ID: 0x00000015 APP_ID: 0x00000000  |
| Entry 5  | SW_ID: 0x00000005 APP_ID: 0x00000000  |
| Entry 6  | SW_ID: 0x00000021 APP_ID: 0x00000000  |
| Entry 7  | SW_ID: 0x0000000e APP_ID: 0x00000000  |
| Entry 8  | SW_ID: 0x0000001f APP_ID: 0x00000555  |
| Entry 9  | SW_ID: 0x0000001f APP_ID: 0x00000556  |
| Entry 10 | SW_ID: 0x00000028 APP_ID: 0x00000000  |
| Entry 11 | SW_ID: 0x00000024 APP_ID: 0x00000000  |


