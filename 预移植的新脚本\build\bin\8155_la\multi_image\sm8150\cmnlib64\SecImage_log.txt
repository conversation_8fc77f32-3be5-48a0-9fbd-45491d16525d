OEM signed image with RSAPSS
QTI PROD signed image with RSAPSS
ERROR: Following validations failed for the image:
       1. Following signing attributes do not match: 
          | Attribute | Image | Config |
          |-----------|-------|--------|
          |   debug   |  0x1  |  0x0   |
          
Image /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/tz_8155/trustzone_images/build/ms/bin/YAQAANAA/cmnlib64.mbn is not encrypted

Base Properties: 
| Integrity Check             | True  |
| Signed                      | True  |
| Encrypted                   | False |
| Size of signature           | 256   |
| Size of one cert            | 2048  |
| Num of certs in cert chain  | 3     |
| Number of root certs        | 1     |
| Maximum number of root certs| 4     |
| Cert chain size             | 12288 |

ELF Properties: 
Header: 
| Magic                      | ELF                           |
| Class                      | ELF64                          |
| Data                       | 2's complement, little endian  |
| Version                    | 1 (Current)                    |
| OS/ABI                     | No extensions or unspecified   |
| ABI Version                | 0                              |
| Type                       | DYN (Shared object file)       |
| Machine                    | 183                            |
| Version                    | 0x1                            |
| Entry address              | 0x00002814                     |
| Program headers offset     | 0x00000040                     |
| Section headers offset     | 0x00000000                     |
| Flags                      | 0x00000000                     |
| ELF header size            | 64                             |
| Program headers size       | 56                             |
| Number of program headers  | 4                              |
| Section headers size       | 64                             |
| Number of section headers  | 0                              |
| String table section index | 0                              |

Program Headers: 
| Num |   Type   | Offset | VirtAddr | PhysAddr | FileSize | MemSize | Flags | Align |
|-----|----------|--------|----------|----------|----------|---------|-------|-------|
|  1  | LOAD     |0x06000 | 0x00000  | 0x00000  | 0x6e600  | 0x6e600 |   RE  | 0x1000|
|  2  | LOAD     |0x75000 | 0x6f000  | 0x6f000  | 0x012b8  | 0x012b8 |   RW  | 0x1000|
|  3  | DYNAMIC  |0x77000 | 0x71000  | 0x71000  | 0x00120  | 0x00120 |   RW  | 0x1000|
|  4  | LOAD     |0x77000 | 0x71000  | 0x71000  | 0x07e3e  | 0x07e3e |   RW  | 0x1000|

Hash Segment Properties: 
| Header Size     | 288B  |
| Hash Algorithm  | sha384|

Header: 
| cert_chain_ptr              | 0xffffffff  |
| cert_chain_size             | 0x00003000  |
| cert_chain_size_qti         | 0x00001800  |
| code_size                   | 0x00000120  |
| image_id                    | 0x00000004  |
| image_size                  | 0x00004c20  |
| metadata_major_version      | 0x00000000  |
| metadata_major_version_qti  | 0x00000000  |
| metadata_minor_version      | 0x00000000  |
| metadata_minor_version_qti  | 0x00000000  |
| metadata_size               | 0x00000078  |
| metadata_size_qti           | 0x00000078  |
| sig_ptr                     | 0xffffffff  |
| sig_size                    | 0x00000100  |
| sig_size_qti                | 0x00000100  |
| version                     | 0x00000006  |
Metadata:
| anti_rollback_version        | 0x00000000  |
| app_id                       | 0x00000556  |
| debug                        | 0x00000001  |
| hw_id                        | 0x00000000  |
| in_use_soc_hw_version        | 0x00000001  |
| model_id                     | 0x00000000  |
| mrc_index                    | 0x00000000  |
| multi_serial_numbers         | 0x00000000  |
| oem_id                       | 0x00000001  |
| oem_id_independent           | 0x00000001  |
| root_revoke_activate_enable  | 0x00000000  |
| rot_en                       | 0x00000000  |
| soc_vers                     | 0x00006003  |
| sw_id                        | 0x0000001f  |
| uie_key_switch_enable        | 0x00000000  |
| use_serial_number_in_signing | 0x00000000  |
Metadata QTI:
| anti_rollback_version        | 0x00000000  |
| app_id                       | 0x00000556  |
| debug                        | 0x00000001  |
| hw_id                        | 0x00000000  |
| in_use_soc_hw_version        | 0x00000001  |
| model_id                     | 0x00000000  |
| mrc_index                    | 0x00000000  |
| multi_serial_numbers         | 0x00000000  |
| oem_id                       | 0x00000001  |
| oem_id_independent           | 0x00000001  |
| root_revoke_activate_enable  | 0x00000000  |
| rot_en                       | 0x00000000  |
| soc_vers                     | 0x00006003  |
| sw_id                        | 0x0000001f  |
| uie_key_switch_enable        | 0x00000000  |
| use_serial_number_in_signing | 0x00000000  |


