<?xml version="1.0" ?>
<contents>
  <product_flavors cmm_pf_var="PRODUCT_FLAVORS">
    <pf>
      <name>8155_la</name>
      <component>
        <name>common</name>
        <flavor>8155_la</flavor>
      </component>
      <component>
        <name>tz_8155</name>
        <flavor>8155_la</flavor>
      </component>
      <component>
        <name>tz_apps</name>
        <flavor>8155_la</flavor>
      </component>
      <component>
        <name>wlan_rome</name>
        <flavor>8155_la</flavor>
      </component>
      <component>
        <name>wlan_hst</name>
        <flavor>8155_la</flavor>
      </component>
      <component>
        <name>wlan_gen</name>
        <flavor>8155_la</flavor>
      </component>
      <component>
        <name>mpss_8155</name>
        <flavor>8155_la</flavor>
      </component>
      <component>
        <name>btfm_rome</name>
        <flavor>8155_la</flavor>
      </component>
      <component>
        <name>btfm_hst</name>
        <flavor>8155_la</flavor>
      </component>
      <component>
        <name>btfm_gen</name>
        <flavor>8155_la</flavor>
      </component>
      <component>
        <name>aop_8155</name>
        <flavor>8155_la</flavor>
      </component>
      <component>
        <name>boot_8155</name>
        <flavor>8155_la</flavor>
      </component>
      <component>
        <name>apps</name>
        <flavor>8155_la</flavor>
      </component>
      <component>
        <name>lagvm</name>
        <flavor>8155_la</flavor>
      </component>
      <component>
        <name>adsp_8155</name>
        <flavor>8155_la</flavor>
      </component>
      <component>
        <name>cdsp_8155</name>
        <flavor>8155_la</flavor>
      </component>
      <component>
        <name>video</name>
        <flavor>8155_la</flavor>
      </component>
      <component>
        <name>npu</name>
        <flavor>8155_la</flavor>
      </component>
    </pf>
    <pf>
      <name>6155_la</name>
      <component>
        <name>common</name>
        <flavor>6155_la</flavor>
      </component>
      <component>
        <name>tz_6155</name>
        <flavor>6155_la</flavor>
      </component>
      <component>
        <name>tz_apps</name>
        <flavor>6155_la</flavor>
      </component>
      <component>
        <name>aop_6155</name>
        <flavor>6155_la</flavor>
      </component>
      <component>
        <name>mpss_6155</name>
        <flavor>6155_la</flavor>
      </component>
      <component>
        <name>apps</name>
        <flavor>6155_la</flavor>
      </component>
      <component>
        <name>lagvm</name>
        <flavor>6155_la</flavor>
      </component>
      <component>
        <name>btfm_rome</name>
        <flavor>6155_la</flavor>
      </component>
      <component>
        <name>btfm_hst</name>
        <flavor>6155_la</flavor>
      </component>
      <component>
        <name>btfm_gen</name>
        <flavor>6155_la</flavor>
      </component>
      <component>
        <name>boot_6155</name>
        <flavor>6155_la</flavor>
      </component>
      <component>
        <name>wlan_rome</name>
        <flavor>6155_la</flavor>
      </component>
      <component>
        <name>wlan_hst</name>
        <flavor>6155_la</flavor>
      </component>
      <component>
        <name>wlan_gen</name>
        <flavor>6155_la</flavor>
      </component>
      <component>
        <name>adsp_6155</name>
        <flavor>6155_la</flavor>
      </component>
      <component>
        <name>cdsp_6155</name>
        <flavor>6155_la</flavor>
      </component>
      <component>
        <name>video_6155</name>
        <flavor>6155_la</flavor>
      </component>
    </pf>
  </product_flavors>
  <product_info>
    <product_name>Snapdragon_Auto_Gen3.HQX.1.2.1.c1</product_name>
    <hlos_type cmm_var="HLOS_TYPE">qx</hlos_type>
    <chipid cmm_var="CHIPID" flavor="8155_la">SDM855</chipid>
    <chipid cmm_var="CHIPID" flavor="6155_la">SM6155</chipid>
    <meta_type cmm_var="META_VARIANT">SPF</meta_type>
  </product_info>
  <partition_info>
    <partition fastboot_erase="true">modemst1</partition>
    <partition fastboot_erase="true">modemst2</partition>
    <partition fastboot_erase="true">fsg</partition>
  </partition_info>
  <builds_flat>
    <build>
      <name>apps</name>
      <role>apps</role>
      <chipset>SDM855,SM6155</chipset>
      <build_id>QXA.QA.5.0.c5.3-00139-SA8155P.HYP-1</build_id>
      <windows_root_path cmm_root_path_var="APPS_BUILDROOT">.\apps\</windows_root_path>
      <linux_root_path cmm_root_path_var="APPS_BUILDROOT">./apps/</linux_root_path>
      <image_dir>qnx_ap</image_dir>
      <release_path/>
      <download_file cmm_file_var="APPSBOOT_BINARY" backup_partition="abl_b" fastboot="abl_a" minimized="true">
        <file_name>abl_fastboot.elf</file_name>
        <file_path flavor="8155_la">qnx_ap/target/hypervisor/host/abl-image/signed/default/abl/</file_path>
      </download_file>
      <download_file cmm_file_var="APPSBOOT_BINARY" backup_partition="abl_b" fastboot="abl_a" minimized="true">
        <file_name>abl_fastboot.elf</file_name>
        <file_path flavor="6155_la">qnx_ap/target/hypervisor/host/abl-image/signed/default/abl/</file_path>
      </download_file>
      <download_file cmm_file_var="APPSIFS2_BINARY" backup_partition="ifs2_b" fastboot="ifs2_a" minimized="true">
        <file_name>ifs2_la.img</file_name>
        <file_path flavor="8155_la">qnx_ap/target/hypervisor/host/out_8155/</file_path>
      </download_file>
      <download_file cmm_file_var="APPSIFS2_BINARY" backup_partition="ifs2_b" fastboot="ifs2_a" minimized="true">
        <file_name>ifs2_la.img</file_name>
        <file_path flavor="6155_la">qnx_ap/target/hypervisor/host/out_6155/</file_path>
      </download_file>
      <download_file cmm_file_var="HYP_BINARY" backup_partition="hyp_b" fastboot="hyp_a" minimized="true">
        <file_name>mifs_hyp_la.img</file_name>
        <file_path flavor="8155_la">qnx_ap/target/hypervisor/host/out_8155/signed/default/qhee/</file_path>
      </download_file>
      <download_file cmm_file_var="HYP_BINARY" backup_partition="hyp_b" fastboot="hyp_a" minimized="true">
        <file_name>mifs_hyp_la.img</file_name>
        <file_path flavor="6155_la">qnx_ap/target/hypervisor/host/out_6155/signed/default/qhee/</file_path>
      </download_file>
      <download_file minimized="true" fastboot="persist">
        <file_name>persist_qnx.img</file_name>
        <file_path flavor="8155_la">qnx_ap/target/hypervisor/host/out_8155/</file_path>
      </download_file>
      <download_file minimized="true" fastboot="persist">
        <file_name>persist_qnx.img</file_name>
        <file_path flavor="6155_la">qnx_ap/target/hypervisor/host/out_6155/</file_path>
      </download_file>
      <download_file minimized="true" fastboot="system_a">
        <file_name>system_la.img.sparse</file_name>
        <file_path flavor="8155_la">qnx_ap/target/hypervisor/host/out_8155/</file_path>
      </download_file>
      <download_file minimized="true" fastboot="system_a">
        <file_name>system_la.img.sparse</file_name>
        <file_path flavor="6155_la">qnx_ap/target/hypervisor/host/out_6155/</file_path>
      </download_file>
      <download_file minimized="true">
        <file_name>system_la.img</file_name>
        <file_path flavor="8155_la">qnx_ap/target/hypervisor/host/out_8155/</file_path>
      </download_file>
      <download_file minimized="true">
        <file_name>system_la.img</file_name>
        <file_path flavor="6155_la">qnx_ap/target/hypervisor/host/out_6155/</file_path>
      </download_file>
      <file_ref ignore="true" qnx_kernel_symfile="true" flavor="8155_la">
        <file_name>procnto-smp-instr.qvmhost_hyp_la.secure.sym</file_name>
        <file_path>/qnx_ap/target/hypervisor/host/out_8155/</file_path>
      </file_ref>
      <file_ref ignore="true" qnx_kernel_symfile="true" flavor="6155_la">
        <file_name>procnto-smp-instr.qvmhost_hyp_la.secure.sym</file_name>
        <file_path>/qnx_ap/target/hypervisor/host/out_6155/</file_path>
      </file_ref>
      <wf_step_filter>hlos</wf_step_filter>
      <buildfile_path>cd/</buildfile_path>
      <build_command>cd qnx_ap/build ; ./copy_script.sh ; cd ../ ; source setenv_64.sh ; make clean ; make</build_command>
    </build>
    <build>
      <name>lagvm</name>
      <role vm="true">lagvm</role>
      <chipset>SDM855,SM6155</chipset>
      <build_id>LA.AU.1.3.2.r4-01200-sa8155_gvmq.0-5</build_id>
      <windows_root_path cmm_root_path_var="APPSGVM_BUILDROOT">.\lagvm\</windows_root_path>
      <linux_root_path cmm_root_path_var="APPSGVM_BUILDROOT">./lagvm/</linux_root_path>
      <image_dir>LINUX</image_dir>
      <release_path/>
      <download_file cmm_file_var="APPS_BINARY" backup_partition="la_boot_b" fastboot="la_boot_a" minimized="true">
        <file_name>boot.img</file_name>
        <file_path flavor="8155_la">LINUX/android/out/target/product/msmnile_gvmq/</file_path>
        <file_path flavor="6155_la">LINUX/android/out/target/product/msmnile_gvmq/</file_path>
      </download_file>
      <download_file sparse_image_path="true" backup_partition="la_system_b" fastboot="la_system_a" minimized="true">
        <file_name>system.img</file_name>
        <file_path flavor="8155_la">LINUX/android/out/target/product/msmnile_gvmq/</file_path>
        <file_path flavor="6155_la">LINUX/android/out/target/product/msmnile_gvmq/</file_path>
      </download_file>
      <download_file sparse_image_path="true" backup_partition="la_vendor_b" fastboot="la_vendor_a" minimized="true">
        <file_name>vendor.img</file_name>
        <file_path flavor="8155_la">LINUX/android/out/target/product/msmnile_gvmq/</file_path>
        <file_path flavor="6155_la">LINUX/android/out/target/product/msmnile_gvmq/</file_path>
      </download_file>
      <download_file backup_partition="dtbo_b" fastboot="dtbo_a" minimized="true">
        <file_name>dtbo.img</file_name>
        <file_path flavor="8155_la">LINUX/android/out/target/product/msmnile_gvmq/</file_path>
        <file_path flavor="6155_la">LINUX/android/out/target/product/msmnile_gvmq/</file_path>
      </download_file>
      <download_file backup_partition="la_vbmeta_b" fastboot="la_vbmeta_a" minimized="true">
        <file_name>vbmeta.img</file_name>
        <file_path flavor="8155_la">LINUX/android/out/target/product/msmnile_gvmq/</file_path>
        <file_path flavor="6155_la">LINUX/android/out/target/product/msmnile_gvmq/</file_path>
      </download_file>
      <download_file sparse_image_path="true" minimized="true" fastboot="la_persist">
        <file_name>persist.img</file_name>
        <file_path flavor="8155_la">LINUX/android/out/target/product/msmnile_gvmq/</file_path>
        <file_path flavor="6155_la">LINUX/android/out/target/product/msmnile_gvmq/</file_path>
      </download_file>
      <download_file sparse_image_path="true" minimized="true" fastboot="la_userdata">
        <file_name>userdata.img</file_name>
        <file_path flavor="8155_la">LINUX/android/out/target/product/msmnile_gvmq/</file_path>
        <file_path flavor="6155_la">LINUX/android/out/target/product/msmnile_gvmq/</file_path>
      </download_file>
      <download_file sparse_image_path="true" minimized="true" fastboot="resources">
        <file_name>resources.img</file_name>
        <file_path flavor="8155_la">LINUX/android/out/target/product/msmnile_gvmq/</file_path>
        <file_path flavor="6155_la">LINUX/android/out/target/product/msmnile_gvmq/</file_path>
      </download_file>
      <download_file sparse_image_path="true" minimized="true" fastboot="sdcard">
        <file_name>sdcard.img</file_name>
        <file_path flavor="8155_la">LINUX/android/out/target/product/msmnile_gvmq/</file_path>
        <file_path flavor="6155_la">LINUX/android/out/target/product/msmnile_gvmq/</file_path>
      </download_file>
      <file_ref cmm_file_var="APPS_ELF" minimized="true">
        <file_name>vmlinux</file_name>
        <file_path flavor="8155_la">LINUX/android/out/target/product/msmnile_gvmq/obj/KERNEL_OBJ/</file_path>
        <file_path flavor="6155_la">LINUX/android/out/target/product/msmnile_gvmq/obj/KERNEL_OBJ/</file_path>
      </file_ref>
      <file_ref ignore="true" minimized="true">
        <file_name>adb.exe</file_name>
        <file_path flavor="8155_la">LINUX/android/vendor/qcom/proprietary/usb/host/windows/prebuilt/</file_path>
        <file_path flavor="6155_la">LINUX/android/vendor/qcom/proprietary/usb/host/windows/prebuilt/</file_path>
      </file_ref>
      <file_ref ignore="true" minimized="true">
        <file_name>AdbWinApi.dll</file_name>
        <file_path flavor="8155_la">LINUX/android/vendor/qcom/proprietary/usb/host/windows/prebuilt/</file_path>
        <file_path flavor="6155_la">LINUX/android/vendor/qcom/proprietary/usb/host/windows/prebuilt/</file_path>
      </file_ref>
      <file_ref ignore="true" minimized="true">
        <file_name>fastboot.exe</file_name>
        <file_path>LINUX/android/vendor/qcom/proprietary/usb/host/windows/prebuilt/</file_path>
      </file_ref>
      <file_ref cmm_file_var="GFX_ELF" ignore="true" minimized="true">
        <file_name>a640_zap.elf</file_name>
        <file_path flavor="8155_la">LINUX/android/out/target/product/msmnile_gvmq/vendor/firmware/</file_path>
        <file_path flavor="6155_la">LINUX/android/out/target/product/msmnile_gvmq/vendor/firmware/</file_path>
      </file_ref>
      <download_file cmm_file_var="IPA_FWS_ELF" ignore="true" minimized="true">
        <file_name>ipa_fws.elf</file_name>
        <file_path flavor="8155_la">LINUX/android/out/target/product/msmnile_gvmq/ipa/signed/</file_path>
        <file_path flavor="6155_la">LINUX/android/out/target/product/msmnile_gvmq/ipa/signed/</file_path>
      </download_file>
      <file_ref cmm_file_var="IPA_UC_ELF" ignore="true" minimized="true">
        <file_name>ipa_uc.elf</file_name>
        <file_path flavor="8155_la">LINUX/android/out/target/product/msmnile_gvmq/vendor/firmware/</file_path>
        <file_path flavor="6155_la">LINUX/android/out/target/product/msmnile_gvmq/vendor/firmware/</file_path>
      </file_ref>
      <wf_step_filter>hlos</wf_step_filter>
      <buildfile_path>./</buildfile_path>
      <build_command>create_BuildProducts ./create_BuildProducts</build_command>
    </build>
    <build>
      <name>aop_8155</name>
      <role>aop</role>
      <chipset>SDM855</chipset>
      <build_id>AOP.HO.1.1.1-00013-SDM855AUAAAAANAZO-1</build_id>
      <windows_root_path cmm_root_path_var="AOP_BUILDROOT">.\aop_8155\</windows_root_path>
      <linux_root_path cmm_root_path_var="AOP_BUILDROOT">./aop_8155/</linux_root_path>
      <image_dir>aop_proc</image_dir>
      <release_path>HY11_CompileTest</release_path>
      <download_file cmm_file_var="AOP_BINARY" minimized="true" backup_partition="aop_b" fastboot_complete="aop_a">
        <file_name>aop.mbn</file_name>
        <file_path flavor="8155_la">aop_proc/build/ms/bin/AAAAANAZO/</file_path>
      </download_file>
      <file_ref cmm_file_var="AOP_ELF" minimized="true">
        <file_name>AOP_AAAAANAZO.elf</file_name>
        <file_path flavor="8155_la">aop_proc/core/bsp/aop/build/</file_path>
      </file_ref>
      <wf_step_filter>multi_image</wf_step_filter>
      <buildfile_path>aop_proc/build/</buildfile_path>
      <build_command>build_packed.sh 855au</build_command>
    </build>
    <build>
      <name>boot_8155</name>
      <role>boot</role>
      <chipset>SDM855</chipset>
      <build_id>BOOT.XF.3.0-00657-SM8150AUZB-1</build_id>
      <windows_root_path cmm_root_path_var="BOOT_BUILDROOT">.\boot_8155\</windows_root_path>
      <linux_root_path cmm_root_path_var="BOOT_BUILDROOT">./boot_8155/</linux_root_path>
      <image_dir>boot_images</image_dir>
      <release_path>HY11_CompileTest</release_path>
      <download_file cmm_file_var="BOOT_BINARY" minimized="true" backup_partition="xbl_b" fastboot_complete="xbl_a">
        <file_name>xbl.elf</file_name>
        <file_path flavor="8155_la">boot_images/QcomPkg/SDMPkg/855/Bin/AU/RELEASE/</file_path>
      </download_file>
      <download_file cmm_file_var="BOOT_CONFIG" minimized="true" backup_partition="xbl_config_b" fastboot_complete="xbl_config_a">
        <file_name>xbl_config.elf</file_name>
        <file_path flavor="8155_la">boot_images/QcomPkg/SDMPkg/855/Bin/AU/RELEASE/</file_path>
      </download_file>
      <download_file fastboot_complete="logfs" minimized="true">
        <file_name>logfs_ufs_8mb.bin</file_name>
        <file_path flavor="8155_la">boot_images/QcomPkg/Tools/binaries/</file_path>
      </download_file>
      <device_programmer cmm_file_var="FIREHOSE_DDR_ELF" minimized="true">
        <file_name>prog_firehose_ddr.elf</file_name>
        <file_path flavor="8155_la">boot_images/QcomPkg/SDMPkg/855/Bin/AU/RELEASE</file_path>
      </device_programmer>
      <device_programmer cmm_file_var="FIREHOSE_LITE_ELF" firehose_type="lite" minimized="true">
        <file_name>prog_firehose_lite.elf</file_name>
        <file_path flavor="8155_la">boot_images/QcomPkg/SDMPkg/855/Bin/AU/RELEASE</file_path>
      </device_programmer>
      <file_ref minimized="true">
        <file_name>*.*</file_name>
        <file_path flavor="8155_la">boot_images/QcomPkg/Tools/storage/fh_loader/</file_path>
      </file_ref>
      <file_ref storage_type="ufs" minimized="true">
        <file_name>JtagProgrammer.cmm</file_name>
        <file_path flavor="8155_la">boot_images/QcomPkg/SDMPkg/Tools/storage/UFS/</file_path>
      </file_ref>
      <file_ref storage_type="ufs" minimized="true">
        <file_name>JtagProgrammer.elf</file_name>
        <file_path flavor="8155_la">boot_images/QcomPkg/SDMPkg/855/Bin/AU/RELEASE/</file_path>
      </file_ref>
      <file_ref cmm_file_var="BOOT_ELF" ignore="true" minimized="true">
        <file_name>XBLLoader.dll</file_name>
        <file_path flavor="8155_la">boot_images/Build/SDM855_Loader/RELEASE_CLANG100LINUX/AARCH64/QcomPkg/XBLLoader/XBLLoader/RELEASE/</file_path>
      </file_ref>
      <wf_step_filter>multi_image</wf_step_filter>
      <buildfile_path>python/</buildfile_path>
      <build_command>python -u boot_images/QcomPkg/buildex.py --variant AU -t SDM855Pkg,QcomToolsPkg,QcomTestPkg,QcomSocPlatTestPkg,QcomCatePkg,QnadPkg &amp;&amp; rm -f boot_images/Conf/* &amp;&amp; cd boot_images/BaseTools &amp;&amp; rm -f ./workspace.txt &amp;&amp; make clean &amp;&amp; cd ../.. &amp;&amp; python boot_images/packit.py -t SDM855,QcomTools --variant AUZB &amp;&amp; mkdir HY11_CompileTest &amp;&amp; cp -r ./HY11_1/* ./HY11_CompileTest/ &amp;&amp; echo &quot;./HY11_CompileTest/boot_images/QcomPkg/SDMPkg/855/Bin/AU/DEBUG/xbl.elf&quot; &gt;&gt; BuildProducts.txt &amp;&amp; echo &quot;./HY11_CompileTest/boot_images/QcomPkg/SDMPkg/855/Bin/AU/DEBUG/xbl_config.elf&quot; &gt;&gt; BuildProducts.txt &amp;&amp; echo &quot;./HY11_CompileTest/boot_images/QcomPkg/SDMPkg/855/Bin/AU/RELEASE/xbl.elf&quot; &gt;&gt; BuildProducts.txt &amp;&amp; echo &quot;./HY11_CompileTest/boot_images/QcomPkg/SDMPkg/855/Bin/AU/RELEASE/xbl_config.elf&quot; &gt;&gt; BuildProducts.txt &amp;&amp; python -u HY11_CompileTest/boot_images/QcomPkg/buildex.py --variant AU -t SDM855Pkg,QcomToolsPkg,QcomTestPkg</build_command>
    </build>
    <build>
      <name>tz_8155</name>
      <role>tz</role>
      <chipset>SDM855</chipset>
      <build_id>TZ.XF.5.5-00366-SM8150AAAAANAZT-1</build_id>
      <short_build_path cmm_var="TZ_BUILDID" flavor="8155_la">YAQAANAA</short_build_path>
      <windows_root_path cmm_root_path_var="TZ_BUILDROOT">.\tz_8155\</windows_root_path>
      <linux_root_path cmm_root_path_var="TZ_BUILDROOT">./tz_8155/</linux_root_path>
      <image_dir>trustzone_images</image_dir>
      <release_path>HY11_1</release_path>
      <download_file cmm_file_var="TZ_BINARY" minimized="true" backup_partition="tz_b" fastboot_complete="tz_a">
        <file_name>tz.mbn</file_name>
        <file_path flavor="8155_la">trustzone_images/build/ms/bin/YAQAANAA/</file_path>
      </download_file>
      <download_file cmm_file_var="TZDEVCFG_BINARY" minimized="true" backup_partition="devcfg_b" fastboot_complete="devcfg_a">
        <file_name>devcfg_auto.mbn</file_name>
        <file_path flavor="8155_la">trustzone_images/build/ms/bin/YAQAANAA/</file_path>
      </download_file>
      <download_file cmm_file_var="STORESEC_BINARY" ignore="true" fastboot_complete="true" minimized="true">
        <file_name>storsec.mbn</file_name>
        <file_path flavor="8155_la">trustzone_images/build/ms/bin/YAQAANAA/</file_path>
      </download_file>
      <download_file cmm_file_var="KEYMASTER_BINARY" minimized="true" pil_split="km4virt" backup_partition="keymaster_b" fastboot_complete="keymaster_a">
        <file_name>km4virt.mbn</file_name>
        <file_path flavor="8155_la">trustzone_images/build/ms/bin/YAQAANAA/</file_path>
      </download_file>
      <download_file cmm_file_var="CMNLIB64_BINARY" minimized="true" pil_split="cmnlib64" backup_partition="cmnlib64_b" fastboot_complete="cmnlib64_a">
        <file_name>cmnlib64.mbn</file_name>
        <file_path flavor="8155_la">trustzone_images/build/ms/bin/YAQAANAA/</file_path>
      </download_file>
      <download_file cmm_file_var="CMNLIB_BINARY" minimized="true" pil_split="cmnlib" backup_partition="cmnlib_b" fastboot_complete="cmnlib_a">
        <file_name>cmnlib.mbn</file_name>
        <file_path flavor="8155_la">trustzone_images/build/ms/bin/YAQAANAA/</file_path>
      </download_file>
      <download_file cmm_file_var="UEFISEC_BINARY" minimized="true" backup_partition="uefisecapp_b" fastboot_complete="uefisecapp_a">
        <file_name>uefi_sec.mbn</file_name>
        <file_path flavor="8155_la">trustzone_images/build/ms/bin/YAQAANAA/</file_path>
      </download_file>
      <file_ref ignore="true" pil_split="smplap32">
        <file_name>smplap32.mbn</file_name>
        <file_path flavor="8155_la">trustzone_images/build/ms/bin/YAQAANAA/</file_path>
      </file_ref>
      <file_ref ignore="true" pil_split="smplap64">
        <file_name>smplap64.mbn</file_name>
        <file_path flavor="8155_la">trustzone_images/build/ms/bin/YAQAANAA/</file_path>
      </file_ref>
      <file_ref ignore="true" pil_split="pr_3_0">
        <file_name>pr_3_0.mbn</file_name>
        <file_path flavor="8155_la">trustzone_images/build/ms/bin/YAQAANAA/</file_path>
      </file_ref>
      <file_ref ignore="true" pil_split="gptest">
        <file_name>gptest.mbn</file_name>
        <file_path flavor="8155_la">trustzone_images/build/ms/bin/YAQAANAA/</file_path>
      </file_ref>
      <file_ref ignore="true" pil_split="hdcp1" minimized="true">
        <file_name>hdcp1.mbn</file_name>
        <file_path flavor="8155_la">trustzone_images/build/ms/bin/YAQAANAA/</file_path>
      </file_ref>
      <file_ref ignore="true" pil_split="hdcp2p2" minimized="true">
        <file_name>hdcp2p2.mbn</file_name>
        <file_path flavor="8155_la">trustzone_images/build/ms/bin/YAQAANAA/</file_path>
      </file_ref>
      <file_ref ignore="true" pil_split="hdcpsrm" minimized="true">
        <file_name>hdcpsrm.mbn</file_name>
        <file_path flavor="8155_la">trustzone_images/build/ms/bin/YAQAANAA/</file_path>
      </file_ref>
      <wf_step_filter>non_hlos,multiimage</wf_step_filter>
      <buildfile_path>/</buildfile_path>
      <build_command> </build_command>
    </build>
    <build>
      <name>tz_apps</name>
      <role>tz_apps</role>
      <chipset>SDM855,SM6155</chipset>
      <build_id>TZ.APPS.2.0-00218-SA8195AAAAANAZT-1</build_id>
      <short_build_path cmm_var="TZ_APPS_BUILDID" flavor="8155_la">YAQAANAA</short_build_path>
      <short_build_path cmm_var="TZ_APPS_BUILDID" flavor="6155_la">PAZAANAA</short_build_path>
      <windows_root_path cmm_root_path_var="TZ_APPS_BUILDROOT">.\tz_apps\</windows_root_path>
      <linux_root_path cmm_root_path_var="TZ_APPS_BUILDROOT">./tz_apps/</linux_root_path>
      <image_dir>qtee_tas</image_dir>
      <release_path>HY11_1</release_path>
      <file_ref pil_split="widevine">
        <file_name>widevine.mbn</file_name>
        <file_path flavor="8155_la">qtee_tas/build/ms/bin/YAQAANAA/</file_path>
        <file_path flavor="6155_la">qtee_tas/build/ms/bin/PAZAANAA/</file_path>
      </file_ref>
      <file_ref ignore="true" pil_split="wvqcomkeyprov">
        <file_name>wvqcomkeyprov.mbn</file_name>
        <file_path flavor="8155_la">qtee_tas/build/ms/bin/YAQAANAA/</file_path>
        <file_path flavor="6155_la">qtee_tas/build/ms/bin/PAZAANAA/</file_path>
      </file_ref>
      <buildfile_path>/</buildfile_path>
      <build_command> </build_command>
    </build>
    <build>
      <name>mpss_8155</name>
      <role>modem</role>
      <chipset>SDM855</chipset>
      <build_id>MPSS.HE.1.0.c1.5-00178-MDF_ALL_PACK-1</build_id>
      <short_build_path cmm_var="MODEM_BUILDID">sm8150.gennmgw.prod</short_build_path>
      <windows_root_path cmm_root_path_var="MPSS_BUILDROOT">.\mpss_8155\</windows_root_path>
      <linux_root_path cmm_root_path_var="MPSS_BUILDROOT">./mpss_8155/</linux_root_path>
      <image_dir>modem_proc</image_dir>
      <release_path>BIN</release_path>
      <download_file cmm_file_var="MPSS_BINARY" pil_split="modem" minimized="true">
        <file_name>qdsp6sw.mbn</file_name>
        <file_path flavor="8155_la">modem_proc/build/ms/bin/sm8150.gennmgw.prod/</file_path>
      </download_file>
      <download_file cmm_file_var="MPSS_QDB_BINARY" minimized="true" fat_file_8155="true">
        <file_name>qdsp6m.qdb</file_name>
        <file_path flavor="8155_la">modem_proc/build/ms/bin/sm8150.gennmgw.prod/</file_path>
      </download_file>
      <file_ref minimized="true">
        <file_name>efs1.bin</file_name>
        <file_path flavor="8155_la">modem_proc/build/ms/bin/sm8150.gennmgw.prod/</file_path>
      </file_ref>
      <file_ref minimized="true">
        <file_name>efs2.bin</file_name>
        <file_path flavor="8155_la">modem_proc/build/ms/bin/sm8150.gennmgw.prod/</file_path>
      </file_ref>
      <file_ref minimized="true">
        <file_name>efs3.bin</file_name>
        <file_path flavor="8155_la">modem_proc/build/ms/bin/sm8150.gennmgw.prod/</file_path>
      </file_ref>
      <file_ref fat_file_8155="true">
        <file_name>modemr.jsn</file_name>
        <file_path flavor="8155_la">modem_proc/build/ms/servreg/sm8150.gennmgw.prodQ/</file_path>
      </file_ref>
      <wf_step_filter>non_hlos</wf_step_filter>
      <buildfile_path>/</buildfile_path>
      <build_command> </build_command>
    </build>
    <build>
      <name>wlan_rome</name>
      <role>cnss</role>
      <chipset>SDM855,SM6155</chipset>
      <build_id>WLAN.RM.4.5.3-00182-QCARMSWRZ-1</build_id>
      <windows_root_path cmm_root_path_var="WLAN_BUILDROOT">.\wlan_rome\</windows_root_path>
      <linux_root_path cmm_root_path_var="WLAN_BUILDROOT">./wlan_rome/</linux_root_path>
      <image_dir>cnss_proc</image_dir>
      <release_path>HK11</release_path>
      <file_ref fat_file_rome_8155="true" fat_file_6155="true" fat_file_rome_6155="true" fat_file_8155="true">
        <file_name>bdwlan30.bin</file_name>
        <file_path>cnss_proc/wlan/fw/target/halphy/ftm/host/systemtools/tools/eepromUtil/qc6174/</file_path>
      </file_ref>
      <file_ref fat_file_rome_8155="true" fat_file_6155="true" fat_file_rome_6155="true" fat_file_8155="true">
        <file_name>bdwlan30.b01</file_name>
        <file_path>cnss_proc/wlan/fw/target/halphy/ftm/host/systemtools/tools/eepromUtil/qc6174/</file_path>
      </file_ref>
      <file_ref fat_file_rome_8155="true" fat_file_6155="true" fat_file_rome_6155="true" fat_file_8155="true">
        <file_name>bdwlan30.b03</file_name>
        <file_path>cnss_proc/wlan/fw/target/halphy/ftm/host/systemtools/tools/eepromUtil/qc6174/</file_path>
      </file_ref>
      <file_ref fat_file_rome_8155="true" fat_file_6155="true" fat_file_rome_6155="true" fat_file_8155="true">
        <file_name>bdwlan30.b06</file_name>
        <file_path>cnss_proc/wlan/fw/target/halphy/ftm/host/systemtools/tools/eepromUtil/qc6174/</file_path>
      </file_ref>
      <file_ref fat_file_rome_8155="true" fat_file_6155="true" fat_file_rome_6155="true" fat_file_8155="true">
        <file_name>bdwlan30.b21</file_name>
        <file_path>cnss_proc/wlan/fw/target/halphy/ftm/host/systemtools/tools/eepromUtil/qc6174/</file_path>
      </file_ref>
      <file_ref fat_file_rome_8155="true" fat_file_6155="true" fat_file_rome_6155="true" fat_file_8155="true">
        <file_name>bdwlan30.b25</file_name>
        <file_path>cnss_proc/wlan/fw/target/halphy/ftm/host/systemtools/tools/eepromUtil/qc6174/</file_path>
      </file_ref>
      <file_ref fat_file_rome_8155="true" fat_file_6155="true" fat_file_rome_6155="true" fat_file_8155="true">
        <file_name>bdwlan30.b31</file_name>
        <file_path>cnss_proc/wlan/fw/target/halphy/ftm/host/systemtools/tools/eepromUtil/qc6174/</file_path>
      </file_ref>
      <file_ref fat_file_rome_8155="true" fat_file_6155="true" fat_file_rome_6155="true" fat_file_8155="true">
        <file_name>qwlan30.bin</file_name>
        <file_path>cnss_proc/wlan/fw/target/pcie_dst/AR6320/hw.3/bin/</file_path>
      </file_ref>
      <file_ref fat_file_rome_8155="true" fat_file_6155="true" fat_file_rome_6155="true" fat_file_8155="true">
        <file_name>otp30.bin</file_name>
        <file_path>cnss_proc/wlan/fw/target/pcie_dst/AR6320/hw.3/bin/</file_path>
      </file_ref>
      <file_ref fat_file_rome_8155="true" fat_file_6155="true" fat_file_rome_6155="true" fat_file_8155="true">
        <file_name>utf30.bin</file_name>
        <file_path>cnss_proc/wlan/fw/target/pcie_dst/AR6320/hw.3/bin/</file_path>
      </file_ref>
      <file_ref fat_file_rome_8155="true" fat_file_6155="true" fat_file_rome_6155="true" fat_file_8155="true">
        <file_name>Data.msc</file_name>
        <file_path>cnss_proc/wlan/fw/target/.output/AR6320/hw.3/bin/</file_path>
      </file_ref>
      <wf_step_filter>non_hlos</wf_step_filter>
      <buildfile_path>cd/</buildfile_path>
      <build_command>cd ./cnss_proc/build; source ./rome_crm_build_hl_combined.sh BUILD_VER=00182 ./wlan/fw/target/halphy/ftm/host/systemtools/tools/eepromUtil/qc6174/bdwlan30.bin;cp ./wlan/fw/target/halphy/ftm/host/systemtools/tools/eepromUtil/qc6174/eeprom_ar6320_3p0_fccsp_DSRC_SDIO_OLPC_YB205.bin ./wlan/fw/target/halphy/ftm/host/systemtools/tools/eepromUtil/qc6174/bdwlan30.bin</build_command>
    </build>
    <build>
      <name>wlan_hst</name>
      <role>wlan_hst</role>
      <chipset>SDM855,SM6155</chipset>
      <build_id>WLAN.HST.1.0.2-03261-QCAHSTSWPL_SILICONZ-1</build_id>
      <windows_root_path cmm_root_path_var="WLAN_BUILDROOT">.\wlan_hst\</windows_root_path>
      <linux_root_path cmm_root_path_var="WLAN_BUILDROOT">./wlan_hst/</linux_root_path>
      <image_dir>wlan_proc</image_dir>
      <release_path>BIN</release_path>
      <file_ref fat_file_hst_6155="true" fat_file_hst_8155="true" fat_file_6155="true" fat_file_8155="true">
        <file_name>amss.bin</file_name>
        <file_path>wlan_proc/config/bsp/cnss_ram_v1_TO_link_patched/build/6390.wlanfw.eval_v1_TO/</file_path>
      </file_ref>
      <file_ref fat_file_hst_6155="true" fat_file_hst_8155="true" fat_file_6155="true" fat_file_8155="true">
        <file_name>amss20.bin</file_name>
        <file_path>wlan_proc/config/bsp/cnss_ram_v2_TO_link_patched/build/6390.wlanfw.eval_v2_TO/</file_path>
      </file_ref>
      <file_ref fat_file_hst_6155="true" fat_file_hst_8155="true">
        <file_name>m3.bin</file_name>
        <file_path>wlan_proc/wlan/subsys/phyucode_binary/image_hastings/</file_path>
      </file_ref>
      <file_ref fat_file_hst_6155="true" fat_file_hst_8155="true" fat_file_6155="true" fat_file_8155="true">
        <file_name>bdwlan02.e01</file_name>
        <file_path>wlan_proc/wlan/phyrf_svc/tools/bdfUtil/device/bdf/qca639x/</file_path>
      </file_ref>
      <file_ref fat_file_hst_6155="true" fat_file_hst_8155="true" fat_file_6155="true" fat_file_8155="true">
        <file_name>bdwlan02.e02</file_name>
        <file_path>wlan_proc/wlan/phyrf_svc/tools/bdfUtil/device/bdf/qca639x/</file_path>
      </file_ref>
      <file_ref fat_file_hst_6155="true" fat_file_hst_8155="true" fat_file_6155="true" fat_file_8155="true">
        <file_name>bdwlan02.e03</file_name>
        <file_path>wlan_proc/wlan/phyrf_svc/tools/bdfUtil/device/bdf/qca639x/</file_path>
      </file_ref>
      <file_ref fat_file_hst_6155="true" fat_file_hst_8155="true">
        <file_name>bdwlan.elf</file_name>
        <file_path>wlan_proc/wlan/phyrf_svc/tools/bdfUtil/device/bdf/qca639x/</file_path>
      </file_ref>
      <wf_step_filter>non_hlos</wf_step_filter>
      <buildfile_path>/</buildfile_path>
      <build_command> </build_command>
    </build>
    <build>
      <name>wlan_gen</name>
      <role>wlan_gen</role>
      <chipset>SDM855,SM6155</chipset>
      <build_id>WLAN.GNO.2.2-00369-7605GN04AUZ-1</build_id>
      <windows_root_path cmm_root_path_var="WLAN_BUILDROOT">.\wlan_gen\</windows_root_path>
      <linux_root_path cmm_root_path_var="WLAN_BUILDROOT">./wlan_gen/</linux_root_path>
      <image_dir>wlan_proc</image_dir>
      <release_path>BIN</release_path>
      <file_ref fat_file_gen_6155="true" fat_file_6155="true" fat_file_gen_8155="true" fat_file_8155="true">
        <file_name>amss.bin</file_name>
        <file_path>wlan_proc/build/ms/bin/7605.wlanfw.eval_v2_TO_ll/</file_path>
      </file_ref>
      <file_ref fat_file_gen_6155="true" fat_file_6155="true" fat_file_gen_8155="true" fat_file_8155="true">
        <file_name>genoaftm.bin</file_name>
        <file_path>wlan_proc/build/ms/bin/7605.wlanfw.eval_v2_TO_ll_ftm/</file_path>
      </file_ref>
      <file_ref fat_file_gen_6155="true" fat_file_6155="true" fat_file_gen_8155="true" fat_file_8155="true">
        <file_name>bdwlan02.b03</file_name>
        <file_path>wlan_proc/wlan/halphy_tools/host/bdfUtil/Genoa/bdf/</file_path>
      </file_ref>
      <file_ref fat_file_gen_6155="true" fat_file_6155="true" fat_file_gen_8155="true" fat_file_8155="true">
        <file_name>bdwlan03.b03</file_name>
        <file_path>wlan_proc/wlan/halphy_tools/host/bdfUtil/Genoa/bdf/</file_path>
      </file_ref>
      <file_ref fat_file_gen_6155="true" fat_file_6155="true" fat_file_gen_8155="true" fat_file_8155="true">
        <file_name>bdwlan03.b04</file_name>
        <file_path>wlan_proc/wlan/halphy_tools/host/bdfUtil/Genoa/bdf/</file_path>
      </file_ref>
      <file_ref fat_file_gen_6155="true" fat_file_6155="true" fat_file_gen_8155="true" fat_file_8155="true">
        <file_name>bdwlan03.b05</file_name>
        <file_path>wlan_proc/wlan/halphy_tools/host/bdfUtil/Genoa/bdf/</file_path>
      </file_ref>
      <file_ref fat_file_gen_6155="true" fat_file_6155="true" fat_file_gen_8155="true" fat_file_8155="true">
        <file_name>bdwlan03.b06</file_name>
        <file_path>wlan_proc/wlan/halphy_tools/host/bdfUtil/Genoa/bdf/</file_path>
      </file_ref>
      <file_ref fat_file_gen_6155="true" fat_file_6155="true" fat_file_gen_8155="true" fat_file_8155="true">
        <file_name>bdwlan04.b01</file_name>
        <file_path>wlan_proc/wlan/halphy_tools/host/bdfUtil/Genoa/bdf/</file_path>
      </file_ref>
      <wf_step_filter>non_hlos</wf_step_filter>
      <buildfile_path>cd/</buildfile_path>
      <build_command>cd ./wlan_proc/build/ms ; sh ./gno_build.sh 7605.wlanfw.eval_v2_TO_ll --buildspec=klocwork BUILD_VER=00369 static_mem_stats=y platform=silicon wlanfw_rom=y image_type=ll fw_log_mode=wmi -c ; sh ./gno_build.sh 7605.wlanfw.eval_v2_TO_ll_epping --buildspec=klocwork BUILD_VER=00369 static_mem_stats=y platform=silicon wlanfw_rom=y image_type=ll COMP_PATCH_ROOT=rom/gno_v2/patch/ build_img=epping -c ; sh ./build.sh 7605.wlanfw.eval_v2_TO_ll_ftm --buildspec=klocwork BUILD_VER=00369 static_mem_stats=y platform=silicon wlanfw_rom=y image_type=ll -c ; sh ./gno_build.sh 7605.wlanfw.eval_v2_TO_ll --buildspec=klocwork BUILD_VER=00369 static_mem_stats=y platform=silicon wlanfw_rom=y image_type=ll COMP_PATCH_ROOT=rom/gno_v2/patch/ fw_log_mode=wmi ; sh ./gno_build.sh 7605.wlanfw.eval_v2_TO_ll_epping --buildspec=klocwork BUILD_VER=00369 static_mem_stats=y platform=silicon wlanfw_rom=y image_type=ll COMP_PATCH_ROOT=rom/gno_v2/patch/ build_img=epping ; sh ./gno_build.sh 7605.wlanfw.eval_v2_TO_ll_ftm --buildspec=klocwork BUILD_VER=00369 static_mem_stats=y platform=silicon wlanfw_rom=y image_type=ll COMP_PATCH_ROOT=rom/gno_v2/patch/ ; cp bin/7605.wlanfw.eval_v2_TO_ll_ftm/SBL_RDDM_RAM_MERGED_7605.wlanfw.eval_v2_TO_ll_ftm.mbn bin/7605.wlanfw.eval_v2_TO_ll_ftm/genoaftm.bin;/pkg/qct/software/hexagon/earlyaccess/volume2/8.4.alpha1/Tools/bin/hexagon-llvm-objcopy ../../../wlan_proc/config/bsp/cnss_ram_v2_TO_link_patched/build/7605.wlanfw.eval_v2_TO_ll/CNSS_RAM_V2_TO_LINK_PATCHED_7605.wlanfw.eval_v2_TO_llQ_link.elf --only-section .region_high_clade -O binary bin/7605.wlanfw.eval_v2_TO_ll/clade1.bin;/pkg/qct/software/hexagon/earlyaccess/volume2/8.4.alpha1/Tools/bin/hexagon-llvm-objcopy ../../../wlan_proc/config/bsp/cnss_ram_v2_TO_link_patched/build/7605.wlanfw.eval_v2_TO_ll/CNSS_RAM_V2_TO_LINK_PATCHED_7605.wlanfw.eval_v2_TO_llQ_link.elf --only-section .region_low_clade -O binary bin/7605.wlanfw.eval_v2_TO_ll/clade2.bin;rm ../../wlan/halphy_tools/host/bdfUtil/Genoa/bdf/bdwlan.bin;cp ../../wlan/halphy_tools/host/bdfUtil/Genoa/bdf/bdwlan03.b05 ../../wlan/halphy_tools/host/bdfUtil/Genoa/bdf/bdwlan.bin;cp ../../config/bsp/cnss_ram_v2_TO_link_patched/build/7605.wlanfw.eval_v2_TO_ll/Data.msc ../../build/ms/bin/7605.wlanfw.eval_v2_TO_ll/;</build_command>
    </build>
    <build>
      <name>btfm_rome</name>
      <role>btfm</role>
      <chipset>SDM855,SM6155</chipset>
      <build_id>BTFM.RM.2.4.1-00046-QCABTFMSWPZ-2</build_id>
      <windows_root_path cmm_root_path_var="BTFM_BUILDROOT">.\btfm_rome\</windows_root_path>
      <linux_root_path cmm_root_path_var="BTFM_BUILDROOT">./btfm_rome/</linux_root_path>
      <image_dir>btfm_proc</image_dir>
      <release_path>HK11</release_path>
      <file_ref fat_file_btfm_6155="true" fat_file_btfm_8155="true">
        <file_name>btnv32.bin</file_name>
        <file_path>btfm_proc/bt/build/ms/bin/QCA6574/</file_path>
      </file_ref>
      <file_ref fat_file_btfm_6155="true" fat_file_btfm_8155="true">
        <file_name>btfw32.tlv</file_name>
        <file_path>btfm_proc/bt/build/ms/bin/QCA6574/</file_path>
      </file_ref>
      <wf_step_filter>btfm</wf_step_filter>
      <buildfile_path>cd/</buildfile_path>
      <build_command>cd ./btfm_proc/build; source ./build_cmds_bt_34.sh BUILD_VER=00046</build_command>
    </build>
    <build>
      <name>btfm_hst</name>
      <role>btfm</role>
      <chipset>SDM855,SM6155</chipset>
      <build_id>BTFM.HST.2.0.1-00062-QCACHROMZ-1</build_id>
      <windows_root_path cmm_root_path_var="BTFM_BUILDROOT">.\btfm_hst\</windows_root_path>
      <linux_root_path cmm_root_path_var="BTFM_BUILDROOT">./btfm_hst/</linux_root_path>
      <image_dir>btfm_proc</image_dir>
      <release_path>HK11</release_path>
      <file_ref fat_file_btfm_6155="true" fat_file_btfm_8155="true">
        <file_name>htnv10.bin</file_name>
        <file_path>btfm_proc/bt/build/ms/bin/QCA6690/auto/</file_path>
      </file_ref>
      <file_ref fat_file_btfm_6155="true" fat_file_btfm_8155="true">
        <file_name>htbtfw10.tlv</file_name>
        <file_path>btfm_proc/bt/build/ms/bin/QCA6690/auto/</file_path>
      </file_ref>
      <file_ref fat_file_btfm_6155="true" fat_file_btfm_8155="true">
        <file_name>htnv20.bin</file_name>
        <file_path>btfm_proc/bt/build/ms/bin/QCA6690/auto/</file_path>
      </file_ref>
      <file_ref fat_file_btfm_6155="true" fat_file_btfm_8155="true">
        <file_name>htnv20.203</file_name>
        <file_path>btfm_proc/bt/build/ms/bin/QCA6690/auto/</file_path>
      </file_ref>
      <file_ref fat_file_btfm_6155="true" fat_file_btfm_8155="true">
        <file_name>htbtfw20.tlv</file_name>
        <file_path>btfm_proc/bt/build/ms/bin/QCA6690/auto/</file_path>
      </file_ref>
      <wf_step_filter>btfm</wf_step_filter>
      <buildfile_path>cd/</buildfile_path>
      <build_command>cd ./btfm_proc/build; source ./crm_build_btfm.sh --target 6690 --chipset 6690 --kw --patch --uniqueid</build_command>
    </build>
    <build>
      <name>btfm_gen</name>
      <role>btfm</role>
      <chipset>SDM855,SM6155</chipset>
      <build_id>BTFM.GEN.2.0.0-00374-QCACHROMZ-2</build_id>
      <windows_root_path cmm_root_path_var="BTFM_BUILDROOT">.\btfm_gen\</windows_root_path>
      <linux_root_path cmm_root_path_var="BTFM_BUILDROOT">./btfm_gen/</linux_root_path>
      <image_dir>btfm_proc</image_dir>
      <release_path>HK11</release_path>
      <file_ref fat_file_btfm_6155="true" fat_file_btfm_8155="true">
        <file_name>gnbtfw20.tlv</file_name>
        <file_path>btfm_proc/bt/build/ms/bin/QCA6595/SCAQBAFM/</file_path>
      </file_ref>
      <file_ref fat_file_btfm_6155="true" fat_file_btfm_8155="true">
        <file_name>gnnv20.bin</file_name>
        <file_path>btfm_proc/bt/build/ms/bin/QCA6595/SCAQBAFM/</file_path>
      </file_ref>
      <wf_step_filter>btfm</wf_step_filter>
      <buildfile_path>cd/</buildfile_path>
      <build_command>cd ./btfm_proc/build; source ./crm_build_bt_gen_patch_200.sh 7605 BUILD_VER=00374</build_command>
    </build>
    <build>
      <name>adsp_8155</name>
      <role>adsp</role>
      <chipset>SDM855</chipset>
      <build_id>ADSP.HT.5.0.1.c1-00149-SM8150-1</build_id>
      <windows_root_path cmm_root_path_var="ADSP_BUILDROOT">.\adsp_8155\</windows_root_path>
      <linux_root_path cmm_root_path_var="ADSP_BUILDROOT">./adsp_8155/</linux_root_path>
      <image_dir>adsp_proc</image_dir>
      <release_path>HY11_CompileTest</release_path>
      <download_file cmm_file_var="ADSP_BINARY" pil_split="adsp" minimized="true">
        <file_name>adsp.mbn</file_name>
        <file_path flavor="8155_la">adsp_proc/obj/qdsp6v5_ReleaseG/855.adsp.prod/</file_path>
      </download_file>
      <file_ref minimized="true" fat_file_8155="true">
        <file_name>adspr.jsn</file_name>
        <file_path flavor="8155_la">adsp_proc/build/ms/servreg/855.adsp.prodQ/</file_path>
      </file_ref>
      <file_ref minimized="true" fat_file_8155="true">
        <file_name>adspua.jsn</file_name>
        <file_path flavor="8155_la">adsp_proc/build/ms/servreg/855.adsp.prodQ/</file_path>
      </file_ref>
      <file_ref adspso_signed="true">
        <file_name>*</file_name>
        <file_path flavor="8155_la">adsp_proc/build/dynamic_modules/855.adsp.prod/</file_path>
      </file_ref>
      <wf_step_filter>non_hlos,dspso,multi_image</wf_step_filter>
      <buildfile_path>python/</buildfile_path>
      <build_command>python ./adsp_proc/build/build.py -c SM8150 -o all -f ADSP; python ./adsp_proc/build/build.py -c SM8150 -o all -f ADSP,USES_BUILD_CASA | tee LOG_build.txt</build_command>
    </build>
    <build>
      <name>cdsp_8155</name>
      <role>cdsp</role>
      <chipset>SDM855</chipset>
      <build_id>CDSP.HT.2.0.1.c2-00062-SM8150-1</build_id>
      <windows_root_path cmm_root_path_var="CDSP_BUILDROOT">.\cdsp_8155\</windows_root_path>
      <linux_root_path cmm_root_path_var="CDSP_BUILDROOT">./cdsp_8155/</linux_root_path>
      <image_dir>cdsp_proc</image_dir>
      <release_path>HY11_CompileTest</release_path>
      <file_ref minimized="true" fat_file_8155="true">
        <file_name>cdspr.jsn</file_name>
        <file_path flavor="8155_la">cdsp_proc/build/ms/servreg/855.cdsp.prodQ/</file_path>
      </file_ref>
      <download_file cmm_file_var="CDSP_BINARY" pil_split="cdsp" minimized="true">
        <file_name>cdsp.mbn</file_name>
        <file_path flavor="8155_la">cdsp_proc/obj/qdsp6v5_ReleaseG/855.cdsp.prod/</file_path>
      </download_file>
      <file_ref cdspso_signed="true">
        <file_name>*</file_name>
        <file_path flavor="8155_la">cdsp_proc/build/dynamic_modules/855.cdsp.prod/</file_path>
      </file_ref>
      <wf_step_filter>non_hlos,dspso,multi_image</wf_step_filter>
      <buildfile_path>python/</buildfile_path>
      <build_command>python ./cdsp_proc/build/build.py -c SM8150 -o all -f CDSP</build_command>
    </build>
    <build>
      <name>video</name>
      <role>video</role>
      <chipset>SDM855</chipset>
      <build_id>VIDEO.IR.1.2-00059-PROD-2</build_id>
      <windows_root_path cmm_root_path_var="VENUS_BUILDROOT">.\video\</windows_root_path>
      <linux_root_path cmm_root_path_var="VENUS_BUILDROOT">./video/</linux_root_path>
      <image_dir>venus_proc</image_dir>
      <release_path>HK11</release_path>
      <download_file cmm_file_var="VENUS_BINARY" pil_split="venus" minimized="true">
        <file_name>venus.mbn</file_name>
        <file_path flavor="8155_la">venus_proc/build/bsp/asic/build/PROD/mbn/reloc/signed/</file_path>
      </download_file>
      <wf_step_filter>non_hlos,multi_image</wf_step_filter>
      <buildfile_path>build.cmd/</buildfile_path>
      <build_command>build.cmd build.cmd</build_command>
    </build>
    <build>
      <name>npu</name>
      <role>npu</role>
      <chipset>SDM855</chipset>
      <build_id>NPU.FW.1.0-00052-SM8150-1</build_id>
      <windows_root_path cmm_root_path_var="NPU_BUILDROOT">.\npu\</windows_root_path>
      <linux_root_path cmm_root_path_var="NPU_BUILDROOT">./npu/</linux_root_path>
      <image_dir>npu_proc</image_dir>
      <release_path>BIN</release_path>
      <download_file cmm_file_var="NPU_BINARY" pil_split="npu" minimized="true">
        <file_name>npu.mbn</file_name>
        <file_path flavor="8155_la">npu_proc/build/ms/signed/</file_path>
      </download_file>
      <wf_step_filter>non_hlos,multi_image</wf_step_filter>
      <buildfile_path>npu_proc/build/</buildfile_path>
      <build_command>build.sh npu</build_command>
    </build>
    <build>
      <name>aop_6155</name>
      <role>aop</role>
      <chipset>SM6155</chipset>
      <build_id>AOP.HO.2.0.1-00059-SM6150AUAAANAZO-1</build_id>
      <windows_root_path cmm_root_path_var="AOP_BUILDROOT">.\aop_6155\</windows_root_path>
      <linux_root_path flavor="6155_la" cmm_root_path_var="AOP_BUILDROOT">./aop_6155/</linux_root_path>
      <image_dir>aop_proc</image_dir>
      <release_path>HY11_CompileTest</release_path>
      <download_file cmm_file_var="AOP_BINARY" flavor="6155_la" minimized="true" backup_partition="aop_b" fastboot_complete="aop_a">
        <file_name>aop.mbn</file_name>
        <file_path>aop_proc/build/ms/bin/AAAAANAZO/</file_path>
      </download_file>
      <download_file cmm_file_var="AOP_ELF" flavor="6155_la" minimized="true">
        <file_name>AOP_AAAAANAZO.elf</file_name>
        <file_path>aop_proc/core/bsp/aop/build/TalosAU</file_path>
      </download_file>
      <wf_step_filter>multi_image</wf_step_filter>
      <buildfile_path>aop_proc/build/</buildfile_path>
      <build_command>build_packed.sh TalosAU</build_command>
    </build>
    <build>
      <name>boot_6155</name>
      <role>boot</role>
      <chipset>SM6155</chipset>
      <build_id>BOOT.XF.3.1-00673-SM6150AUZB-3</build_id>
      <windows_root_path cmm_root_path_var="BOOT_BUILDROOT">.\boot_6155\</windows_root_path>
      <linux_root_path flavor="6155_la" cmm_root_path_var="BOOT_BUILDROOT">./boot_6155/</linux_root_path>
      <image_dir>boot_images</image_dir>
      <release_path>HY11_CompileTest</release_path>
      <download_file cmm_file_var="BOOT_BINARY" flavor="6155_la" minimized="true" backup_partition="xbl_b" fastboot_complete="xbl_a">
        <file_name>xbl.elf</file_name>
        <file_path>boot_images/QcomPkg/SDMPkg/6150/Bin/AU/RELEASE/</file_path>
      </download_file>
      <download_file cmm_file_var="BOOT_CONFIG" flavor="6155_la" minimized="true" backup_partition="xbl_config_b" fastboot_complete="xbl_config_a">
        <file_name>xbl_config.elf</file_name>
        <file_path>boot_images/QcomPkg/SDMPkg/6150/Bin/AU/RELEASE/</file_path>
      </download_file>
      <download_file flavor="6155_la" fastboot_complete="logfs" minimized="true">
        <file_name>logfs_ufs_8mb.bin</file_name>
        <file_path>boot_images/QcomPkg/Tools/binaries/</file_path>
      </download_file>
      <device_programmer cmm_file_var="FIREHOSE_DDR_ELF" flavor="6155_la" minimized="true">
        <file_name>prog_firehose_ddr.elf</file_name>
        <file_path>boot_images/QcomPkg/SDMPkg/6150/Bin/AU/RELEASE/</file_path>
      </device_programmer>
      <device_programmer cmm_file_var="FIREHOSE_LITE_ELF" flavor="6155_la" firehose_type="lite" minimized="true">
        <file_name>prog_firehose_lite.elf</file_name>
        <file_path>boot_images/QcomPkg/SDMPkg/6150/Bin/AU/RELEASE/</file_path>
      </device_programmer>
      <file_ref flavor="6155_la" minimized="true">
        <file_name>*.*</file_name>
        <file_path>boot_images/QcomPkg/Tools/storage/fh_loader/</file_path>
      </file_ref>
      <file_ref flavor="6155_la" storage_type="emmc" minimized="true">
        <file_name>JtagProgrammer.cmm</file_name>
        <file_path>boot_images/QcomPkg/SDMPkg/6150/Tools/storage/eMMC/</file_path>
      </file_ref>
      <file_ref flavor="6155_la" storage_type="ufs" minimized="true">
        <file_name>JtagProgrammer.cmm</file_name>
        <file_path>boot_images/QcomPkg/SDMPkg/6150/Tools/storage/UFS/</file_path>
      </file_ref>
      <file_ref flavor="6155_la" storage_type="ufs" minimized="true">
        <file_name>JtagProgrammer.elf</file_name>
        <file_path>boot_images/QcomPkg/SDMPkg/6150/Bin/AU/RELEASE/</file_path>
      </file_ref>
      <file_ref cmm_file_var="BOOT_ELF" ignore="true" flavor="6155_la" minimized="true">
        <file_name>XBLLoader.dll</file_name>
        <file_path>boot_images/Build/SDM6150_Loader/RELEASE_CLANG100LINUX/AARCH64/QcomPkg/XBLLoader/XBLLoader/RELEASE/</file_path>
      </file_ref>
      <wf_step_filter>multi_image</wf_step_filter>
      <buildfile_path>python/</buildfile_path>
      <build_command>python -u boot_images/QcomPkg/buildex.py --variant AU -t SDM6150Pkg,QcomToolsPkg,QcomTestPkg,QcomSocPlatTestPkg,QcomCatePkg,QuestPkg,LoaderTestPkg &amp;&amp; rm -f boot_images/Conf/* &amp;&amp; cd boot_images/BaseTools &amp;&amp; rm -f ./workspace.txt &amp;&amp; make clean &amp;&amp; cd ../.. &amp;&amp; python boot_images/packit.py -t SDM6150,QcomTools,QcomTest --variant AUZB &amp;&amp; mkdir HY11_CompileTest &amp;&amp; cp -r ./HY11_1/* ./HY11_CompileTest/ &amp;&amp; echo &quot;./HY11_CompileTest/boot_images/QcomPkg/SDMPkg/6150/Bin/AU/DEBUG/xbl.elf&quot; &gt;&gt; BuildProducts.txt &amp;&amp; echo &quot;./HY11_CompileTest/boot_images/QcomPkg/SDMPkg/6150/Bin/AU/DEBUG/xbl_config.elf&quot; &gt;&gt; BuildProducts.txt &amp;&amp; echo &quot;./HY11_CompileTest/boot_images/QcomPkg/SDMPkg/6150/Bin/AU/RELEASE/xbl.elf&quot; &gt;&gt; BuildProducts.txt &amp;&amp; echo &quot;./HY11_CompileTest/boot_images/QcomPkg/SDMPkg/6150/Bin/AU/RELEASE/xbl_config.elf&quot; &gt;&gt; BuildProducts.txt &amp;&amp; python -u HY11_CompileTest/boot_images/QcomPkg/buildex.py --variant AU -t SDM6150Pkg,QcomToolsPkg,QcomTestPkg</build_command>
    </build>
    <build>
      <name>tz_6155</name>
      <role>tz</role>
      <chipset>SM6155</chipset>
      <build_id>TZ.XF.5.4-00358-S6150AAAAANAZT-1</build_id>
      <short_build_path cmm_var="TZ_BUILDID" flavor="6155_la">PAZAANAA</short_build_path>
      <windows_root_path cmm_root_path_var="TZ_BUILDROOT">.\tz_6155\</windows_root_path>
      <linux_root_path cmm_root_path_var="TZ_BUILDROOT">./tz_6155/</linux_root_path>
      <image_dir>trustzone_images</image_dir>
      <release_path>HY11_1</release_path>
      <download_file cmm_file_var="TZ_BINARY" flavor="6155_la" minimized="true" backup_partition="tz_b" fastboot_complete="tz_a">
        <file_name>tz.mbn</file_name>
        <file_path>trustzone_images/build/ms/bin/PAZAANAA/</file_path>
      </download_file>
      <download_file cmm_file_var="TZDEVCFG_BINARY" flavor="6155_la" minimized="true" backup_partition="devcfg_b" fastboot_complete="devcfg_a">
        <file_name>devcfg_auto.mbn</file_name>
        <file_path>trustzone_images/build/ms/bin/PAZAANAA/</file_path>
      </download_file>
      <download_file cmm_file_var="STORESEC_BINARY" ignore="true" flavor="6155_la" fastboot_complete="true" minimized="true">
        <file_name>storsec.mbn</file_name>
        <file_path>trustzone_images/build/ms/bin/PAZAANAA/</file_path>
      </download_file>
      <download_file cmm_file_var="KEYMASTER_BINARY" minimized="true" pil_split="km4virt" backup_partition="keymaster_b" fastboot_complete="keymaster_a">
        <file_name>km4virt.mbn</file_name>
        <file_path flavor="6155_la">trustzone_images/build/ms/bin/PAZAANAA/</file_path>
      </download_file>
      <download_file fastboot_complete="cmnlib64_a" pil_split="cmnlib64" minimized="true" cmm_file_var="CMNLIB64_BINARY" flavor="6155_la" backup_partition="cmnlib64_b">
        <file_name>cmnlib64.mbn</file_name>
        <file_path>trustzone_images/build/ms/bin/PAZAANAA/</file_path>
      </download_file>
      <download_file fastboot_complete="cmnlib_a" pil_split="cmnlib" minimized="true" cmm_file_var="CMNLIB_BINARY" flavor="6155_la" backup_partition="cmnlib_b">
        <file_name>cmnlib.mbn</file_name>
        <file_path>trustzone_images/build/ms/bin/PAZAANAA/</file_path>
      </download_file>
      <download_file cmm_file_var="UEFISEC_BINARY" flavor="6155_la" minimized="true" backup_partition="uefisecapp_b" fastboot_complete="uefisecapp_a">
        <file_name>uefi_sec.mbn</file_name>
        <file_path>trustzone_images/build/ms/bin/PAZAANAA/</file_path>
      </download_file>
      <file_ref ignore="true" flavor="6155_la" pil_split="smplap32">
        <file_name>smplap32.mbn</file_name>
        <file_path>trustzone_images/build/ms/bin/PAZAANAA/</file_path>
      </file_ref>
      <file_ref ignore="true" flavor="6155_la" pil_split="smplap64">
        <file_name>smplap64.mbn</file_name>
        <file_path>trustzone_images/build/ms/bin/PAZAANAA/</file_path>
      </file_ref>
      <file_ref ignore="true" flavor="6155_la" pil_split="pr_3_0">
        <file_name>pr_3_0.mbn</file_name>
        <file_path>trustzone_images/build/ms/bin/PAZAANAA/</file_path>
      </file_ref>
      <file_ref ignore="true" flavor="6155_la" pil_split="gptest">
        <file_name>gptest.mbn</file_name>
        <file_path>trustzone_images/build/ms/bin/PAZAANAA/</file_path>
      </file_ref>
      <file_ref ignore="true" flavor="6155_la" pil_split="hdcp1" minimized="true">
        <file_name>hdcp1.mbn</file_name>
        <file_path>trustzone_images/build/ms/bin/PAZAANAA/</file_path>
      </file_ref>
      <file_ref ignore="true" flavor="6155_la" pil_split="hdcp2p2" minimized="true">
        <file_name>hdcp2p2.mbn</file_name>
        <file_path>trustzone_images/build/ms/bin/PAZAANAA/</file_path>
      </file_ref>
      <file_ref ignore="true" flavor="6155_la" pil_split="hdcpsrm" minimized="true">
        <file_name>hdcpsrm.mbn</file_name>
        <file_path>trustzone_images/build/ms/bin/PAZAANAA/</file_path>
      </file_ref>
      <wf_step_filter>non_hlos,multiimage</wf_step_filter>
      <buildfile_path>/</buildfile_path>
      <build_command> </build_command>
    </build>
    <build>
      <name>mpss_6155</name>
      <role>modem</role>
      <chipset>SM6155</chipset>
      <build_id>MPSS.AT.4.3.c10-00138-SM6150_GENNMMDF_PACK-1</build_id>
      <short_build_path cmm_var="MODEM_BUILDID">sm6150.gennmmdf.prod</short_build_path>
      <windows_root_path cmm_root_path_var="MPSS_BUILDROOT">.\mpss_6155\</windows_root_path>
      <linux_root_path cmm_root_path_var="MPSS_BUILDROOT">./mpss_6155/</linux_root_path>
      <image_dir>modem_proc</image_dir>
      <release_path>BIN</release_path>
      <download_file cmm_file_var="MPSS_BINARY" pil_split="modem" minimized="true">
        <file_name>qdsp6sw.mbn</file_name>
        <file_path flavor="6155_la">modem_proc/build/ms/bin/sm6150.gennmmdf.prod/</file_path>
      </download_file>
      <download_file cmm_file_var="MPSS_QDB_BINARY" minimized="true" fat_file_8155="true">
        <file_name>qdsp6m.qdb</file_name>
        <file_path flavor="6155_la">modem_proc/build/ms/bin/sm6150.gennmmdf.prod/</file_path>
      </download_file>
      <file_ref minimized="true">
        <file_name>efs1.bin</file_name>
        <file_path flavor="6155_la">modem_proc/build/ms/bin/sm6150.gennmmdf.prod/</file_path>
      </file_ref>
      <file_ref minimized="true">
        <file_name>efs2.bin</file_name>
        <file_path flavor="6155_la">modem_proc/build/ms/bin/sm6150.gennmmdf.prod/</file_path>
      </file_ref>
      <file_ref minimized="true">
        <file_name>efs3.bin</file_name>
        <file_path flavor="6155_la">modem_proc/build/ms/bin/sm6150.gennmmdf.prod/</file_path>
      </file_ref>
      <file_ref ignore="true" fat_file_6155="true">
        <file_name>modemr.jsn</file_name>
        <file_path flavor="6155_la">modem_proc/build/ms/servreg/sm6150.gennmmdf.prodQ/</file_path>
      </file_ref>
      <wf_step_filter>non_hlos</wf_step_filter>
      <buildfile_path>/</buildfile_path>
      <build_command> </build_command>
    </build>
    <build>
      <name>adsp_6155</name>
      <role>adsp</role>
      <chipset>SM6155</chipset>
      <build_id>ADSP.VT.5.2.c3-00240-SM6150_AU-1</build_id>
      <windows_root_path cmm_root_path_var="ADSP_BUILDROOT">.\adsp_6155\</windows_root_path>
      <linux_root_path cmm_root_path_var="ADSP_BUILDROOT">./adsp_6155/</linux_root_path>
      <image_dir>adsp_proc</image_dir>
      <release_path>HY11_CompileTest</release_path>
      <download_file cmm_file_var="ADSP_BINARY" flavor="6155_la" pil_split="adsp" minimized="true">
        <file_name>adsp.mbn</file_name>
        <file_path>adsp_proc/obj/qdsp6v5_ReleaseG/6150.adsp.au/</file_path>
      </download_file>
      <file_ref flavor="6155_la" fat_file_6155="true" minimized="true">
        <file_name>adspr.jsn</file_name>
        <file_path>adsp_proc/build/ms/servreg/6150.adsp.auQ/</file_path>
      </file_ref>
      <file_ref flavor="6155_la" fat_file_6155="true" minimized="true">
        <file_name>adspua.jsn</file_name>
        <file_path>adsp_proc/build/ms/servreg/6150.adsp.auQ/</file_path>
      </file_ref>
      <file_ref adspso_signed="true" flavor="6155_la">
        <file_name>*</file_name>
        <file_path>adsp_proc/build/dynamic_modules/6150.adsp.au/</file_path>
      </file_ref>
      <wf_step_filter>non_hlos,dspso,multi_image</wf_step_filter>
      <buildfile_path>python/</buildfile_path>
      <build_command>python ./adsp_proc/build/build.py -c sm6150 -f ADSP; python ./adsp_proc/build/build.py -c sm6150 -f ADSP,USES_BUILD_CASA | tee LOG_build.txt</build_command>
    </build>
    <build>
      <name>cdsp_6155</name>
      <role>cdsp</role>
      <chipset>SM6155</chipset>
      <build_id>CDSP.VT.2.2.c2-00052-SM6150-1</build_id>
      <windows_root_path cmm_root_path_var="CDSP_BUILDROOT">.\cdsp_6155\</windows_root_path>
      <linux_root_path cmm_root_path_var="CDSP_BUILDROOT">./cdsp_6155/</linux_root_path>
      <image_dir>cdsp_proc</image_dir>
      <release_path>HY11_CompileTest</release_path>
      <file_ref flavor="6155_la" fat_file_6155="true" minimized="true">
        <file_name>cdspr.jsn</file_name>
        <file_path>cdsp_proc/build/ms/servreg/6150.cdsp.prodQ/</file_path>
      </file_ref>
      <download_file cmm_file_var="CDSP_BINARY" flavor="6155_la" pil_split="cdsp" minimized="true">
        <file_name>cdsp.mbn</file_name>
        <file_path>cdsp_proc/obj/qdsp6v5_ReleaseG/6150.cdsp.prod/</file_path>
      </download_file>
      <file_ref cdspso_signed="true" flavor="6155_la">
        <file_name>*</file_name>
        <file_path>cdsp_proc/build/dynamic_modules/6150.cdsp.prod/</file_path>
      </file_ref>
      <wf_step_filter>non_hlos,dspso,multi_image</wf_step_filter>
      <buildfile_path>python/</buildfile_path>
      <build_command>python ./cdsp_proc/build/build.py -c sm6150 -f CDSP -o all</build_command>
    </build>
    <build>
      <name>video_6155</name>
      <role>video</role>
      <chipset>SM6155</chipset>
      <build_id>VIDEO.VE.5.4-00047-PROD-3</build_id>
      <windows_root_path cmm_root_path_var="VENUS_BUILDROOT">.\video_6155\</windows_root_path>
      <linux_root_path cmm_root_path_var="VENUS_BUILDROOT">./video_6155/</linux_root_path>
      <image_dir>venus_proc</image_dir>
      <release_path>HK11</release_path>
      <download_file cmm_file_var="VENUS_BINARY" flavor="6155_la" pil_split="venus" minimized="true">
        <file_name>venus.mbn</file_name>
        <file_path>venus_proc/build/bsp/asic/build/PROD/mbn/reloc/socCheckV6/signed/</file_path>
      </download_file>
      <wf_step_filter>non_hlos,multi_image</wf_step_filter>
      <buildfile_path>build.cmd/</buildfile_path>
      <build_command>build.cmd build.cmd</build_command>
    </build>
    <build>
      <name>common</name>
      <role>common</role>
      <chipset>SDM855,SM6155</chipset>
      <build_id>Snapdragon_Auto_Gen3.HQX.1.2.1.c1-00004-STD.PROD-2</build_id>
      <windows_root_path>.\</windows_root_path>
      <linux_root_path>./</linux_root_path>
      <image_dir>common</image_dir>
      <release_path/>
      <download_file storage_type="ufs" backup_partition="modem_b" fastboot="modem_a" minimized="true">
        <file_name>NON-HLOS.bin</file_name>
        <file_path flavor="8155_la">common/build/ufs/8155_la/bin</file_path>
        <file_path flavor="6155_la">common/build/ufs/6155_la/bin</file_path>
      </download_file>
      <download_file storage_type="emmc" backup_partition="modem_b" fastboot="modem_a" minimized="true">
        <file_name>NON-HLOS.bin</file_name>
        <file_path flavor="6155_la">common/build/emmc/6155_la/bin</file_path>
      </download_file>
      <download_file storage_type="ufs" backup_partition="bluetooth_b" fastboot="bluetooth_a" minimized="true">
        <file_name>BTFM.bin</file_name>
        <file_path flavor="8155_la">common/build/ufs/8155_la/bin</file_path>
        <file_path flavor="6155_la">common/build/ufs/6155_la/bin</file_path>
      </download_file>
      <download_file storage_type="emmc" backup_partition="bluetooth_b" fastboot="bluetooth_a" minimized="true">
        <file_name>BTFM.bin</file_name>
        <file_path flavor="6155_la">common/build/emmc/6155_la/bin</file_path>
      </download_file>
      <download_file backup_partition="dsp_b" fastboot="dsp_a" minimized="true">
        <file_name>dspso.bin</file_name>
        <file_path flavor="8155_la">common/build/bin/8155_la</file_path>
        <file_path flavor="6155_la">common/build/bin/6155_la</file_path>
      </download_file>
      <download_file cmm_file_var="QUP_BINARY" minimized="true" backup_partition="qupfw_b" fastboot_complete="qupfw_a">
        <file_name>qupv3fw.elf</file_name>
        <file_path flavor="8155_la">common/core_qupv3fw/sdm855/</file_path>
        <file_path flavor="6155_la">common/core_qupv3fw/sm6150/</file_path>
      </download_file>
      <download_file storage_type="ufs" minimized="true" fastboot="ddr">
        <file_name>zeros_5sectors.bin</file_name>
        <file_path flavor="8155_la">common/build/ufs/8155_la</file_path>
        <file_path flavor="6155_la">common/build/ufs/6155_la</file_path>
      </download_file>
      <download_file cmm_file_var="MULTIIMGOEM_BINARY" fastboot_complete="multiimgoem" minimized="true">
        <file_name>multi_image.mbn</file_name>
        <file_path flavor="8155_la">common/build/bin/8155_la</file_path>
        <file_path flavor="6155_la">common/build/bin/6155_la</file_path>
      </download_file>
      <download_file storage_type="ufs" minimized="true">
        <file_name>*.bin</file_name>
        <file_path flavor="8155_la">common/build/ufs/8155_la</file_path>
        <file_path flavor="6155_la">common/build/ufs/6155_la</file_path>
      </download_file>
      <download_file storage_type="emmc" minimized="true">
        <file_name>*.bin</file_name>
        <file_path flavor="6155_la">common/build/emmc/6155_la</file_path>
      </download_file>
      <download_file storage_type="ufs" minimized="true">
        <file_name>*.xml</file_name>
        <file_path flavor="8155_la">common/build/ufs/8155_la</file_path>
        <file_path flavor="6155_la">common/build/ufs/6155_la</file_path>
      </download_file>
      <download_file storage_type="emmc" minimized="true">
        <file_name>*.xml</file_name>
        <file_path flavor="6155_la">common/build/emmc/6155_la</file_path>
      </download_file>
      <download_file ignore="true" minimized="true">
        <file_name>sec.dat</file_name>
        <file_path flavor="8155_la">common/sectools/resources/build/fileversion2/</file_path>
        <file_path flavor="6155_la">common/sectools/resources/build/fileversion2/</file_path>
      </download_file>
      <download_file storage_type="ufs" minimized="true">
        <file_name>gpt_main0.bin</file_name>
        <file_path flavor="8155_la">common/build/ufs/8155_la</file_path>
        <file_path flavor="6155_la">common/build/ufs/6155_la</file_path>
      </download_file>
      <download_file storage_type="emmc" minimized="true">
        <file_name>gpt_main0.bin</file_name>
        <file_path flavor="6155_la">common/build/emmc/6155_la</file_path>
      </download_file>
      <download_file storage_type="ufs" minimized="true">
        <file_name>gpt_main1.bin</file_name>
        <file_path flavor="8155_la">common/build/ufs/8155_la</file_path>
        <file_path flavor="6155_la">common/build/ufs/6155_la</file_path>
      </download_file>
      <download_file storage_type="ufs" minimized="true">
        <file_name>gpt_main2.bin</file_name>
        <file_path flavor="8155_la">common/build/ufs/8155_la</file_path>
        <file_path flavor="6155_la">common/build/ufs/6155_la</file_path>
      </download_file>
      <download_file storage_type="ufs" minimized="true">
        <file_name>gpt_main3.bin</file_name>
        <file_path flavor="8155_la">common/build/ufs/8155_la</file_path>
        <file_path flavor="6155_la">common/build/ufs/6155_la</file_path>
      </download_file>
      <download_file storage_type="ufs" minimized="true">
        <file_name>gpt_main4.bin</file_name>
        <file_path flavor="8155_la">common/build/ufs/8155_la</file_path>
        <file_path flavor="6155_la">common/build/ufs/6155_la</file_path>
      </download_file>
      <download_file storage_type="ufs" minimized="true">
        <file_name>gpt_main5.bin</file_name>
        <file_path flavor="8155_la">common/build/ufs/8155_la</file_path>
        <file_path flavor="6155_la">common/build/ufs/6155_la</file_path>
      </download_file>
      <download_file storage_type="ufs" gpt_file="partition:0" minimized="true">
        <file_name>gpt_both0.bin</file_name>
        <file_path flavor="8155_la">common/build/ufs/8155_la</file_path>
        <file_path flavor="6155_la">common/build/ufs/6155_la</file_path>
      </download_file>
      <download_file storage_type="emmc" gpt_file="partition:0" minimized="true">
        <file_name>gpt_both0.bin</file_name>
        <file_path flavor="6155_la">common/build/emmc/6155_la</file_path>
      </download_file>
      <download_file storage_type="ufs" gpt_file="partition:1" minimized="true">
        <file_name>gpt_both1.bin</file_name>
        <file_path flavor="8155_la">common/build/ufs/8155_la</file_path>
        <file_path flavor="6155_la">common/build/ufs/6155_la</file_path>
      </download_file>
      <download_file storage_type="ufs" gpt_file="partition:2" minimized="true">
        <file_name>gpt_both2.bin</file_name>
        <file_path flavor="8155_la">common/build/ufs/8155_la</file_path>
        <file_path flavor="6155_la">common/build/ufs/6155_la</file_path>
      </download_file>
      <download_file storage_type="ufs" minimized="true">
        <file_name>gpt_both3.bin</file_name>
        <file_path flavor="8155_la">common/build/ufs/8155_la</file_path>
        <file_path flavor="6155_la">common/build/ufs/6155_la</file_path>
      </download_file>
      <download_file storage_type="ufs" gpt_file="partition:4" minimized="true">
        <file_name>gpt_both4.bin</file_name>
        <file_path flavor="8155_la">common/build/ufs/8155_la</file_path>
        <file_path flavor="6155_la">common/build/ufs/6155_la</file_path>
      </download_file>
      <download_file storage_type="ufs" gpt_file="partition:5" minimized="true">
        <file_name>gpt_both5.bin</file_name>
        <file_path flavor="8155_la">common/build/ufs/8155_la</file_path>
        <file_path flavor="6155_la">common/build/ufs/6155_la</file_path>
      </download_file>
      <file_ref ignore="true" flavor="8155_la" vm_linux="true">
        <file_name>vmlinux</file_name>
        <file_path>/LINUX/android/out/target/product/msmnile_gvmq/obj/KERNEL_OBJ/</file_path>
      </file_ref>
      <file_ref ignore="true" flavor="6155_la" vm_linux="true">
        <file_name>vmlinux</file_name>
        <file_path>/LINUX/android/out/target/product/msmnile_gvmq/obj/KERNEL_OBJ/</file_path>
      </file_ref>
      <file_ref raw_partition="true" flavor="8155_la" storage_type="ufs">
        <file_name>partition_la.xml</file_name>
        <file_path>common/config/8155/ufs/</file_path>
      </file_ref>
      <file_ref raw_partition="true" flavor="6155_la" storage_type="ufs">
        <file_name>partition_la.xml</file_name>
        <file_path>common/config/6155/ufs/</file_path>
      </file_ref>
      <file_ref raw_partition="true" flavor="6155_la" storage_type="emmc">
        <file_name>partition_la.xml</file_name>
        <file_path>common/config/6155/emmc/</file_path>
      </file_ref>
      <partition_file storage_type="ufs">
        <file_name>rawprogram_unsparse0.xml</file_name>
        <file_path flavor="8155_la">common/build/ufs/8155_la/bin/sparse_images/</file_path>
        <file_path flavor="6155_la">common/build/ufs/6155_la/bin/sparse_images/</file_path>
      </partition_file>
      <partition_file storage_type="emmc">
        <file_name>rawprogram_unsparse0.xml</file_name>
        <file_path flavor="6155_la">common/build/emmc/6155_la/bin/sparse_images/</file_path>
      </partition_file>
      <partition_patch_file storage_type="ufs">
        <file_name>patch0.xml</file_name>
        <file_path flavor="8155_la">common/build/ufs/8155_la</file_path>
        <file_path flavor="6155_la">common/build/ufs/6155_la</file_path>
      </partition_patch_file>
      <partition_patch_file storage_type="emmc">
        <file_name>patch0.xml</file_name>
        <file_path flavor="6155_la">common/build/emmc/6155_la</file_path>
      </partition_patch_file>
      <partition_file storage_type="ufs">
        <file_name>rawprogram1.xml</file_name>
        <file_path flavor="8155_la">common/build/ufs/8155_la</file_path>
        <file_path flavor="6155_la">common/build/ufs/6155_la</file_path>
      </partition_file>
      <partition_patch_file storage_type="ufs">
        <file_name>patch1.xml</file_name>
        <file_path flavor="8155_la">common/build/ufs/8155_la</file_path>
        <file_path flavor="6155_la">common/build/ufs/6155_la</file_path>
      </partition_patch_file>
      <partition_file storage_type="ufs">
        <file_name>rawprogram2.xml</file_name>
        <file_path flavor="8155_la">common/build/ufs/8155_la</file_path>
        <file_path flavor="6155_la">common/build/ufs/6155_la</file_path>
      </partition_file>
      <partition_patch_file storage_type="ufs">
        <file_name>patch2.xml</file_name>
        <file_path flavor="8155_la">common/build/ufs/8155_la</file_path>
        <file_path flavor="6155_la">common/build/ufs/6155_la</file_path>
      </partition_patch_file>
      <partition_file storage_type="ufs">
        <file_name>rawprogram3.xml</file_name>
        <file_path flavor="8155_la">common/build/ufs/8155_la</file_path>
        <file_path flavor="6155_la">common/build/ufs/6155_la</file_path>
      </partition_file>
      <partition_file storage_type="ufs">
        <file_name>rawprogram4.xml</file_name>
        <file_path flavor="8155_la">common/build/ufs/8155_la</file_path>
        <file_path flavor="6155_la">common/build/ufs/6155_la</file_path>
      </partition_file>
      <partition_patch_file storage_type="ufs">
        <file_name>patch3.xml</file_name>
        <file_path flavor="8155_la">common/build/ufs/8155_la</file_path>
        <file_path flavor="6155_la">common/build/ufs/6155_la</file_path>
      </partition_patch_file>
      <partition_patch_file storage_type="ufs">
        <file_name>patch4.xml</file_name>
        <file_path flavor="8155_la">common/build/ufs/8155_la</file_path>
        <file_path flavor="6155_la">common/build/ufs/6155_la</file_path>
      </partition_patch_file>
      <partition_file storage_type="ufs">
        <file_name>rawprogram5.xml</file_name>
        <file_path flavor="8155_la">common/build/ufs/8155_la</file_path>
        <file_path flavor="6155_la">common/build/ufs/6155_la</file_path>
      </partition_file>
      <partition_patch_file storage_type="ufs">
        <file_name>patch5.xml</file_name>
        <file_path flavor="8155_la">common/build/ufs/8155_la</file_path>
        <file_path flavor="6155_la">common/build/ufs/6155_la</file_path>
      </partition_patch_file>
      <file_ref flavor="8155_la" storage_type="ufs" fat_file_ufs_8155_la="true">
        <file_name>*</file_name>
        <file_path>common/build/ufs/8155_la/bin/pil_split_bins/</file_path>
      </file_ref>
      <file_ref storage_type="ufs" fat_file_ufs_6155_la="true">
        <file_name>*</file_name>
        <file_path flavor="6155_la">common/build/ufs/6155_la/bin/pil_split_bins/</file_path>
      </file_ref>
      <file_ref storage_type="emmc" fat_file_emmc_6155_la="true">
        <file_name>*</file_name>
        <file_path flavor="6155_la">common/build/emmc/6155_la/bin/pil_split_bins/</file_path>
      </file_ref>
      <download_file storage_type="ufs" minimized="true">
        <file_name>*.img</file_name>
        <file_path flavor="8155_la">common/build/ufs/8155_la/bin/sparse_images/</file_path>
        <file_path flavor="6155_la">common/build/ufs/6155_la/bin/sparse_images/</file_path>
      </download_file>
      <download_file storage_type="emmc" minimized="true">
        <file_name>*.img</file_name>
        <file_path flavor="6155_la">common/build/emmc/6155_la/bin/sparse_images/</file_path>
      </download_file>
      <wf_step_filter>partition,non_hlos,cmm_scripts</wf_step_filter>
    </build>
    <build>
      <name>glue</name>
      <role>glue</role>
      <chipset>SDM855</chipset>
      <build_id>GLUE.SPF_HQX121_AUTO.1.17-00006-NOOP_TEST-1</build_id>
      <windows_root_path>.\</windows_root_path>
      <linux_root_path>./</linux_root_path>
      <release_path/>
      <image_dir>common</image_dir>
      <wf_step_filter>partition,non_hlos,cmm_scripts</wf_step_filter>
      <buildfile_path>common/build/</buildfile_path>
      <build_command>build.sh common/build/build.sh</build_command>
    </build>
  </builds_flat>
  <build_tools>
   
  </build_tools>
  <external_tools>
    <tool>
      <name>trace32</name>
      <version cmm_var="T32_VER">LT180417</version>
    </tool>
    <tool>
      <name>qpst</name>
      <version>TBD</version>
      <path>TBD</path>
    </tool>
    <tool>
      <name>qxdm</name>
      <version>TBD</version>
      <path>TBD</path>
    </tool>
  </external_tools>
  <workflow>
    <tool>
      <name>fatgen.py</name>
      <build>common</build>
      <path>./common/config/storage/</path>
    </tool>
    <tool>
      <name>fatadd.py</name>
      <build>common</build>
      <path>./common/config/storage/</path>
    </tool>
    <tool>
      <name>msp</name>
      <build>common</build>
      <path>./common/config/storage/</path>
    </tool>
    <tool>
      <name>checksparse.py</name>
      <build>common</build>
      <path>./common/config/storage/</path>
    </tool>
    <tool>
      <name>pil-splitter.py</name>
      <build>common</build>
      <path>./common/config/</path>
    </tool>
    <tool>
      <name>sectools.py</name>
      <build>common</build>
      <path>./common/sectools/</path>
    </tool>
    <tool>
      <name>ptool.py</name>
      <build>common</build>
      <path>./common/config/storage/</path>
    </tool>
    <tool>
      <name>fat_creation.py</name>
      <build>common</build>
      <path>./common/build/app</path>
    </tool>
    <tool>
      <name>checksparse.py</name>
      <build>common</build>
      <path>./common/config/storage/</path>
    </tool>
    <tool>
      <name>make_ext4fs_win.exe</name>
      <build>adsp_8155</build>
      <path>./adsp_proc/build/ext4fs_tools/windows/</path>
    </tool>
    <tool>
      <name>simg2img_win.exe</name>
      <build>adsp_8155</build>
      <path>./adsp_proc/build/ext4fs_tools/windows/</path>
    </tool>
    <tool>
      <name>make_ext4fs</name>
      <build>adsp_8155</build>
      <path>./adsp_proc/build/ext4fs_tools/ubuntu</path>
    </tool>
    <tool>
      <name>simg2img</name>
      <build>adsp_8155</build>
      <path>./adsp_proc/build/ext4fs_tools/ubuntu</path>
    </tool>
    <step filter="partition" flavor="8155_la" type="exec">
      <exec_dir>./ufs/8155_la</exec_dir>
      <params>python @tool_name -x ./../../../config/8155/ufs/partition_la.xml</params>
      <tool_name>ptool.py</tool_name>
    </step>
    <step filter="partition" flavor="6155_la" type="exec">
      <exec_dir>./ufs/6155_la</exec_dir>
      <params>python @tool_name -x ./../../../config/6155/ufs/partition_la.xml</params>
      <tool_name>ptool.py</tool_name>
    </step>
    <step filter="partition" flavor="6155_la" type="exec">
      <exec_dir>./emmc/6155_la</exec_dir>
      <params>python @tool_name -x ./../../../config/6155/emmc/partition_la.xml</params>
      <tool_name>ptool.py</tool_name>
    </step>
    <step filter="hlos" flavor="8155_la" type="exec" storage_type="ufs">
      <exec_dir>./ufs/8155_la/bin/sparse_images</exec_dir>
      <params>python @tool_name -i @rawprogram_xml -s @root_dir + @sparse_dir -s @root_dir_lagvm + @la_apps_dir -o rawprogram_unsparse0.xml</params>
      <tool_name>checksparse.py</tool_name>
      <root_dir>$[build:apps]</root_dir>
      <sparse_dir>qnx_ap/target/hypervisor/host/out_8155/</sparse_dir>
      <root_dir_lagvm>$[build:lagvm]</root_dir_lagvm>
      <la_apps_dir>LINUX/android/out/target/product/msmnile_gvmq/</la_apps_dir>
      <rawprogram_xml>./../../rawprogram0.xml</rawprogram_xml>
    </step>
    <step filter="hlos" flavor="6155_la" type="exec" storage_type="ufs">
      <exec_dir>./ufs/6155_la/bin/sparse_images</exec_dir>
      <params>python @tool_name -i @rawprogram_xml -s @root_dir + @sparse_dir -s @root_dir_lagvm + @la_apps_dir -o rawprogram_unsparse0.xml</params>
      <tool_name>checksparse.py</tool_name>
      <root_dir>$[build:apps]</root_dir>
      <sparse_dir>qnx_ap/target/hypervisor/host/out_6155/</sparse_dir>
      <root_dir_lagvm>$[build:lagvm]</root_dir_lagvm>
      <la_apps_dir>LINUX/android/out/target/product/msmnile_gvmq/</la_apps_dir>
      <rawprogram_xml>./../../rawprogram0.xml</rawprogram_xml>
    </step>
    <step filter="hlos" flavor="6155_la" type="exec" storage_type="emmc">
      <exec_dir>./emmc/6155_la/bin/sparse_images</exec_dir>
      <params>python @tool_name -i @rawprogram_xml -s @root_dir + @sparse_dir -s @root_dir_lagvm + @la_apps_dir -o rawprogram_unsparse0.xml</params>
      <tool_name>checksparse.py</tool_name>
      <root_dir>$[build:apps]</root_dir>
      <sparse_dir>qnx_ap/target/hypervisor/host/out_6155/</sparse_dir>
      <root_dir_lagvm>$[build:lagvm]</root_dir_lagvm>
      <la_apps_dir>LINUX/android/out/target/product/msmnile_gvmq/</la_apps_dir>
      <rawprogram_xml>./../../rawprogram0.xml</rawprogram_xml>
    </step>
    <step filter="non_hlos" flavor="8155_la" type="exec">
      <params>python @tool_name @src_file @destn_dir + @src_file_var</params>
      <tool_name>pil-splitter.py</tool_name>
      <src_file_vars>$[attribute:pil_split]</src_file_vars>
      <destn_dir>./ufs/8155_la/bin/pil_split_bins</destn_dir>
    </step>
    <step filter="non_hlos" flavor="6155_la" type="exec">
      <params>python @tool_name @src_file @destn_dir + @src_file_var</params>
      <tool_name>pil-splitter.py</tool_name>
      <src_file_vars>$[attribute:pil_split]</src_file_vars>
      <destn_dir>./ufs/6155_la/bin/pil_split_bins</destn_dir>
    </step>
    <step filter="non_hlos" flavor="6155_la" type="exec">
      <params>python @tool_name @src_file @destn_dir + @src_file_var</params>
      <tool_name>pil-splitter.py</tool_name>
      <src_file_vars>$[attribute:pil_split]</src_file_vars>
      <destn_dir>./emmc/6155_la/bin/pil_split_bins</destn_dir>
    </step>
    <step filter="non_hlos" flavor="8155_la" type="delete" storage_type="ufs">
      <destn>./ufs/8155_la/bin/NON-HLOS.bin</destn>
    </step>
    <step filter="non_hlos" flavor="6155_la" type="delete" storage_type="ufs">
      <destn>./ufs/6155_la/bin/NON-HLOS.bin</destn>
    </step>
    <step filter="non_hlos" flavor="6155_la" type="delete" storage_type="emmc">
      <destn>./emmc/6155_la/bin/NON-HLOS.bin</destn>
    </step>
    <step filter="non_hlos" flavor="8155_la" type="exec" storage_type="ufs">
      <params>python @tool_name -a @attributes -c @destn --ps @partition_size --ss 4096 -f 8155_la --fatgen @fatgen_loc --fatadd @fatadd_loc</params>
      <tool_name>fat_creation.py</tool_name>
      <attributes>&quot;(fat_file_8155)||(fat_file_ufs_8155_la)&quot;</attributes>
      <destn>./ufs/8155_la/bin/NON-HLOS.bin</destn>
      <partition_size>@getPartitionSize(&quot;./../config/8155/ufs/partition_la.xml&quot;,&quot;modem_a&quot;)</partition_size>
      <fatgen_loc>@root_dir + common/config/storage/fatgen.py</fatgen_loc>
      <fatadd_loc>@root_dir + common/config/storage/fatadd.py</fatadd_loc>
      <root_dir>$[build:common]</root_dir>
    </step>
    <step filter="non_hlos" flavor="6155_la" type="exec" storage_type="ufs">
      <params>python @tool_name -a @attributes -c @destn --ps @partition_size --ss 4096 -f 6155_la --fatgen @fatgen_loc --fatadd @fatadd_loc</params>
      <tool_name>fat_creation.py</tool_name>
      <attributes>&quot;(fat_file_6155)||(fat_file_ufs_6155_la)&quot;</attributes>
      <destn>./ufs/6155_la/bin/NON-HLOS.bin</destn>
      <partition_size>@getPartitionSize(&quot;./../config/6155/ufs/partition_la.xml&quot;,&quot;modem_a&quot;)</partition_size>
      <fatgen_loc>@root_dir + common/config/storage/fatgen.py</fatgen_loc>
      <fatadd_loc>@root_dir + common/config/storage/fatadd.py</fatadd_loc>
      <root_dir>$[build:common]</root_dir>
    </step>
    <step filter="non_hlos" flavor="6155_la" type="exec" storage_type="emmc">
      <params>python @tool_name -a @attributes -c @destn --ps @partition_size -f 6155_la --fatgen @fatgen_loc --fatadd @fatadd_loc</params>
      <tool_name>fat_creation.py</tool_name>
      <attributes>&quot;(fat_file_6155)||(fat_file_emmc_6155_la)&quot;</attributes>
      <destn>./emmc/6155_la/bin/NON-HLOS.bin</destn>
      <partition_size>@getPartitionSize(&quot;./../config/6155/emmc/partition_la.xml&quot;,&quot;modem_a&quot;)</partition_size>
      <fatgen_loc>@root_dir + common/config/storage/fatgen.py</fatgen_loc>
      <fatadd_loc>@root_dir + common/config/storage/fatadd.py</fatadd_loc>
      <root_dir>$[build:common]</root_dir>
    </step>
    <step filter="non_hlos" flavor="8155_la" type="exec">
      <params>python @tool_name --sectorsize 4096 -n @destn -f @src_file -d image/qca6174</params>
      <tool_name>fatadd.py</tool_name>
      <destn>./ufs/8155_la/bin/NON-HLOS.bin</destn>
      <src_files>$[attribute:fat_file_rome_8155]</src_files>
    </step>
    <step filter="non_hlos" flavor="8155_la" type="exec">
      <params>python @tool_name --sectorsize 4096 -n @destn -f @src_file -d image/qca6390</params>
      <tool_name>fatadd.py</tool_name>
      <destn>./ufs/8155_la/bin/NON-HLOS.bin</destn>
      <src_files>$[attribute:fat_file_hst_8155]</src_files>
    </step>
    <step filter="non_hlos" flavor="8155_la" type="exec">
      <params>python @tool_name --sectorsize 4096 -n @destn -f @src_file -d image/qcn7605</params>
      <tool_name>fatadd.py</tool_name>
      <destn>./ufs/8155_la/bin/NON-HLOS.bin</destn>
      <src_files>$[attribute:fat_file_gen_8155]</src_files>
    </step>
    <step filter="non_hlos" flavor="6155_la" type="exec">
      <params>python @tool_name --sectorsize 4096 -n @destn -f @src_file -d image/qca6174</params>
      <tool_name>fatadd.py</tool_name>
      <destn>./ufs/6155_la/bin/NON-HLOS.bin</destn>
      <src_files>$[attribute:fat_file_rome_6155]</src_files>
    </step>
    <step filter="non_hlos" flavor="6155_la" type="exec">
      <params>python @tool_name --sectorsize 4096 -n @destn -f @src_file -d image/qca6390</params>
      <tool_name>fatadd.py</tool_name>
      <destn>./ufs/6155_la/bin/NON-HLOS.bin</destn>
      <src_files>$[attribute:fat_file_hst_6155]</src_files>
    </step>
    <step filter="non_hlos" flavor="6155_la" type="exec">
      <params>python @tool_name --sectorsize 4096 -n @destn -f @src_file -d image/qcn7605</params>
      <tool_name>fatadd.py</tool_name>
      <destn>./ufs/6155_la/bin/NON-HLOS.bin</destn>
      <src_files>$[attribute:fat_file_gen_6155]</src_files>
    </step>
    <step filter="non_hlos" flavor="6155_la" type="exec">
      <params>python @tool_name --sectorsize 512 -n @destn -f @src_file -d image/qca6174</params>
      <tool_name>fatadd.py</tool_name>
      <destn>./emmc/6155_la/bin/NON-HLOS.bin</destn>
      <src_files>$[attribute:fat_file_rome_6155]</src_files>
    </step>
    <step filter="non_hlos" flavor="6155_la" type="exec">
      <params>python @tool_name --sectorsize 512 -n @destn -f @src_file -d image/qca6390</params>
      <tool_name>fatadd.py</tool_name>
      <destn>./emmc/6155_la/bin/NON-HLOS.bin</destn>
      <src_files>$[attribute:fat_file_hst_6155]</src_files>
    </step>
    <step filter="non_hlos" flavor="6155_la" type="exec">
      <params>python @tool_name --sectorsize 512 -n @destn -f @src_file -d image/qcn7605</params>
      <tool_name>fatadd.py</tool_name>
      <destn>./emmc/6155_la/bin/NON-HLOS.bin</destn>
      <src_files>$[attribute:fat_file_gen_6155]</src_files>
    </step>
    <step filter="non_hlos" flavor="8155_la" type="delete" storage_type="ufs">
      <destn>./ufs/8155_la/bin/BTFM.bin</destn>
    </step>
    <step filter="non_hlos" flavor="6155_la" type="delete" storage_type="ufs">
      <destn>./ufs/6155_la/bin/BTFM.bin</destn>
    </step>
    <step filter="non_hlos" flavor="6155_la" type="delete" storage_type="emmc">
      <destn>./emmc/6155_la/bin/BTFM.bin</destn>
    </step>
    <step filter="non_hlos" flavor="8155_la" type="exec" storage_type="ufs">
      <params>python @tool_name -a @attributes -c @destn --ps 64 --ss 4096 -f $[flavor:.] --fatgen @fatgen_loc --fatadd @fatadd_loc</params>
      <tool_name>fat_creation.py</tool_name>
      <attributes>fat_file_btfm_8155</attributes>
      <destn>./ufs/8155_la/bin/BTFM.bin</destn>
      <fatgen_loc>@root_dir + common/config/storage/fatgen.py</fatgen_loc>
      <fatadd_loc>@root_dir + common/config/storage/fatadd.py</fatadd_loc>
      <root_dir>$[build:common]</root_dir>
    </step>
    <step filter="non_hlos" flavor="6155_la" type="exec" storage_type="ufs">
      <params>python @tool_name -a @attributes -c @destn --ps 64 --ss 4096 -f $[flavor:.] --fatgen @fatgen_loc --fatadd @fatadd_loc</params>
      <tool_name>fat_creation.py</tool_name>
      <attributes>fat_file_btfm_6155</attributes>
      <destn>./ufs/6155_la/bin/BTFM.bin</destn>
      <fatgen_loc>@root_dir + common/config/storage/fatgen.py</fatgen_loc>
      <fatadd_loc>@root_dir + common/config/storage/fatadd.py</fatadd_loc>
      <root_dir>$[build:common]</root_dir>
    </step>
    <step filter="non_hlos" flavor="6155_la" type="exec" storage_type="emmc">
      <params>python @tool_name -a @attributes -c @destn --ps 64 --ss 4096 -f 6155_la --fatgen @fatgen_loc --fatadd @fatadd_loc</params>
      <tool_name>fat_creation.py</tool_name>
      <attributes>fat_file_btfm_6155</attributes>
      <destn>./emmc/6155_la/bin/BTFM.bin</destn>
      <fatgen_loc>@root_dir + common/config/storage/fatgen.py</fatgen_loc>
      <fatadd_loc>@root_dir + common/config/storage/fatadd.py</fatadd_loc>
      <root_dir>$[build:common]</root_dir>
    </step>
    <step filter="multi_image" flavor="8155_la" type="delete">
      <destn_dir>./bin/8155_la/multi_image</destn_dir>
    </step>
    <step filter="multi_image" flavor="6155_la" type="delete">
      <destn_dir>./bin/6155_la/multi_image</destn_dir>
    </step>
    <step filter="multi_image" flavor="8155_la" type="exec">
      <params>python @tool_name secimage -p sm8150 -m @root_dir --m_gen --m_sign --m_validate -a -o @destn</params>
      <tool_name>sectools.py</tool_name>
      <destn>./bin/8155_la/multi_image/</destn>
      <root_dir>$[build:common]</root_dir>
    </step>
    <step filter="multi_image" flavor="8155_la" type="copy">
      <destn_dir>./bin/8155_la</destn_dir>
      <src_files>./bin/8155_la/multi_image/sm8150/multi_image/multi_image.mbn</src_files>
    </step>
    <step filter="multi_image" flavor="6155_la" type="exec">
      <params>python @tool_name secimage -p sm6150 -m @root_dir --m_gen --m_sign --m_validate -a -o @destn</params>
      <tool_name>sectools.py</tool_name>
      <destn>./bin/6155_la/multi_image/</destn>
      <root_dir>$[build:common]</root_dir>
    </step>
    <step filter="multi_image" flavor="6155_la" type="copy">
      <destn_dir>./bin/6155_la</destn_dir>
      <src_files>./bin/6155_la/multi_image/sm6150/multi_image/multi_image.mbn</src_files>
    </step>
    <step filter="cmm_scripts" flavor="8155_la" type="exec">
      <params>python @tool_name --spf</params>
      <exec_dir>../Core/8155</exec_dir>
      <tool_name>MetascriptsBuilder.py</tool_name>
    </step>
    <step filter="cmm_scripts" flavor="6155_la" type="exec">
      <params>python @tool_name --spf</params>
      <exec_dir>../Core/6155</exec_dir>
      <tool_name>MetascriptsBuilder_6155.py</tool_name>
    </step>
    <step filter="cmm_scripts" flavor="8155_la" type="exec">
      <params>python @tool_name</params>
      <exec_dir>../Core/8155/tools/tools/</exec_dir>
      <tool_name>RootCMMBuilder.py</tool_name>
    </step>
    <step filter="cmm_scripts" flavor="6155_la" type="exec">
      <params>python @tool_name</params>
      <exec_dir>../Core/6155/tools/tools/</exec_dir>
      <tool_name>RootCMMBuilder_6155.py</tool_name>
    </step>
  </workflow>
</contents>
