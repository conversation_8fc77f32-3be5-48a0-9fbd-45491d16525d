OEM signed image with RSAPSS
ERROR: Following validations failed for the image:
       1. Following signing attributes do not match: 
          | Attribute | Image | Config |
          |-----------|-------|--------|
          |   debug   |  0x1  |  0x0   |
          
Image /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/tz_8155/trustzone_images/build/ms/bin/YAQAANAA/devcfg_auto.mbn is not encrypted

Base Properties: 
| Integrity Check             | True  |
| Signed                      | True  |
| Encrypted                   | False |
| Size of signature           | 256   |
| Size of one cert            | 2048  |
| Num of certs in cert chain  | 3     |
| Number of root certs        | 1     |
| Maximum number of root certs| None  |
| Cert chain size             | 6144  |

ELF Properties: 
Header: 
| Magic                      | ELF                           |
| Class                      | ELF64                          |
| Data                       | 2's complement, little endian  |
| Version                    | 1 (Current)                    |
| OS/ABI                     | No extensions or unspecified   |
| ABI Version                | 0                              |
| Type                       | EXEC (Executable file)         |
| Machine                    | 183                            |
| Version                    | 0x1                            |
| Entry address              | 0x1c00d000                     |
| Program headers offset     | 0x00000040                     |
| Section headers offset     | 0x00000000                     |
| Flags                      | 0x00000000                     |
| ELF header size            | 64                             |
| Program headers size       | 56                             |
| Number of program headers  | 2                              |
| Section headers size       | 64                             |
| Number of section headers  | 0                              |
| String table section index | 0                              |

Program Headers: 
| Num | Type | Offset | VirtAddr | PhysAddr | FileSize | MemSize | Flags | Align |
|-----|------|--------|----------|----------|----------|---------|-------|-------|
|  1  | LOAD | 0x3000 |0x1c00d000|0x1c00d000|  0x91f8  |  0x91f8 |   RW  | 0x1000|
|  2  | LOAD | 0xd000 |0x85cfd000|0x85cfd000|  0x1fc0  |  0x1fc0 |   RW  | 0x1000|

Hash Segment Properties: 
| Header Size     | 168B  |
| Hash Algorithm  | sha384|

Header: 
| cert_chain_ptr              | 0xffffffff  |
| cert_chain_size             | 0x00001800  |
| cert_chain_size_qti         | 0x00000000  |
| code_size                   | 0x000000c0  |
| image_id                    | 0x00000004  |
| image_size                  | 0x000019c0  |
| metadata_major_version      | 0x00000000  |
| metadata_major_version_qti  | 0x00000000  |
| metadata_minor_version      | 0x00000000  |
| metadata_minor_version_qti  | 0x00000000  |
| metadata_size               | 0x00000078  |
| metadata_size_qti           | 0x00000000  |
| sig_ptr                     | 0xffffffff  |
| sig_size                    | 0x00000100  |
| sig_size_qti                | 0x00000000  |
| version                     | 0x00000006  |
Metadata:
| anti_rollback_version        | 0x00000000  |
| app_id                       | 0x00000000  |
| debug                        | 0x00000001  |
| hw_id                        | 0x00000000  |
| in_use_soc_hw_version        | 0x00000001  |
| model_id                     | 0x00000000  |
| mrc_index                    | 0x00000000  |
| multi_serial_numbers         | 0x00000000  |
| oem_id                       | 0x00000000  |
| oem_id_independent           | 0x00000000  |
| root_revoke_activate_enable  | 0x00000000  |
| rot_en                       | 0x00000000  |
| soc_vers                     | 0x00006003  |
| sw_id                        | 0x00000005  |
| uie_key_switch_enable        | 0x00000000  |
| use_serial_number_in_signing | 0x00000000  |


