[19:18:00] - fat_creation.py: Specified container location:
[19:18:00] -                  ./ufs/8155_la/bin/temp_BTFM.bin
[19:18:00] - fat_creation.py: Specified partition size: 64
[19:18:00] - fat_creation.py: Float partition can't be used for fat creation, hence truncating it. New partition size : 64
[19:18:00] -                  Using fatgen from /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatgen.py
[19:18:00] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatgen.py -f -s 64 -n ./ufs/8155_la/bin/temp_BTFM.bin --sectorsize 4096
-c is deprecated for size in sectors. It now specifies the sector size (512, 4096 etc.)
Using cluster size 16384 bytes
Generated 64MB FAT16 container: ./ufs/8155_la/bin/temp_BTFM.bin
[19:18:00] - fat_creation.py: Adding files into fat container:
[19:18:00] -                  ./ufs/8155_la/bin/temp_BTFM.bin
[19:18:00] - fat_creation.py: Found 9 files
[19:18:00] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/btfm_gen/btfm_proc/bt/build/ms/bin/QCA6595/SCAQBAFM/gnbtfw20.tlv
[19:18:00] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_BTFM.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/btfm_gen/btfm_proc/bt/build/ms/bin/QCA6595/SCAQBAFM/gnbtfw20.tlv -d image --sectorsize 4096
Container size is 67108864 bytes
adding IMAGE
Added IMAGE
adding GNBTFW20.TLV
Added GNBTFW20.TLV
[19:18:00] - ./ufs/8155_la/bin/temp_BTFM.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/btfm_gen/btfm_proc/bt/build/ms/bin/QCA6595/SCAQBAFM/gnbtfw20.tlv: 176128 bytes
[19:18:00] - ./ufs/8155_la/bin/temp_BTFM.bin last modified time stamp   : Tue Aug  5 19:18:00 2025
[19:18:00] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/btfm_gen/btfm_proc/bt/build/ms/bin/QCA6595/SCAQBAFM/gnnv20.bin
[19:18:00] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_BTFM.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/btfm_gen/btfm_proc/bt/build/ms/bin/QCA6595/SCAQBAFM/gnnv20.bin -d image --sectorsize 4096
Container size is 67108864 bytes
Added IMAGE
adding GNNV20.BIN
Added GNNV20.BIN
[19:18:01] - ./ufs/8155_la/bin/temp_BTFM.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/btfm_gen/btfm_proc/bt/build/ms/bin/QCA6595/SCAQBAFM/gnnv20.bin: 192512 bytes
[19:18:01] - ./ufs/8155_la/bin/temp_BTFM.bin last modified time stamp   : Tue Aug  5 19:18:01 2025
[19:18:01] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/btfm_hst/btfm_proc/bt/build/ms/bin/QCA6690/auto/htbtfw10.tlv
[19:18:01] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_BTFM.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/btfm_hst/btfm_proc/bt/build/ms/bin/QCA6690/auto/htbtfw10.tlv -d image --sectorsize 4096
Container size is 67108864 bytes
Added IMAGE
adding HTBTFW10.TLV
Added HTBTFW10.TLV
[19:18:01] - ./ufs/8155_la/bin/temp_BTFM.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/btfm_hst/btfm_proc/bt/build/ms/bin/QCA6690/auto/htbtfw10.tlv: 323584 bytes
[19:18:01] - ./ufs/8155_la/bin/temp_BTFM.bin last modified time stamp   : Tue Aug  5 19:18:01 2025
[19:18:01] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/btfm_hst/btfm_proc/bt/build/ms/bin/QCA6690/auto/htbtfw20.tlv
[19:18:01] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_BTFM.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/btfm_hst/btfm_proc/bt/build/ms/bin/QCA6690/auto/htbtfw20.tlv -d image --sectorsize 4096
Container size is 67108864 bytes
Added IMAGE
adding HTBTFW20.TLV
Added HTBTFW20.TLV
[19:18:01] - ./ufs/8155_la/bin/temp_BTFM.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/btfm_hst/btfm_proc/bt/build/ms/bin/QCA6690/auto/htbtfw20.tlv: 536576 bytes
[19:18:01] - ./ufs/8155_la/bin/temp_BTFM.bin last modified time stamp   : Tue Aug  5 19:18:01 2025
[19:18:01] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/btfm_hst/btfm_proc/bt/build/ms/bin/QCA6690/auto/htnv10.bin
[19:18:01] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_BTFM.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/btfm_hst/btfm_proc/bt/build/ms/bin/QCA6690/auto/htnv10.bin -d image --sectorsize 4096
Container size is 67108864 bytes
Added IMAGE
adding HTNV10.BIN
Added HTNV10.BIN
[19:18:01] - ./ufs/8155_la/bin/temp_BTFM.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/btfm_hst/btfm_proc/bt/build/ms/bin/QCA6690/auto/htnv10.bin: 552960 bytes
[19:18:01] - ./ufs/8155_la/bin/temp_BTFM.bin last modified time stamp   : Tue Aug  5 19:18:01 2025
[19:18:01] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/btfm_hst/btfm_proc/bt/build/ms/bin/QCA6690/auto/htnv20.203
[19:18:01] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_BTFM.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/btfm_hst/btfm_proc/bt/build/ms/bin/QCA6690/auto/htnv20.203 -d image --sectorsize 4096
Container size is 67108864 bytes
Added IMAGE
adding HTNV20.203
Added HTNV20.203
[19:18:01] - ./ufs/8155_la/bin/temp_BTFM.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/btfm_hst/btfm_proc/bt/build/ms/bin/QCA6690/auto/htnv20.203: 569344 bytes
[19:18:01] - ./ufs/8155_la/bin/temp_BTFM.bin last modified time stamp   : Tue Aug  5 19:18:01 2025
[19:18:01] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/btfm_hst/btfm_proc/bt/build/ms/bin/QCA6690/auto/htnv20.bin
[19:18:01] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_BTFM.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/btfm_hst/btfm_proc/bt/build/ms/bin/QCA6690/auto/htnv20.bin -d image --sectorsize 4096
Container size is 67108864 bytes
Added IMAGE
adding HTNV20.BIN
Added HTNV20.BIN
[19:18:01] - ./ufs/8155_la/bin/temp_BTFM.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/btfm_hst/btfm_proc/bt/build/ms/bin/QCA6690/auto/htnv20.bin: 585728 bytes
[19:18:01] - ./ufs/8155_la/bin/temp_BTFM.bin last modified time stamp   : Tue Aug  5 19:18:01 2025
[19:18:01] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/btfm_rome/btfm_proc/bt/build/ms/bin/QCA6574/btfw32.tlv
[19:18:01] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_BTFM.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/btfm_rome/btfm_proc/bt/build/ms/bin/QCA6574/btfw32.tlv -d image --sectorsize 4096
Container size is 67108864 bytes
Added IMAGE
adding BTFW32.TLV
Added BTFW32.TLV
[19:18:01] - ./ufs/8155_la/bin/temp_BTFM.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/btfm_rome/btfm_proc/bt/build/ms/bin/QCA6574/btfw32.tlv: 667648 bytes
[19:18:01] - ./ufs/8155_la/bin/temp_BTFM.bin last modified time stamp   : Tue Aug  5 19:18:01 2025
[19:18:01] - /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/btfm_rome/btfm_proc/bt/build/ms/bin/QCA6574/btnv32.bin
[19:18:01] - Executing: python /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/config/storage/fatadd.py -n ./ufs/8155_la/bin/temp_BTFM.bin -f /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/btfm_rome/btfm_proc/bt/build/ms/bin/QCA6574/btnv32.bin -d image --sectorsize 4096
Container size is 67108864 bytes
Added IMAGE
adding BTNV32.BIN
Added BTNV32.BIN
[19:18:01] - ./ufs/8155_la/bin/temp_BTFM.bin size after adding   /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/btfm_rome/btfm_proc/bt/build/ms/bin/QCA6574/btnv32.bin: 684032 bytes
[19:18:01] - ./ufs/8155_la/bin/temp_BTFM.bin last modified time stamp   : Tue Aug  5 19:18:01 2025
