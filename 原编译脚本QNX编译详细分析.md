# 原编译脚本QNX编译详细分析

## 📋 概述

本文档详细分析原编译脚本中QNX编译的完整流程，包括源码下载、配置解析、构建细节、成果物校验和打包等各个环节。

## 🔍 1. 源码下载路径和配置

### 1.1 SVN根路径配置
```bash
SVN_ROOT=http://swsvn01.adayoge.com/svn/Qualcomm/02_SA8155P
Build_Shell_Path="http://10.2.4.101/svn/Qualcomm/02_SA8155P/01_HS7001A/02_CodeLib/01_MainPath/1.2/02_Android/04_Shell/buildShell3.0/buildShell"
Base_Shell_Path="http://10.2.4.101/svn/Qualcomm/02_SA8155P/01_HS7001A/02_CodeLib/01_MainPath/1.2/02_Android/04_Shell/baseConfigShell"
```

### 1.2 源码模块配置数组
```bash
# 通过XML配置文件生成的源码配置数组
GLOBAL_PROJECT_DEF_SRCCODE=(
    # 格式: "SVN_TRUNK_PATH SVN_TAG_PATH LOCAL_SOURCE_PATH"
    # 示例: "trunk_path tag_path local_path"
)

# 需要删除的继承代码配置
GLOBAL_PROJECT_DEF_REVCODE=(
    # 格式: "TAG_PATH SOURCE_PATH"
)
```

### 1.3 源码下载核心逻辑
```bash
function _export_project_code() {
    for i in "${GLOBAL_PROJECT_DEF_SRCCODE[@]}"; do
        root_path=($i)
        
        # 获取SVN标签路径
        svn_tag=$(get_field_value $configFile ${root_path[${DEF_PROJECT_TAG_PATH}]} tag)
        
        # 构建完整SVN路径
        tmp_path_head_hyp=${GLOBAL_SVN_SOURCE_TAG_MAIN_HYP}/${projectname}_Tag-${projectversion}
        
        # 导出源码
        echo "QNX Export ${root_path[${DEF_PROJECT_TAG_PATH}]} code from ${svn_tag}"
        $SVN_CMD --force export ${tmp_path_head_hyp}${root_path[tmp_index]} ${LOCAL_PATH}/temDir
        
        # 处理MPU模块特殊逻辑
        if [[ "${root_path[${DEF_PROJECT_TAG_PATH}]}" =~ .*MPU.* ]]; then
            # 获取apps/mids/libs列表
            $SVN_CMD list ${tmp_path_head_hyp}${root_path[tmp_index]}/mpu/apps | grep -v "public" | awk -F '/' '{print $1}' >> ${LOCAL_PATH}/appsList.txt
            $SVN_CMD list ${tmp_path_head_hyp}${root_path[tmp_index]}/mpu/mids | grep -v "public" | awk -F '/' '{print $1}' >> ${LOCAL_PATH}/midsList.txt
            $SVN_CMD list ${tmp_path_head_hyp}${root_path[tmp_index]}/mpu/libs | awk -F '/' '{print $1}' >> ${LOCAL_PATH}/libsList.txt
        fi
    done
}
```

## 🔧 2. 配置解析机制

### 2.1 XML配置文件读取
```bash
# XML配置文件读取脚本
readXMLFile.sh

# 主要功能模块
Code_Module=("SDP" "BSP" "MPU" "PUBLISH" "AMSS" "MCU" "4G_MODULE" "LIGHT_SOUND")
OutPut_Module=("ANDROID" "QNX" "AMSS")
Images_Module=("Delete" "Switch")
Package_Module=("Coverity" "Version" "Email")
```

### 2.2 配置文件解析函数
```bash
function get_field_value() {
    # 参数: 配置文件路径 块名 字段名
    if [ ! -f $1 ] || [ $# -ne 3 ]; then   
        return 1  
    fi
    
    blockname=$2  
    fieldname=$3
    
    # 解析配置文件中的特定字段值
    begin_block=0  
    end_block=0
    
    while read line; do
        if [[ $line =~ $blockname ]]; then
            begin_block=1  
            continue  
        fi
        
        if [[ $begin_block -eq 1 ]]; then
            # 处理字段值提取逻辑
            # ...
        fi
    done < $1
}
```

### 2.3 版本信息配置
```bash
function set_version() {
    # 读取车型配置
    CarType=`$LOCAL_PATH/${Build_Shell_Path##*/}/readXMLFile.sh Qnx ${projectname} Package CarType`
    
    if [[ $CarType = "Beiqi" ]]; then
        # 北汽项目特殊版本处理
        sv=`echo ${projectversion//./} | awk -F 'T' '{print $1}'`
        tv="T"`echo ${projectversion//./} | awk -F 'T' '{print $2}'`
        
        # 读取版本相关配置
        ControlSys=`$LOCAL_PATH/${Build_Shell_Path##*/}/readXMLFile.sh Qnx ${projectname} Package Version ControlSys`
        PN=`$LOCAL_PATH/${Build_Shell_Path##*/}/readXMLFile.sh Qnx ${projectname} Package Version PN`
        SoftNum=`$LOCAL_PATH/${Build_Shell_Path##*/}/readXMLFile.sh Qnx ${projectname} Package Version SoftNum`
        ProjectNum=`$LOCAL_PATH/${Build_Shell_Path##*/}/readXMLFile.sh Qnx ${projectname} Package Version ProjectNum`
        IfsVersion=`$LOCAL_PATH/${Build_Shell_Path##*/}/readXMLFile.sh Qnx ${projectname} Package Version IfsVersion`
        
        # 生成更新包名称
        updatePackage=${ControlSys}${PN}${sv}${SoftNum}${ProjectNum}${tv}${PACKAGE_BUILD_DATE:2}
    else
        updatePackage=${projectversion}
    fi
}
```

## 🏗️ 3. QNX构建详细流程

### 3.1 构建环境准备
```bash
function sync_mainline() {
    # 1. 清理旧的构建源码
    echo "Begin delect Qnx output img and update !!!!"
    rm -f $Qualcomm8155BashPath/${projectname}_Package/amss.tar.gz
    rm -rf $Qualcomm8155BashPath/${projectname}_Package/output/Qnx
    rm -rf $Qualcomm8155BashPath/${projectname}_Package/Image/{AMSS,Qnx}
    rm -rf $Qualcomm8155BashPath/${projectname}_Package/Update/{AMSS.tar.gz,QNX.tar.gz}
    
    # 2. 创建目录结构
    mkdir -p $Qualcomm8155BashPath/${projectname}_Package/Image/{Android,Qnx,AMSS}
    mkdir -p $Qualcomm8155BashPath/${projectname}_Package/Update
    mkdir -p $Qualcomm8155BashPath/${projectname}_Package/output/Qnx
    
    # 3. 解压QNX基线代码
    if [[ ! -d $LOCALQNX_SDP_PATH ]] && [[ "${isBuildC1}" = "0" ]]; then
        echo "download sdp"
        tar -vxf /home/<USER>/e-cockpit/qualcomm/HQX1.2.1_R00005.2/sdp700_hqx1_2.tar.gz -C $LOCAL_PATH/
    fi
    
    # 4. QNX许可证注册
    if [[ ! -d $LOCAL_PATH/.qnx/license ]]; then
        echo "qnx sdp license register"
        tar -xf /home/<USER>/e-cockpit/qualcomm/HQX1.0_R00020.1/qnx_sdp_install/sdp_license/qnx700_license/license.tar -C $LOCAL_PATH
    fi
}
```

### 3.2 QNX核心构建流程
```bash
function _build() {
    # 1. 设置QNX构建环境
    cd $LOCALQUA_BSP_PATH/apps/qnx_ap
    source setenv_64.sh --external $LOCALQNX_SDP_PATH
    
    # 2. 清理之前的构建
    make clean
    
    # 3. 执行构建
    if [[ $coverity_scan = "true" ]]; then
        # Coverity代码扫描构建
        ${RA_license}/nuw-config --qnx 
        mkdir -p $Qualcomm8155BashPath/${projectname}_Package/Coverity_Build
        ${RA_license}/nuw-build --dir $Qualcomm8155BashPath/${projectname}_Package/Coverity_Build make all 2>$Qualcomm8155BashPath/${projectname}_Package/QNX_Build_Error_${build_paramNum}.log
        result=$?
        ${RA_license}/nuw-analyze --dir $Qualcomm8155BashPath/${projectname}_Package/Coverity_Build 
        ${RA_license}/nuw-commit-error --dir $Qualcomm8155BashPath/${projectname}_Package/Coverity_Build --host *********** --port 9900 --access-token `cat ${RA_token}` --project $PROJECT_NAME --project-version $PROJECT_VERSION
    else
        # 普通构建
        make all 2>$Qualcomm8155BashPath/${projectname}_Package/QNX_Build_Error_${build_paramNum}.log
        result=$?
    fi
    
    # 4. 构建结果检查
    echo "Build Qnx Over!!!! Make result is "$result
    if [ $result -ne 0 ]; then
        # 错误分析
        buildErrorModule=`cat QNX_Build_Error_${build_paramNum}.log grep -E 'failed|error'`
        if [[ $buildErrorModule =~ .*${ERROR_BUILD_MODULE_MPU_PATH}.* ]]; then
            echo "QNX MPU Module Build Error ....."
            RUNING_ERROR_PARAM=$ERROR_PARAM_BUILD_MPU
            echo $buildErrorModule | awk -F ${ERROR_BUILD_MODULE_MPU_PATH} '{print $2}' | awk -F "/" '{print $3}' > $Qualcomm8155BashPath/${projectname}_Package/QNX_ERROR_MODULE
        elif [[ $buildErrorModule =~ .*${ERROR_BUILD_MODULE_BSP_PATH}.* ]]; then
            echo "QNX BSP Module Build Error ....."
            RUNING_ERROR_PARAM=$ERROR_PARAM_BUILD_BSP
        else
            echo "QNX Other Module Build Error ....."
            RUNING_ERROR_PARAM=$ERROR_PARAM_BUILD_OTHER
        fi
        _build_error_exit $RUNING_ERROR_PARAM
    fi
}
```

### 3.3 安全启动构建流程
```bash
function secure_boot_make() {
    # 1. ADSP构建
    echo "===>secure_boot_make   make adsp ---->>"
    cd $LOCALQUA_AMSS_PATH/adsp_proc/build
    python build.py -c sm8150 -o all -f ADSP 2>$Qualcomm8155BashPath/${projectname}_Package/QNX_Build_Error_${build_paramNum}.log
    result=$?
    if [ $result -ne 0 ]; then
        echo "build adsp ERROR !!!!!!!!"
        _build_error_exit $RUNING_ERROR_PARAM
    fi

    # 2. TZ构建
    echo "===>secure_boot_make   make tz ---->>"
    cd $LOCALQUA_AMSS_PATH/tz_8155/trustzone_images/build/ms
    python build_all.py CHIPSET=sdx55 2>$Qualcomm8155BashPath/${projectname}_Package/QNX_Build_Error_${build_paramNum}.log
    result=$?
    if [ $result -ne 0 ]; then
        echo "build tz ERROR !!!!!!!!"
        _build_error_exit $RUNING_ERROR_PARAM
    fi

    # 3. AOP构建
    echo "===>secure_boot_make   make aop ---->>"
    cd $LOCALQUA_AMSS_PATH/aop_8155/aop_proc/build
    ./build_855au.sh 2>$Qualcomm8155BashPath/${projectname}_Package/QNX_Build_Error_${build_paramNum}.log
    result=$?
    if [ $result -ne 0 ]; then
        echo "build aop ERROR !!!!!!!!"
        _build_error_exit $RUNING_ERROR_PARAM
    fi

    # 4. QNX构建
    echo "===>secure_boot_make   make qnx 2 ---->>"
    cd $LOCALQUA_BSP_PATH/apps/qnx_ap
    source setenv_64.sh --external $LOCALQNX_SDP_PATH
    make clean
    make all 2>$Qualcomm8155BashPath/${projectname}_Package/QNX_Build_Error_${build_paramNum}.log
    result=$?
    if [ $result -ne 0 ]; then
        # 错误处理逻辑同普通构建
        _build_error_exit $RUNING_ERROR_PARAM
    fi
}
```

## 🔍 4. 成果物校验和验证

### 4.1 构建成果物路径
```bash
# QNX构建输出路径
QNX_OUTPUT_PATH="$LOCALQUA_BSP_PATH/apps/qnx_ap/target/hypervisor/host/out_8155"

# 主要成果物文件
QNX_UPDATE_PATH="$QNX_OUTPUT_PATH/update/$projectversion"
QNX_IMAGES=(
    "system_la.img"
    "ifs2_la.img"
    "mifs_hyp_la.img"
    "share.img"
)

# AMSS构建输出路径
AMSS_OUTPUT_PATH="$LOCALQUA_AMSS_PATH/$projectversion"
AMSS_IMAGES=(
    "NON-HLOS.bin"
    "tz.mbn"
    "devcfg_auto.mbn"
    "aop.mbn"
)
```

### 4.2 文件完整性检查
```bash
function validate_build_outputs() {
    # 检查QNX构建输出
    if [[ ! -d $QNX_UPDATE_PATH ]]; then
        echo "ERROR: QNX update directory not found: $QNX_UPDATE_PATH"
        return 1
    fi

    # 检查关键镜像文件
    for img in "${QNX_IMAGES[@]}"; do
        if [[ ! -f "$QNX_OUTPUT_PATH/$img" ]]; then
            echo "WARNING: QNX image not found: $img"
        else
            echo "Found QNX image: $img ($(stat -c%s "$QNX_OUTPUT_PATH/$img") bytes)"
        fi
    done

    # 检查AMSS构建输出
    for img in "${AMSS_IMAGES[@]}"; do
        if [[ ! -f "$AMSS_OUTPUT_PATH/$img" ]]; then
            echo "WARNING: AMSS image not found: $img"
        else
            echo "Found AMSS image: $img ($(stat -c%s "$AMSS_OUTPUT_PATH/$img") bytes)"
        fi
    done
}
```

## 📦 5. 打包和输出处理

### 5.1 QNX打包流程
```bash
function package_qnx_outputs() {
    # 1. 进入QNX更新目录
    cd $LOCALQUA_BSP_PATH/apps/qnx_ap/target/hypervisor/host/out_8155/update/$projectversion

    # 2. 创建QNX.tar.gz压缩包
    RUNING_ERROR_PARAM=$ERROR_PARAM_PACKAGE
    tar -czf QNX.tar.gz *
    if [[ $? -ne 0 ]]; then
        _build_error_exit $RUNING_ERROR_PARAM
    fi

    # 3. 复制到打包目录
    cp $LOCALQUA_BSP_PATH/apps/qnx_ap/target/hypervisor/host/out_8155/update/$projectversion/QNX.tar.gz $Qualcomm8155BashPath/${projectname}_Package/Update

    # 4. 同步镜像文件到Image目录
    rsync -rv --exclude='QNX.tar.gz' $LOCALQUA_BSP_PATH/apps/qnx_ap/target/hypervisor/host/out_8155/update/$projectversion/* $Qualcomm8155BashPath/${projectname}_Package/Image/Qnx

    # 5. 特定项目的OTA处理
    if [[ $projectname =~ "HS7012A" ]] || [[ $projectname =~ "HS7023A" ]] || [[ $projectname =~ "HS7029A" ]]; then
        rsync -rv --exclude='QNX.tar.gz' $LOCALQUA_BSP_PATH/apps/qnx_ap/target/hypervisor/host/out_8155/update/$projectversion/* $Qualcomm8155BashPath/${projectname}_Package/OTA/Qnx
    fi
}
```

### 5.2 AMSS打包流程
```bash
function package_amss_outputs() {
    # 1. 进入AMSS输出目录
    cd $LOCALQUA_AMSS_PATH/$projectversion

    # 2. 创建AMSS.tar.gz压缩包
    tar -czf AMSS.tar.gz *

    # 3. 复制到打包目录
    cp $LOCALQUA_AMSS_PATH/$projectversion/AMSS.tar.gz $Qualcomm8155BashPath/${projectname}_Package/Update

    # 4. 同步镜像文件到Image目录
    rsync -rv --exclude='AMSS.tar.gz' $LOCALQUA_AMSS_PATH/$projectversion/* $Qualcomm8155BashPath/${projectname}_Package/Image/AMSS

    # 5. 特定项目的OTA处理
    if [[ $projectname =~ "HS7012A" ]] || [[ $projectname =~ "HS7023A" ]] || [[ $projectname =~ "HS7029A" ]]; then
        cp -f $LOCALQUA_AMSS_PATH/$projectversion/NON-HLOS.bin $Qualcomm8155BashPath/${projectname}_Package/OTA/AMSS
        cp -f $LOCALQUA_BSP_PATH/apps/qnx_ap/target/hypervisor/host/out_8155/share.img $Qualcomm8155BashPath/${projectname}_Package/Image/Qnx
    fi
}
```

### 5.3 符号文件输出
```bash
function _output_install() {
    cd $LOCALQUA_BSP_PATH/apps/qnx_ap

    # 复制调试符号文件到输出目录
    cp -f ./target/hypervisor/host/out_8155/el2-save-restore.qvmhost_hyp_la.sym $Qualcomm8155BashPath/${projectname}_Package/output/Qnx
    cp -f ./target/hypervisor/host/out_8155/procnto-smp-instr.qvmhost_hyp_la.sym $Qualcomm8155BashPath/${projectname}_Package/output/Qnx
    cp -f ./target/hypervisor/host/out_8155/startup-sdx.qvmhost_hyp_la.sym $Qualcomm8155BashPath/${projectname}_Package/output/Qnx
}
```

## 🚨 6. 错误处理和诊断

### 6.1 构建错误分类
```bash
# 错误参数定义
ERROR_PARAM_BUILD_MPU=51    # MPU模块构建错误
ERROR_PARAM_BUILD_BSP=52    # BSP模块构建错误
ERROR_PARAM_BUILD_OTHER=53  # 其他模块构建错误
ERROR_PARAM_PACKAGE=54      # 打包错误

# 错误模块路径定义
ERROR_BUILD_MODULE_MPU_PATH="apps/qnx_ap/src/mpu"
ERROR_BUILD_MODULE_BSP_PATH="apps/qnx_ap/src/services"
```

### 6.2 错误分析逻辑
```bash
function analyze_build_error() {
    buildErrorModule=`cat QNX_Build_Error_${build_paramNum}.log | grep -E 'failed|error'`

    if [[ $buildErrorModule =~ .*${ERROR_BUILD_MODULE_MPU_PATH}.* ]]; then
        echo "QNX MPU Module Build Error ....."
        RUNING_ERROR_PARAM=$ERROR_PARAM_BUILD_MPU
        # 提取具体错误模块
        echo $buildErrorModule | awk -F ${ERROR_BUILD_MODULE_MPU_PATH} '{print $2}' | awk -F "/" '{print $3}' > $Qualcomm8155BashPath/${projectname}_Package/QNX_ERROR_MODULE
    elif [[ $buildErrorModule =~ .*${ERROR_BUILD_MODULE_BSP_PATH}.* ]]; then
        echo "QNX BSP Module Build Error ....."
        RUNING_ERROR_PARAM=$ERROR_PARAM_BUILD_BSP
    else
        echo "QNX Other Module Build Error ....."
        RUNING_ERROR_PARAM=$ERROR_PARAM_BUILD_OTHER
    fi

    _build_error_exit $RUNING_ERROR_PARAM
}
```

### 6.3 错误退出处理
```bash
function _build_error_exit() {
    error_code=$1

    # 记录错误信息
    echo "Build failed with error code: $error_code"
    echo "Error timestamp: $(date)"

    # 清理临时文件
    if [[ -d $LOCAL_PATH/temDir ]]; then
        rm -rf $LOCAL_PATH/temDir
    fi

    # 退出并返回错误码
    exit $error_code
}
```

## 📁 7. 关键路径配置

### 7.1 基础路径配置
```bash
# 工作根目录
Qualcomm8155BashPath=/mnt/home/<USER>/e-cockpit/qnx-android/jenkins_qnx-android_Qualcomm8155

# 本地构建路径
LOCAL_PATH=$Qualcomm8155BashPath/Qualcomm8155Pool${build_paramNum}/${QualcommPathName}

# QNX相关路径
LOCALQNX_SDP_PATH=$LOCAL_PATH/qnx_sdp
LOCALQUA_BSP_PATH=$LOCAL_PATH/qnx_bsp
LOCALQUA_AMSS_PATH=$LOCAL_PATH/amss

# 输出路径
BuildOutputPath=/mnt/BU2_NAS/Jenkins_output/SA8155
```

### 7.2 源码基线路径
```bash
# QNX基线代码路径
QualcommPathName="hqx1.2.1.c1_r00004.2"
QualcommLastPathName="HQX1.2.1_R00005.2"

# 基线代码存储路径
BASE_CODE_PATH="/home/<USER>/e-cockpit/qualcomm/$QualcommLastPathName/$QualcommPathName.tar.gz"

# SDP路径
SDP_PATH="/home/<USER>/e-cockpit/qualcomm/HQX1.2.1_R00005.2/sdp700_hqx1_2.tar.gz"

# 许可证路径
LICENSE_PATH="/home/<USER>/e-cockpit/qualcomm/HQX1.0_R00020.1/qnx_sdp_install/sdp_license/qnx700_license/license.tar"
```

## 📊 8. 构建流程总结

### 8.1 完整构建时序
```
1. 环境准备 (0-5分钟)
   ├── 清理旧构建输出
   ├── 创建目录结构
   ├── 解压QNX基线代码
   ├── 解压SDP (如需要)
   └── 注册QNX许可证

2. 源码获取 (5-15分钟)
   ├── 读取XML配置
   ├── 解析源码路径配置
   ├── SVN导出各模块源码
   └── 处理MPU模块特殊逻辑

3. 构建执行 (15-90分钟)
   ├── 设置QNX构建环境
   ├── 执行make clean
   ├── 执行make all
   └── 安全启动构建 (可选)
       ├── ADSP构建
       ├── TZ构建
       ├── AOP构建
       └── QNX构建

4. 成果物处理 (90-100分钟)
   ├── 验证构建输出
   ├── 打包QNX镜像
   ├── 打包AMSS镜像
   └── 输出符号文件

5. 错误处理 (如需要)
   ├── 分析错误类型
   ├── 记录错误信息
   └── 清理和退出
```

### 8.2 关键特性
- **模块化设计**: 支持MPU、BSP、AMSS等模块独立构建
- **错误分类**: 详细的错误分析和分类机制
- **灵活配置**: 基于XML的配置驱动构建
- **安全启动**: 支持安全启动模式的完整构建流程
- **成果物管理**: 完善的打包和输出管理机制

### 8.3 构建产物
- **QNX.tar.gz**: QNX系统完整镜像包
- **AMSS.tar.gz**: AMSS固件完整包
- **符号文件**: 用于调试的符号文件集合
- **镜像文件**: 各种.img和.bin格式的镜像文件
```
