#! /bin/bash 
#****************************
# OTA����ű�
# OTA�������ű�
# Authot xizhuang.wu
# version 1.0
#****************************

LOCAL_PATH=""
Project_Name=$1

LOCAL_AILABI_PATH=/mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Ailabi_Package
PACKAGE_PATH=/mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/${Project_Name}_Package
SVN_GEMENAL_XML_PATH="http://10.2.4.101/svn/Qualcomm/02_SA8155P/01_HS7001A/02_CodeLib/01_MainPath/1.2/03_Config/AilabiConfig"
SVN_BASE_PROJECT_XML_PATH="http://10.2.4.101/svn/Qualcomm/02_SA8155P"/${Project_Name%%_*}/"02_CodeLib/01_ProjectPath/03_Config/AilabiConfig"
SVN_PROJECT_XML_PATH="http://10.2.4.101/svn/Qualcomm/02_SA8155P"/${Project_Name}/"02_CodeLib/01_ProjectPath/03_Config/AilabiConfig"

AilabiAndroidXml=partition_delta_android_${Project_Name}.xml
AilabiQnxXml=partition_delta_qnx_${Project_Name}.xml

ERROR_PARAM_AILABI_CONFIG=21
ERROR_PARAM_AILABI_ANDROID=22
ERROR_PARAM_AILABI_QNX=23

RUNING_ERROR_PARAM=0

_build_error_exit ()
{
    EXIT_ERROR_PARAM=$1
	echo "OTA build Error,Error param is $EXIT_ERROR_PARAM,exit ........"
	exit $EXIT_ERROR_PARAM
}

_build_ota_package()
{
    RUNING_ERROR_PARAM=$ERROR_PARAM_AILABI_CONFIG
    cd $LOCAL_AILABI_PATH
	if [[ ! -d $LOCAL_AILABI_PATH/WholeUpdateZip/${Project_Name} ]];then
	    mkdir -p $LOCAL_AILABI_PATH/WholeUpdateZip/${Project_Name}
	fi
	
	if [[ ! -d $LOCAL_AILABI_PATH/AilabiXmlConfig ]];then
	    mkdir -p $LOCAL_AILABI_PATH/AilabiXmlConfig
	fi
	
	if [[ -d $PACKAGE_PATH/AilabiConfig ]];then
	    rm -rf $PACKAGE_PATH/AilabiConfig
		mkdir -p $PACKAGE_PATH/AilabiConfig
	fi
	
	if [[ -d $PACKAGE_PATH/AilabiExe ]];then
	    rm -rf $PACKAGE_PATH/AilabiExe
	fi
	mkdir -p $PACKAGE_PATH/AilabiExe
	
	#��ȡ�����������ļ�
	svn --force export $SVN_GEMENAL_XML_PATH $PACKAGE_PATH/AilabiConfig --username gjzhao --password gjz123 --no-auth-cache --trust-server-cert
	svn --force export $SVN_BASE_PROJECT_XML_PATH $PACKAGE_PATH/AilabiConfig --username gjzhao --password gjz123 --no-auth-cache --trust-server-cert
	svn --force export $SVN_PROJECT_XML_PATH $PACKAGE_PATH/AilabiConfig --username gjzhao --password gjz123 --no-auth-cache --trust-server-cert
	
	if [[ ! -f $PACKAGE_PATH/AilabiConfig/partition_delta_android_genenal.xml ]] || [[ ! -f $PACKAGE_PATH/AilabiConfig/partition_delta_qnx_genenal.xml ]];then
		echo "buildOtaPackage svn --force export AilabiConfig Error!!!! exit $RUNING_ERROR_PARAM"
		_build_error_exit $RUNING_ERROR_PARAM
	else
		cp -f $PACKAGE_PATH/AilabiConfig/* $LOCAL_AILABI_PATH/AilabiXmlConfig
		sed -i "s/ReplaceZone/"$Project_Name"_Package/" $LOCAL_AILABI_PATH/AilabiXmlConfig/partition_delta_android_genenal.xml
		sed -i "s/ReplaceZone/"$Project_Name"_Package/" $LOCAL_AILABI_PATH/AilabiXmlConfig/partition_delta_qnx_genenal.xml
		cp -f $LOCAL_AILABI_PATH/AilabiXmlConfig/partition_delta_android_genenal.xml $LOCAL_AILABI_PATH/AilabiXmlConfig/${AilabiAndroidXml}
		cp -f $LOCAL_AILABI_PATH/AilabiXmlConfig/partition_delta_qnx_genenal.xml $LOCAL_AILABI_PATH/AilabiXmlConfig/${AilabiQnxXml}
	fi
	
	if [[ -d $LOCAL_AILABI_PATH/WholeUpdateZip/${Project_Name} ]];then
	    rm -rf $LOCAL_AILABI_PATH/WholeUpdateZip/${Project_Name}/*
	fi
	
	#��ȡ�����ȴ������
	AilabiExePath=`$PACKAGE_PATH/packShell/readXMLFile.sh Qnx ${Project_Name} Tools Ailabi`
	echo "project path: $AilabiExePath"
	svn --force export "$AilabiExePath" $PACKAGE_PATH/AilabiExe --username gjzhao --password gjz123 --no-auth-cache --trust-server-cert
	if [[ $? -ne 0 ]];then
		echo "use scp17 default mkotapackage"
	else
		if [[ -f $PACKAGE_PATH/AilabiExe/mkotapackage ]];then
			echo "use project mkotapackage"
			cd $PACKAGE_PATH/AilabiExe
			chmod 777 mkotapackage
		else
			echo "use scp17 default mkotapackage"
		fi
	fi
	
	RUNING_ERROR_PARAM=$ERROR_PARAM_AILABI_ANDROID
	sudo ./mkotapackage -c $LOCAL_AILABI_PATH/AilabiXmlConfig/${AilabiAndroidXml} -o $LOCAL_AILABI_PATH/WholeUpdateZip/${Project_Name}/AilabiAndroidWholeUpdate.zip
	if [[ $? -ne 0 ]];then
	    echo "Make Android Ailabi Package Error!!!! exit $RUNING_ERROR_PARAM"
		df -h /tmp
	    _build_error_exit $RUNING_ERROR_PARAM
	fi
	
	RUNING_ERROR_PARAM=$ERROR_PARAM_AILABI_QNX
	sudo ./mkotapackage -c $LOCAL_AILABI_PATH/AilabiXmlConfig/${AilabiQnxXml} -o $LOCAL_AILABI_PATH/WholeUpdateZip/${Project_Name}/AilabiQnxWholeUpdate.zip
	if [[ $? -ne 0 ]];then
	    echo "Make Qnx Ailabi Package Error!!!! exit $RUNING_ERROR_PARAM"
		df -h /tmp
		_build_error_exit $RUNING_ERROR_PARAM
	fi
	
	echo "Package OTA Img Success !!!!!"
}

<<delect_fun
_make_android_ota()
{
    echo "Begin build Ota !!!!!"
    mkdir -p $LOCAL_PATH/hqx1.2.1.c1_r00004.2/android/device/qcom/msmnile_gvmq/radio

    cp -f $LOCAL_PATH/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/NON-HLOS.bin  $LOCAL_PATH/hqx1.2.1.c1_r00004.2/android/device/qcom/msmnile_gvmq/radio/modem.img
    cp -f $LOCAL_PATH/hqx1.2.1.c1_r00004.2/amss/common/build/ufs/8155_la/bin/BTFM.bin  $LOCAL_PATH/hqx1.2.1.c1_r00004.2/android/device/qcom/msmnile_gvmq/radio/bluetooth.img
    cp -f $LOCAL_PATH/hqx1.2.1.c1_r00004.2/amss/common/build/bin/8155_la/dspso.bin  $LOCAL_PATH/hqx1.2.1.c1_r00004.2/android/device/qcom/msmnile_gvmq/radio/dsp.img
    cp -f $LOCAL_PATH/hqx1.2.1.c1_r00004.2/amss/common/core_qupv3fw/sdm855/qupv3fw.elf  $LOCAL_PATH/hqx1.2.1.c1_r00004.2/android/device/qcom/msmnile_gvmq/radio/qupfw.img
    cp -f $LOCAL_PATH/hqx1.2.1.c1_r00004.2/amss/aop_8155/aop_proc/build/ms/bin/AAAAANAZO/aop.mbn  $LOCAL_PATH/hqx1.2.1.c1_r00004.2/android/device/qcom/msmnile_gvmq/radio/aop.img
    cp -f $LOCAL_PATH/hqx1.2.1.c1_r00004.2/amss/tz_8155/trustzone_images/build/ms/bin/YAQAANAA/cmnlib64.mbn $LOCAL_PATH/hqx1.2.1.c1_r00004.2/android/device/qcom/msmnile_gvmq/radio/cmnlib64.img
    cp -f $LOCAL_PATH/hqx1.2.1.c1_r00004.2/amss/tz_8155/trustzone_images/build/ms/bin/YAQAANAA/cmnlib.mbn $LOCAL_PATH/hqx1.2.1.c1_r00004.2/android/device/qcom/msmnile_gvmq/radio/cmnlib.img
    cp -f $LOCAL_PATH/hqx1.2.1.c1_r00004.2/amss/tz_8155/trustzone_images/build/ms/bin/YAQAANAA/devcfg_auto.mbn $LOCAL_PATH/hqx1.2.1.c1_r00004.2/android/device/qcom/msmnile_gvmq/radio/devcfg.img
    cp -f $LOCAL_PATH/hqx1.2.1.c1_r00004.2/amss/tz_8155/trustzone_images/build/ms/bin/YAQAANAA/hyp.mbn $LOCAL_PATH/hqx1.2.1.c1_r00004.2/android/device/qcom/msmnile_gvmq/radio/hyp.img
    cp -f $LOCAL_PATH/hqx1.2.1.c1_r00004.2/amss/tz_8155/trustzone_images/build/ms/bin/YAQAANAA/km4.mbn $LOCAL_PATH/hqx1.2.1.c1_r00004.2/android/device/qcom/msmnile_gvmq/radio/keymaster.img
    cp -f $LOCAL_PATH/hqx1.2.1.c1_r00004.2/amss/tz_8155/trustzone_images/build/ms/bin/YAQAANAA/tz.mbn  $LOCAL_PATH/hqx1.2.1.c1_r00004.2/android/device/qcom/msmnile_gvmq/radio/tz.img
    cp -f $LOCAL_PATH/hqx1.2.1.c1_r00004.2/amss/tz_8155/trustzone_images/build/ms/bin/YAQAANAA/uefi_sec.mbn  $LOCAL_PATH/hqx1.2.1.c1_r00004.2/android/device/qcom/msmnile_gvmq/radio/uefisecapp.img
    cp -f $LOCAL_PATH/hqx1.2.1.c1_r00004.2/amss/boot_8155/boot_images/QcomPkg/SDMPkg/855/Bin/AU/RELEASE/xbl.elf $LOCAL_PATH/hqx1.2.1.c1_r00004.2/android/device/qcom/msmnile_gvmq/radio/xbl.img
    cp -f $LOCAL_PATH/hqx1.2.1.c1_r00004.2/amss/boot_8155/boot_images/QcomPkg/SDMPkg/855/Bin/AU/RELEASE/xbl_config.elf $LOCAL_PATH/hqx1.2.1.c1_r00004.2/android/device/qcom/msmnile_gvmq/radio/xbl_config.img

	cp -f $LOCAL_PATH/hqx1.2.1.c1_r00004.2/android/out/target/product/msmnile_gvmq/abl.elf $LOCAL_PATH/hqx1.2.1.c1_r00004.2/android/device/qcom/msmnile_gvmq/radio/abl.img
    cp -f $LOCAL_PATH/hqx1.2.1.c1_r00004.2/android/out/target/product/msmnile_gvmq/{boot.img,dtbo.img,vbmeta.img,system.img,vendor.img} $LOCAL_PATH/hqx1.2.1.c1_r00004.2/android/device/qcom/msmnile_gvmq/radio/

    cd $LOCAL_PATH/hqx1.2.1.c1_r00004.2/android
    source build/envsetup.sh
    lunch msmnile_gvmq-userdebug
	make clean

	find ./prebuilts/abi-dumps/vndk/30 -name libevent.so.lsdump -delete
	./development/vndk/tools/header-checker/utils/create_reference_dumps.py  -l libbinder -product msmnile_gvmq
    make dist AB_OTA_PARTITIONS="abl aop bluetooth boot cmnlib64 cmnlib devcfg dsp dtbo hyp keymaster modem qupfw system tz uefisecapp vbmeta vendor xbl_config xbl" -j8
    if [[ $? -ne 0 ]];then
	   echo "build ota Error !!!!! exit 2"
	   exit 2
	fi
	
	echo "Make OTA Over !!!!"
}
delect_fun

#_make_android_ota

_build_ota_package