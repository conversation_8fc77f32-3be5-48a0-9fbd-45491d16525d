OEM signed image with RSAPSS
QTI PROD signed image with RSAPSS
ERROR: Following validations failed for the image:
       1. Following signing attributes do not match: 
          | Attribute | Image | Config |
          |-----------|-------|--------|
          |   debug   |  0x1  |  0x0   |
          
Image /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/common/core_qupv3fw/sdm855/qupv3fw.elf is not encrypted

Base Properties: 
| Integrity Check             | True  |
| Signed                      | True  |
| Encrypted                   | False |
| Size of signature           | 256   |
| Size of one cert            | 2048  |
| Num of certs in cert chain  | 3     |
| Number of root certs        | 1     |
| Maximum number of root certs| 4     |
| Cert chain size             | 12288 |

ELF Properties: 
Header: 
| Magic                      | ELF                           |
| Class                      | ELF32                          |
| Data                       | 2's complement, little endian  |
| Version                    | 1 (Current)                    |
| OS/ABI                     | No extensions or unspecified   |
| ABI Version                | 0                              |
| Type                       | EXEC (Executable file)         |
| Machine                    | 164                            |
| Version                    | 0x1                            |
| Entry address              | 0x00000000                     |
| Program headers offset     | 0x00000034                     |
| Section headers offset     | 0x00000000                     |
| Flags                      | 0x00000003                     |
| ELF header size            | 52                             |
| Program headers size       | 32                             |
| Number of program headers  | 13                             |
| Section headers size       | 0                              |
| Number of section headers  | 0                              |
| String table section index | 0                              |

Program Headers: 
| Num | Type | Offset | VirtAddr | PhysAddr | FileSize | MemSize | Flags | Align |
|-----|------|--------|----------|----------|----------|---------|-------|-------|
|  1  | LOAD |0x06000 |  0x1000  |  0x1000  |  0x3070  |  0x3070 |  RWE  | 0x8   |
|  2  | LOAD |0x09070 |  0x4070  |  0x4070  |  0x0622  |  0x0622 |  RWE  | 0x8   |
|  3  | LOAD |0x09698 |  0x4698  |  0x4698  |  0x1156  |  0x1156 |  RWE  | 0x8   |
|  4  | LOAD |0x0a7f0 |  0x57f0  |  0x57f0  |  0x08d2  |  0x08d2 |  RWE  | 0x8   |
|  5  | LOAD |0x0b0c8 |  0x60c8  |  0x60c8  |  0x02fa  |  0x02fa |  RWE  | 0x8   |
|  6  | LOAD |0x0b3c8 |  0x63c8  |  0x63c8  |  0x0746  |  0x0746 |  RWE  | 0x8   |
|  7  | LOAD |0x0bb10 |  0x6b10  |  0x6b10  |  0x3070  |  0x3070 |  RWE  | 0x8   |
|  8  | LOAD |0x0eb80 |  0x9b80  |  0x9b80  |  0x057b  |  0x057b |  RWE  | 0x8   |
|  9  | LOAD |0x0f100 |  0xa100  |  0xa100  |  0x0622  |  0x0622 |  RWE  | 0x8   |
|  10 | LOAD |0x0f728 |  0xa728  |  0xa728  |  0x1156  |  0x1156 |  RWE  | 0x8   |
|  11 | LOAD |0x10880 |  0xb880  |  0xb880  |  0x08d7  |  0x08d7 |  RWE  | 0x8   |
|  12 | LOAD |0x11158 |  0xc158  |  0xc158  |  0x02d2  |  0x02d2 |  RWE  | 0x8   |
|  13 | LOAD |0x11430 |  0xc430  |  0xc430  |  0x0746  |  0x0746 |  RWE  | 0x8   |

Hash Segment Properties: 
| Header Size     | 288B  |
| Hash Algorithm  | sha384|

Header: 
| cert_chain_ptr              | 0xffffffff  |
| cert_chain_size             | 0x00003000  |
| cert_chain_size_qti         | 0x00001800  |
| code_size                   | 0x000002d0  |
| image_id                    | 0x00000000  |
| image_size                  | 0x00004dd0  |
| metadata_major_version      | 0x00000000  |
| metadata_major_version_qti  | 0x00000000  |
| metadata_minor_version      | 0x00000000  |
| metadata_minor_version_qti  | 0x00000000  |
| metadata_size               | 0x00000078  |
| metadata_size_qti           | 0x00000078  |
| sig_ptr                     | 0xffffffff  |
| sig_size                    | 0x00000100  |
| sig_size_qti                | 0x00000100  |
| version                     | 0x00000006  |
Metadata:
| anti_rollback_version        | 0x00000000  |
| app_id                       | 0x00000000  |
| debug                        | 0x00000001  |
| hw_id                        | 0x000a50e1  |
| in_use_soc_hw_version        | 0x00000001  |
| model_id                     | 0x00000000  |
| mrc_index                    | 0x00000000  |
| multi_serial_numbers         | 0x00000000  |
| oem_id                       | 0x00000001  |
| oem_id_independent           | 0x00000001  |
| root_revoke_activate_enable  | 0x00000000  |
| rot_en                       | 0x00000000  |
| soc_vers                     | 0x00006003  |
| sw_id                        | 0x00000024  |
| uie_key_switch_enable        | 0x00000000  |
| use_serial_number_in_signing | 0x00000000  |
Metadata QTI:
| anti_rollback_version        | 0x00000000  |
| app_id                       | 0x00000000  |
| debug                        | 0x00000001  |
| hw_id                        | 0x000a50e1  |
| in_use_soc_hw_version        | 0x00000001  |
| model_id                     | 0x00000000  |
| mrc_index                    | 0x00000000  |
| multi_serial_numbers         | 0x00000000  |
| oem_id                       | 0x00000001  |
| oem_id_independent           | 0x00000001  |
| root_revoke_activate_enable  | 0x00000000  |
| rot_en                       | 0x00000000  |
| soc_vers                     | 0x00006003  |
| sw_id                        | 0x00000024  |
| uie_key_switch_enable        | 0x00000000  |
| use_serial_number_in_signing | 0x00000000  |


