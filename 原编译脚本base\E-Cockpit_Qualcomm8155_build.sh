#!/bin/bash

UserName=`whoami`
USERNAME="zlizhang"
PASSWORD="zlz123"
Shell_Svn_Path=""
Qualcomm8155BashPath=/mnt/home/<USER>/e-cockpit/qnx-android/jenkins_qnx-android_Qualcomm8155
BuildDoquePath=/mnt/home/<USER>/e-cockpit/qnx-android/jenkins_qnx-android_Qualcomm8155/startShell

BuildOutputPath=/mnt/BU2_NAS/Jenkins_output/SA8155

export JENKINS_SW1_NAME=$8
export JENKINS_SW2_NAME=$9
export JENKINS_SW3_NAME=${10}
export JENKINS_SVN_TAG_PATH=$4
export JENKINS_ISNEED_COV_BUILD=$7
# export JENKINS_BUILD_ID=${14}
# export JENKINS_SVN_INT_VERSION=${15}

if [ "$JENKINS_SVN_INT_VERSION" == "" ];then
	JENKINS_SVN_INT_VERSION="V0.00.01"
fi

JfrogpathProject=$(echo "${JENKINS_PRJNAME%_*}" | tr '[:upper:]' '[:lower:]')
Jfrogpath=$JfrogpathProject-adayo-update
WareHouse="https://swjfrog151.adayoge.com:8082/ui/repos/tree/General/$Jfrogpath"

#设置编译池数量，由于服务器没有足够可用线程导致报错，将编译池由4减小为3
PoolNum=3
#设置排队数量
WaitPoolNum=9
WaitFlag=0
PoolCount=-1
memoryTip=0

AndroidResum=0
QnxResum=0
EmailResum=0
UFSResum=0
OTAResum=0

PIDResum=0
PACResum=0
InspectResum=0

#判断是否需要打包升级包    0 编译成功，可以打包    1 安卓编译报错   2 QNX编译报错    3 QNX安卓都编译报错    4 艾拉比打包错误
PackageResum=0

#判断是否将包上传至JFROG系统 0:不需要上传FROG系统  1:上传FROG系统成功  2:上传失败 3:编译失败
JFROGResum=0

#Jenkins上输入参数检查 0:参数无误；8：版本号中包含空格
JenkinsInputCheck=0

EmailSendStr=""
WaitDoqueStr=""
RunDoqueStr=""

SUBMIT_FLAG=""
BUILD_DATE=$(date +%m-%d-%Y)
PACKAGE_BUILD_DATE=$(date +%Y%m%d)
#Out6W6CDir=${JENKINS_PRJNAME}_${JENKINS_SVN_VERSION}_${BUILD_DATE}_${RANDOM}
Out6W6CDir=${JENKINS_PRJNAME}_${JENKINS_SVN_VERSION}_${BUILD_DATE}_${JENKINS_BUILD_NUMBER}
if [[ ${JENKINS_BUILD_SECURE} == "1" ]];then
	Out6W6CDir=${Out6W6CDir}_safety
fi
CollectEmailFilePath="/mnt/home/<USER>/e-cockpit/qnx-android/jenkins_qnx-android_Qualcomm8155/CollectBuild"

QNX_Error_Module=""
DateRegularMax=2     #设定的最大预约天数（超出此范围定时编译任务将不生效）

androidSubPid=-1
qnxSubPid=-1

if [ $UserName != "Jenkins" ];then
	SUBMIT_FLAG="--username $USERNAME --password $PASSWORD --no-auth-cache"
fi

_print(){
	echo -e "\033[32m $@ \033[0m"	
}

write_flag ()
{
    writeValue=$1
	writeFile=$2
	echo "$writeValue to $Qualcomm8155BashPath/${JENKINS_PRJNAME}_Package/RunningFlag_${JENKINS_BUILD_NUMBER}/$writeFile"
	if [[ ! -d $Qualcomm8155BashPath/${JENKINS_PRJNAME}_Package/RunningFlag_${JENKINS_BUILD_NUMBER} ]];then mkdir -p $Qualcomm8155BashPath/${JENKINS_PRJNAME}_Package/RunningFlag_${JENKINS_BUILD_NUMBER};fi
	echo $writeValue > $Qualcomm8155BashPath/${JENKINS_PRJNAME}_Package/RunningFlag_${JENKINS_BUILD_NUMBER}/$writeFile
}

read_flag ()
{
    readFile=$1
	cat $Qualcomm8155BashPath/${JENKINS_PRJNAME}_Package/RunningFlag_${JENKINS_BUILD_NUMBER}/$readFile
}

init_flag ()
{
    echo "Begin Init Build Flag !!!!!!!"
    write_flag 0 AndroidResum
	write_flag 0 QnxResum
	write_flag 0 PackageResum
	write_flag 0 EmailResum
	write_flag 0 UFSResum
	write_flag 0 OTAResum
	write_flag 0 InspectResum
	write_flag 0 asyncBuildFlag
	write_flag -1 AndroidPidResum
	write_flag -1 QnxPidResum
	write_flag 0 JFROGResum
}

#中断检查
drop_out_inspect ()
{
    PackageResum=`read_flag PackageResum`
	startBuildFlag=`read_flag startBuildFlag`
	InspectResum=`read_flag InspectResum`
	
	#中断
	if [[ $startBuildFlag -ne 0 ]];then
	    if [[ `ls $Qualcomm8155BashPath/${JENKINS_PRJNAME}_Package/Image/Android -l | grep "^-" | wc -l` -le 0 ]];then
			write_flag 1 PackageResum
			write_flag 30 InspectResum
	    fi
		
		PackageResum=`read_flag PackageResum`
	    if [[ `ls $Qualcomm8155BashPath/${JENKINS_PRJNAME}_Package/Image/Qnx -l | grep "^-" | wc -l` -le 0 ]];then
			write_flag 30 InspectResum
			if [[ $PackageResum -eq 1 ]];then
			    write_flag 3 PackageResum
		    else
			    write_flag 2 PackageResum
		    fi
	    fi
	else
	    if [[ `ls $Qualcomm8155BashPath/${JENKINS_PRJNAME}_Package/Image/Android -l | grep "^-" | wc -l` -le 0 ]] && [[ $JENKINS_SW1_NAME = "1" ]];then
			write_flag 1 PackageResum
			write_flag 31 InspectResum
	    fi
		
		if [[ `ls $Qualcomm8155BashPath/${JENKINS_PRJNAME}_Package/Image/Qnx -l | grep "^-" | wc -l` -le 0 ]] && [[ $JENKINS_SW1_NAME = "2" ]];then
			write_flag 2 PackageResum
			write_flag 32 InspectResum
	    fi
	fi
}

build_process_exit ()
{
    echo "当前启动的编译进程即将结束........."
	
	AndroidResum=`read_flag AndroidResum`
	QnxResum=`read_flag QnxResum`
	PackageResum=`read_flag PackageResum`
	EmailResum=`read_flag EmailResum`
	UFSResum=`read_flag UFSResum`
	OTAResum=`read_flag OTAResum`
	
	upload_to_jfrog
	JFROGResum=`read_flag JFROGResum`
	
	send_email
	collect_build_n
	echo "Build Process is over ,Result: AndroidResum = $AndroidResum QnxResum = $QnxResum PackageResum = $PackageResum EmailResum = $EmailResum UFSResum = $UFSResum OTAResum = $OTAResum JFROGResum = $JFROGResum"
	if [[ -e $Qualcomm8155BashPath/${JENKINS_PRJNAME}_Package/RunningFlag_${JENKINS_BUILD_NUMBER} ]];then
		rm -rf $Qualcomm8155BashPath/${JENKINS_PRJNAME}_Package/RunningFlag_${JENKINS_BUILD_NUMBER}
	fi
	
	# if [[ $EmailResum -eq 0 ]];then
		# if [[ -d $Qualcomm8155BashPath/${JENKINS_PRJNAME}_Package ]];then
			# find $Qualcomm8155BashPath/${JENKINS_PRJNAME}_Package/* -maxdepth 0 ! -name "*packShell*" ! -name "*RunningFlag*" -exec rm -rf {} +
			# echo "build successd ! clean ${JENKINS_PRJNAME}_Package "
		# fi
	# fi
	
	date +%F" "%T
	exit $EmailResum
}

send_email ()
{
    echo "Begin Send Email ........."
	if [[ -f $CollectEmailFilePath/SendCollectEmail ]];then
	    buildCollectMessage="0"   #暂时关闭此功能
	else
	    buildCollectMessage="0"
	fi
	
	WaitDoqueStr=`tr -s "\n" "   " < $BuildDoquePath/waitDoqueFile`
	RunDoqueStr=`tr -s "\n" "   " < $BuildDoquePath/runDoqueFile`
	EmailResum=`read_flag EmailResum`
	EmailSendStr=`$Qualcomm8155BashPath/${JENKINS_PRJNAME}_Package/${Pak_Shell_Path##*/}/readXMLFile.sh Qnx $JENKINS_PRJNAME Package Email | sed 's/{//g' | sed 's/}//g'`
	JFROGResum=`read_flag JFROGResum`
	JFROGWebsite=$WareHouse
	python $Qualcomm8155BashPath/${JENKINS_PRJNAME}_Package/${Pak_Shell_Path##*/}/AutoSendEmail.pyc $JENKINS_PRJNAME $JENKINS_SVN_VERSION $JENKINS_SW1_NAME "$DataNowTime" $EmailResum $EmailSendStr "$Out6W6CDir" "$WaitDoqueStr" "$RunDoqueStr" "$memoryTip" $JENKINS_BUILD_NUMBER $buildCollectMessage $JFROGResum "$JFROGWebsite"
	
	if [[ -f $CollectEmailFilePath/SendCollectEmail ]] && [[ $CollectEmailFilePath != "" ]];then
	    echo "delect $CollectEmailFilePath/SendCollectEmail after send collect Email ..."
		rm -f $CollectEmailFilePath/SendCollectEmail
	fi
}

upload_to_jfrog ()
{
	PackagePath=${BuildOutputPath}/${JENKINS_PRJNAME}/${Out6W6CDir}
	if [[ ${JENKINS_BUILD_SECURE} == "1" ]];then
		UpdatePackage=Cockpit_${JENKINS_PRJNAME%%_*}_${JENKINS_SVN_VERSION}.${PACKAGE_BUILD_DATE:2}_safety
	else
		UpdatePackage=Cockpit_${JENKINS_PRJNAME%%_*}_${JENKINS_SVN_VERSION}.${PACKAGE_BUILD_DATE:2}
	fi
	UpdatePackage=${UpdatePackage}.zip
	OTAPackage=ota.zip
	EmailResum=`read_flag EmailResum`
	if [[ $JENKINS_UPLOAD_TO_JFROG -eq 1 ]];then
		if [[ $EmailResum -eq 0 ]];then
			if [[ -f $PackagePath/update/$UpdatePackage ]];then
				jf rt u $PackagePath/update/$UpdatePackage ${Jfrogpath}/${JENKINS_PRJNAME}/${Out6W6CDir}/$UpdatePackage
				if [[ $? -ne 0 ]];then
					write_flag 2 JFROGResum
					echo "$UpdatePackage upload JFROG failed!"
				else
					write_flag 1 JFROGResum
					echo "$WareHouse/${JENKINS_PRJNAME}/${Out6W6CDir}/$UpdatePackage" > $PackagePath/versioninfo.txt
					jf rt u $PackagePath/versioninfo.txt ${Jfrogpath}/${JENKINS_PRJNAME}/versioninfo.txt
				fi
			else
				echo "uploadToJFROG: $UpdatePackage is not exsit"
				write_flag 3 JFROGResum
			fi
			
			if [[ -f $PackagePath/$OTAPackage ]];then
				jf rt u $PackagePath/$OTAPackage ${Jfrogpath}/${JENKINS_PRJNAME}/${Out6W6CDir}/$OTAPackage
				if [[ $? -ne 0 ]];then
					write_flag 2 JFROGResum
					echo "$OTAPackage upload JFROG failed!"
				else
					write_flag 1 JFROGResum
					echo "$WareHouse/${JENKINS_PRJNAME}/${Out6W6CDir}/$OTAPackage" >> $PackagePath/versioninfo.txt
					jf rt u $PackagePath/versioninfo.txt ${Jfrogpath}/${JENKINS_PRJNAME}/versioninfo.txt
				fi
			else 
				echo "uploadToJFROG: $OTAPackage is not exsit"
				write_flag 3 JFROGResum
			fi
			
		else
			write_flag 3 JFROGResum
		fi
	fi
}

collect_build_time()
{
	endTime=`date +%F" "%T`
	start_sec=$(date -d "${DataNowTime}" +%s)
	end_sec=$(date -d "${endTime}" +%s)
	total_build_seconds=$((end_sec - start_sec))
	total_build_seconds=${total_build_seconds#-}  # 处理负数
	TotalBuildTimes=$(awk -v x=$total_build_seconds -v y=3600 'BEGIN{printf "%.1f\n", x/y}')
	echo "TotalBuildTimes=$TotalBuildTimes"
}

collect_build_n ()
{
    EmailResum=`read_flag EmailResum`
	PackageResum=`read_flag PackageResum`
    echo "Begin collect build Email .........   EmailResum = $EmailResum"
	if [[ -f $Qualcomm8155BashPath/${JENKINS_PRJNAME}_Package/QNX_ERROR_MODULE ]];then QNX_Error_Module=`cat $Qualcomm8155BashPath/${JENKINS_PRJNAME}_Package/QNX_ERROR_MODULE`;fi
	collect_build_time
	$Qualcomm8155BashPath/${JENKINS_PRJNAME}_Package/${Pak_Shell_Path##*/}/CollectBuildError.py $JENKINS_PRJNAME $JENKINS_BUILD_NUMBER $JENKINS_SVN_VERSION $EmailResum "$QNX_Error_Module" $PackageResum $TotalBuildTimes
}

#编译池检查排队
_reco_build_pool ()
{
    #同一项目至多启动一个编译进程判断
	echo "Begin inquired PID ........."
	echo "Main Pidid = $BASHPID......................"
	
	PIDResum=2
	
	#清除结束的编译进程
	$Qualcomm8155BashPath/${Start_Shell_Path##*/}/Qualcomm8155BashPID.sh clean $BASHPID $JENKINS_PRJNAME $BuildDoquePath $WaitPoolNum
	
	#判断当前启动的项目是否已经在运行编译
	tail $BuildDoquePath/waitDoqueFile | grep ^${JENKINS_PRJNAME}$
	waitFlag=$?
	tail $BuildDoquePath/runDoqueFile | grep ^${JENKINS_PRJNAME}$
	runFlag=$?
	
	if [[ $waitFlag -eq 0 ]] || [[ $runFlag -eq 0 ]];then
	    echo "Build $JENKINS_PRJNAME Project is runing   waitFlag = $waitFlag  runFlag = $runFlag !!!! exit 4"
	    PIDResum=4
	fi
	
	if [[ $JENKINS_BUILD_UFS = "1" ]] && [[ $JENKINS_SW1_NAME != "0" ]] && [[ $JENKINS_SW1_NAME != "4" ]];then
	    echo "Build UFS must choose full build !!!! exit 6"
	    PIDResum=6
	fi
	
	while [[ $PIDResum -eq 2 ]]
	do
	    PoolCount=$((PoolCount+1))
	    if [[ ${PoolCount} -ge ${PoolNum} ]];then
	        PoolCount=0
		    echo "None Qualcomm8155Pool is available at the moment,Please Waing ......"
		    sleep 60
	    fi
	    if [[ ! -d $Qualcomm8155BashPath/Qualcomm8155Pool${PoolCount} ]] && [[ ${PoolCount} -le ${PoolNum} ]];then
	        mkdir -p $Qualcomm8155BashPath/Qualcomm8155Pool${PoolCount}
	    fi
	    $Qualcomm8155BashPath/${Start_Shell_Path##*/}/Qualcomm8155BashPID.sh Qualcomm8155Pool${PoolCount} $BASHPID $JENKINS_PRJNAME $BuildDoquePath $WaitPoolNum 
	    PIDResum=$?
	    if [[ $PIDResum -eq 2 ]] && [[ $WaitFlag -ge 0 ]];then
	        WaitFlag=$((WaitFlag+1))
	    fi
	   
	    #邮件通知  启动的项目开始排队和开始编译
	    if ([[ $WaitFlag -ge $PoolNum ]] && [[ $PIDResum -eq 2 ]]) || ([[ $WaitFlag -eq -1 ]] && [[ $PIDResum -eq 1 ]]);then
			write_flag $PIDResum EmailResum
			WaitFlag=-1
	        send_email
	    fi
	done
	
	#当前项目已经在运行/当前项目已经无法排队，排队队列已满/选择UFS烧录后必须选择全编译
	if [[ $PIDResum -gt 2 ]];then
		write_flag $PIDResum EmailResum
	    build_process_exit
	fi
}

#安卓编译
_start_build_android_mode ()
{
    if [[ $JENKINS_SW1_NAME = "4" ]];then curAndroidPid=$BASHPID;fi
	PATH=$FGE_PATH_TMP
	
    cd $F_BUILD_PATH	
	rm -rf $Qualcomm8155BashPath/${JENKINS_PRJNAME}_Package/error_android_${JENKINS_PRJNAME}_*.log
	./${Build_Shell_Path##*/}/Batch_Build_1.2.sh -p $JENKINS_PRJNAME -v $JENKINS_SVN_VERSION -t $JENKINS_SVN_TAG_PATH -m $JENKINS_MODEL_NAME -f $JENKINS_SW2_NAME -c $JENKINS_BUILD_C1 -q "$PACKAGE_BUILD_DATE" -b $JENKINS_BUILD_NUMBER -g $JENKINS_SW3_NAME -h "$BuildNowTime" -j $JENKINS_SVN_INT_VERSION -s $JENKINS_BUILD_SECURE -x $JENKINS_BUILD_XUGAO > >(tee $Qualcomm8155BashPath/${JENKINS_PRJNAME}_Package/error_android_${JENKINS_PRJNAME}_${JENKINS_BUILD_NUMBER}.log) 2>&1
	AndroidResum=$?
	
	EmailResum=`read_flag EmailResum`
	write_flag $AndroidResum AndroidResum
	if [[ $EmailResum -ne 0 ]] && [[ $AndroidResum -eq 0 ]];then
	    echo "Android Build Over , EmailResum = $EmailResum   AndroidResum = $AndroidResum, no write EmailResum"
	else
	    write_flag $AndroidResum EmailResum
	fi
    write_flag 0 startBuildFlag     #设置编译状态为结束
	
	PackageResum=`read_flag PackageResum`
	AndroidPidResum=`read_flag AndroidPidResum`
	QnxPidResum=`read_flag QnxPidResum`
	echo "Build Android AndroidResum = $AndroidResum ; curAndroidPid = $curAndroidPid   WriteFlag AndroidResum = $AndroidPidResum"
	if [[ $AndroidResum -ne 0 ]];then
		if [[ $PackageResum -eq 2 ]];then
			write_flag 3 PackageResum
		else
		    write_flag 1 PackageResum
		fi
		
		if [[ $JENKINS_SW1_NAME = "4" ]] && [[ $AndroidPidResum = $curAndroidPid ]];then
		    curQnxPid=`ps --ppid ${curFatherPid} | awk '{if($1~/[0-9]+/) print $1}' | grep -v $curAndroidPid`
			curQnxPidFlag=$?
			if [[ $curQnxPid != $QnxPidResum ]] || [[ $curQnxPidFlag -ne 0 ]];then return;fi
		    qnxLocalSubPid=$(ps --no-headers --ppid=${curQnxPid} o pid)
			qnxLocalSubPidFlag=$?
			if [[ $qnxLocalSubPidFlag -ne 0 ]];then return;fi
			
			qnxLocalGranPid=$(ps --no-headers --ppid=${qnxLocalSubPid} o pid)
			qnxLocalGranPidFlag=$?
			if [[ $qnxLocalGranPidFlag -ne 0 ]];then qnxLocalGranPid="";fi
			echo "Async Pid curQnxPid=$curQnxPid    qnxLocalSubPid=$qnxLocalSubPid   qnxLocalGranPid=$qnxLocalGranPid"
			echo "Async Build Android Build Error,Kill Qnx Build and exit !!!! kill $curQnxPid $qnxLocalSubPid $qnxLocalGranPid"
			write_flag 1 asyncBuildFlag
		    #kill $curQnxPid $qnxLocalSubPid $qnxLocalGranPid
		fi
	fi
}

#QNX编译
_start_build_qnx_mode ()
{
    AndroidResum=`read_flag AndroidResum`
    if [[ $AndroidResum -gt 100 ]] && [[ $JENKINS_SW1_NAME = "0" ]];then return;fi
	if [[ $JENKINS_SW1_NAME = "4" ]];then curQnxPid=$BASHPID;fi
    PATH=$FGE_PATH_TMP
	
    cd $F_BUILD_PATH
	rm -rf $Qualcomm8155BashPath/${JENKINS_PRJNAME}_Package/error_qnx_${JENKINS_PRJNAME}_*.log
	./${Build_Shell_Path##*/}/Qualcomm8155QnxBuild.sh -p $JENKINS_PRJNAME -v $JENKINS_SVN_VERSION -a $JENKINS_WORK_ORDER -y $JENKINS_ISNEED_COV_BUILD -n $JENKINS_SVN_PATH -f $JENKINS_SW2_NAME -c $JENKINS_BUILD_C1 -u $JENKINS_BUILD_UFS -t $JENKINS_SW1_NAME -q "$PACKAGE_BUILD_DATE" -b $JENKINS_BUILD_NUMBER -d $androidSubPid -g $JENKINS_SW3_NAME -s $JENKINS_BUILD_SECURE -x $JENKINS_BUILD_XUGAO > >(tee $Qualcomm8155BashPath/${JENKINS_PRJNAME}_Package/error_qnx_${JENKINS_PRJNAME}_${JENKINS_BUILD_NUMBER}.log) 2>&1
	QnxResum=$?
	
	EmailResum=`read_flag EmailResum`
	write_flag $QnxResum QnxResum
	if [[ $EmailResum -ne 0 ]] && [[ $QnxResum -eq 0 ]];then
	    echo "Qnx Build Over , EmailResum = $EmailResum   QnxResum = $QnxResum, no write EmailResum"
	else
	    write_flag $QnxResum EmailResum
	fi
    write_flag 0 startBuildFlag     #设置编译状态为结束
	
	PackageResum=`read_flag PackageResum`
	AndroidPidResum=`read_flag AndroidPidResum`
	QnxPidResum=`read_flag QnxPidResum`
	echo "Build QNX QnxResum = $QnxResum    PackageResum = $PackageResum  curQnxPid = $curQnxPid   WriteFlag QnxPidResum = $QnxPidResum"
	if [[ $QnxResum -ne 0 ]];then
	    if [[ $PackageResum -eq 1 ]];then
			write_flag 3 PackageResum
		else
			write_flag 2 PackageResum
		fi
		
		if [[ $JENKINS_SW1_NAME = "4" ]] && [[ $QnxPidResum = $curQnxPid ]];then
		    curAndroidPid=`ps --ppid ${curFatherPid} | awk '{if($1~/[0-9]+/) print $1}' | grep -v $curQnxPid`
			curAndroidPidFlag=$?
			if [[ $curAndroidPid != $AndroidPidResum ]] || [[ $curAndroidPidFlag -ne 0 ]];then return;fi
		    androidLocalSubPid=$(ps --no-headers --ppid=${curAndroidPid} o pid)
			androidLocalSubPidFlag=$?
			if [[ $androidLocalSubPidFlag -ne 0 ]];then return;fi
			
			androidLocalGranPid=$(ps --no-headers --ppid=${androidLocalSubPid} o pid)
			androidLocalGranPidFlag=$?
			if [[ $androidLocalGranPidFlag -ne 0 ]];then androidLocalGranPid="";fi
			echo "Async Pid curAndroidPid=$curAndroidPid    androidLocalSubPid=$androidLocalSubPid   androidLocalGranPid=$androidLocalGranPid"
			echo "Async Build QNX Build Error,Kill Android Build and exit !!!! kill $curAndroidPid $androidLocalSubPid $androidLocalGranPid"
			write_flag 1 asyncBuildFlag
		    #kill $curAndroidPid $androidLocalSubPid $androidLocalGranPid
		fi
	fi
}

_start_make_ailabi_pack ()
{
    PackageResum=`read_flag PackageResum`
	
    if ([[ $JENKINS_SW3_NAME != "2" ]] && [[ $JENKINS_SW3_NAME != "5" ]]) || [[ $PackageResum -ne 0 ]];then return;fi
	#艾拉比全量包制作进程监控（一个服务器仅能同时做一个艾拉比全量包）
	$Qualcomm8155BashPath/${Start_Shell_Path##*/}/Qualcomm8155BashPID.sh 'AILABI' $BASHPID $JENKINS_PRJNAME $BuildDoquePath $WaitPoolNum
	AilabiPid=$?
	while [[ $AilabiPid -eq 2 ]]
    do
	    echo "Other Project is doing Ailabi Whole Package,Please wait.............."
	    sleep 90
		$Qualcomm8155BashPath/${Start_Shell_Path##*/}/Qualcomm8155BashPID.sh 'AILABI' $BASHPID $JENKINS_PRJNAME $BuildDoquePath $WaitPoolNum
	    AilabiPid=$?
    done
    rm -rf $Qualcomm8155BashPath/${JENKINS_PRJNAME}_Package/error_ota_${JENKINS_PRJNAME}_*.log
	$Qualcomm8155BashPath/${JENKINS_PRJNAME}_Package/${Pak_Shell_Path##*/}/buildOtaPackage.sh $JENKINS_PRJNAME > >(tee $Qualcomm8155BashPath/${JENKINS_PRJNAME}_Package/error_ota_${JENKINS_PRJNAME}_${JENKINS_BUILD_NUMBER}.log) 2>&1
	OTAResum=$?
	if [[ $OTAResum -ne 0 ]];then write_flag 4 PackageResum;fi
	write_flag $OTAResum OTAResum
	write_flag $OTAResum EmailResum
	echo "Build Ailabi Package OTAResum = $OTAResum    PackageResum = $PackageResum"
}

_start_make_tar_pack ()
{
    PackageResum=`read_flag PackageResum`
	
    if ([[ $JENKINS_SW1_NAME != "0" ]] && [[ $JENKINS_SW1_NAME != "1" ]] && [[ $JENKINS_SW1_NAME != "4" ]]) || [[ $PackageResum -ne 0 ]];then return;fi
    echo "_start_make_tar_pack"
	InstallTarFlag=1
	AmssTarFlag=0
	if [[ $JENKINS_WORK_ORDER = "1" ]] || [[ $JENKINS_BUILD_UFS = "1" ]];then AmssTarFlag=1;fi
	$Qualcomm8155BashPath/${JENKINS_PRJNAME}_Package/${Pak_Shell_Path##*/}/TarPackageSH.sh -b $PoolCount -p $JENKINS_PRJNAME -i $InstallTarFlag -a $AmssTarFlag
}

_start_make_ufs_pack ()
{
    PackageResum=`read_flag PackageResum`
	
    if ([[ $JENKINS_SW1_NAME != "0" ]] && [[ $JENKINS_SW1_NAME != "4" ]]) || [[ $JENKINS_BUILD_UFS != "1" ]] || [[ $PackageResum -ne 0 ]];then return;fi
    echo "_start_make_ufs_pack"
	rm -rf $Qualcomm8155BashPath/${JENKINS_PRJNAME}_Package/error_ufs_${JENKINS_PRJNAME}_*.log
	$Qualcomm8155BashPath/${JENKINS_PRJNAME}_Package/${Pak_Shell_Path##*/}/Ufs_Qualcomm_Build.sh $JENKINS_PRJNAME $JENKINS_SVN_VERSION $PoolCount $JENKINS_BUILD_NUMBER > >(tee $Qualcomm8155BashPath/${JENKINS_PRJNAME}_Package/error_ufs_${JENKINS_PRJNAME}_${JENKINS_BUILD_NUMBER}.log) 2>&1
	UFSResum=$?
	write_flag $UFSResum UFSResum
	write_flag $UFSResum EmailResum
}

#启动各模块编译
_start_build_module ()
{
    cd $Qualcomm8155BashPath/Qualcomm8155Pool${PoolCount}
	F_BUILD_PATH=$(pwd)
  
	if [[ -e $Qualcomm8155BashPath/Qualcomm8155Pool${PoolCount}/${Build_Shell_Path##*/} ]] && [[ $Qualcomm8155BashPath != "" ]] && [[ ${Build_Shell_Path##*/} != "" ]]; then
	    rm -rf $Qualcomm8155BashPath/Qualcomm8155Pool${PoolCount}/${Build_Shell_Path##*/}
	fi
	
	#更新编译脚本
	svn --force export $Build_Shell_Path 
	if [ $? -ne 0 ];then
	    _print "shell svn checkout $Build_Shell_Path failed, exit error!!"
	    exit 2
	fi
	
	cd $F_BUILD_PATH
	chmod 777 ./${Build_Shell_Path##*/}/*.sh
	chmod 777 ./${Build_Shell_Path##*/}/*.py
	chmod 777 ./${Build_Shell_Path##*/}/*.pyc

    export FGE_PATH_TMP=$PATH
    echo "FGE_PATH_TMP=$FGE_PATH_TMP"
	
	#全编 采用多线程同时编译安卓和QNX
	if [[ $JENKINS_SW1_NAME = "0" ]];then
		_start_build_android_mode
		_start_build_qnx_mode
	elif [[ $JENKINS_SW1_NAME = "1" ]];then
	    _start_build_qnx_mode
	elif [[ $JENKINS_SW1_NAME = "2" ]];then
	    _start_build_android_mode
	elif [[ $JENKINS_SW1_NAME = "4" ]];then
	    curFatherPid=$BASHPID
	    _start_build_android_mode &
		androidSubPid=$!
		write_flag $androidSubPid AndroidPidResum
		echo "Begin start a new android sub process "$androidSubPid
		
		_start_build_qnx_mode &
		qnxSubPid=$!
		write_flag $qnxSubPid QnxPidResum
		echo "Begin start a new qnx sub process "$qnxSubPid
	    wait
	else
	    echo "$JENKINS_SW1_NAME build param didnt make build process ....."
	fi
}

_rebuild_package ()
{
	cd $Qualcomm8155BashPath/${JENKINS_PRJNAME}_Package
	if [[ -e $Qualcomm8155BashPath/${JENKINS_PRJNAME}_Package/${Pak_Shell_Path##*/} ]] && [[ $Qualcomm8155BashPath != "" ]] && [[ ${Pak_Shell_Path##*/} != "" ]]; then
	    rm -rf $Qualcomm8155BashPath/${JENKINS_PRJNAME}_Package/${Pak_Shell_Path##*/}
	fi
	
	#更新编译脚本
	svn --force export $Pak_Shell_Path 
	if [ $? -ne 0 ];then
	    _print "shell svn checkout $Pak_Shell_Path failed, exit error!!"
	    exit 2
	fi
	chmod 777 ./${Pak_Shell_Path##*/}/*.sh
	chmod 777 ./${Pak_Shell_Path##*/}/*.py
	chmod 777 ./${Pak_Shell_Path##*/}/*.pyc
	
	if [[ $JENKINS_SW1_NAME = "4" ]];then
	    _start_make_ailabi_pack &
		ailabiSubPid=$!
		echo "Begin start a new ailabi sub process "$ailabiSubPid
		
		_start_make_ufs_pack &
		ufsSubPid=$!
		echo "Begin start a new ufs sub process "$ufsSubPid
		
		_start_make_tar_pack &
		tarSubPid=$!
		echo "Begin start a new tar sub process "$tarSubPid
		
		wait
	else
	    _start_make_ailabi_pack
		_start_make_ufs_pack
		_start_make_tar_pack
	fi
	
	PackageResum=`read_flag PackageResum`
	echo "build MakePackage   !!!!!!!  PackageResum = $PackageResum"
	rm -rf $Qualcomm8155BashPath/${JENKINS_PRJNAME}_Package/error_package_${JENKINS_PRJNAME}_*.log
	$Qualcomm8155BashPath/${JENKINS_PRJNAME}_Package/${Pak_Shell_Path##*/}/MakePackage_3.0.sh -p $JENKINS_PRJNAME -v $JENKINS_SVN_VERSION -t $JENKINS_SW1_NAME -y $JENKINS_ISNEED_COV_BUILD -r $JENKINS_SW3_NAME -c $JENKINS_BUILD_C1 -a $JENKINS_WORK_ORDER -u $JENKINS_BUILD_UFS -f $JENKINS_SW2_NAME -z "$Out6W6CDir" -q "$PACKAGE_BUILD_DATE" -o $PackageResum -h "$BuildNowTime" -s $JENKINS_BUILD_SECURE -x $JENKINS_BUILD_XUGAO > >(tee ./error_package_${JENKINS_PRJNAME}_${JENKINS_BUILD_NUMBER}.log) 2>&1
	PACResum=$?
	
	OTAResum=`read_flag OTAResum`
	UFSResum=`read_flag UFSResum`
	InspectResum=`read_flag InspectResum`
	PackageResum=`read_flag PackageResum`

	if [[ $PACResum -ne 0 ]] && [[ $PackageResum -eq 0 ]];then write_flag $PACResum EmailResum;fi
	if [[ $UFSResum -ne 0 ]];then write_flag $UFSResum EmailResum;fi
	if [[ $OTAResum -ne 0 ]];then write_flag $OTAResum EmailResum;fi
	if [[ $InspectResum -gt 0 ]];then write_flag $InspectResum EmailResum;fi
}

_jenkins_input_param_check()
{
	if [[ $JENKINS_SVN_VERSION == *" "* ]];then
		JenkinsInputCheck=8
		echo "Version number input error, including spaces"
	fi
	
	if [[ $JenkinsInputCheck -ne 0 ]];then
		write_flag $JenkinsInputCheck EmailResum
		build_process_exit
	fi
}

_start_build_xml(){
    _print "_start_build_xml........"
	date +%F" "%T
	   
	memorySize=`df -h | grep /mnt/BU2_NAS | awk '{print $4}' | tr -cd '[0-9]'`
	#6w6c空间不足200G时，邮件提示
	if [[ $memorySize -le 100 ]];then
	    memoryTip=$memorySize
	fi
	
	DateRegularMaxN=$((DateRegularMax*24*60*60))
    DateRegular=`date -d "$JENKINS_REGULAR_TIMER" +%s`
	DataNowTime=`date +%F" "%T`
	BuildNowTime=`date +%F"_"%T`
	DataNow=`date -d "$DataNowTime" +%s`
	   
	#编译脚本路径选择（StartShell脚本需要手动更新至$Qualcomm8155BashPath)
	if [[ ${JENKINS_PRJNAME} = "HS7006A_Test" ]] || [[ ${JENKINS_PRJNAME} = "HS7012A_Test" ]] || [[ ${JENKINS_PRJNAME} = "HS7023A_Git" ]];then
		Start_Shell_Path="http://**********/svn/Qualcomm/02_SA8155P/01_HS7001A/02_CodeLib/01_MainPath/1.2/02_Android/04_Shell/buildShellTest/startShell"
		Pak_Shell_Path="http://**********/svn/Qualcomm/02_SA8155P/01_HS7001A/02_CodeLib/01_MainPath/1.2/02_Android/04_Shell/buildShellTest/packShell"
		Build_Shell_Path="http://**********/svn/Qualcomm/02_SA8155P/01_HS7001A/02_CodeLib/01_MainPath/1.2/02_Android/04_Shell/buildShellTest/buildShell"
	else
		Start_Shell_Path="http://**********/svn/Qualcomm/02_SA8155P/01_HS7001A/02_CodeLib/01_MainPath/1.2/02_Android/04_Shell/buildShell3.0/startShell"
		Pak_Shell_Path="http://**********/svn/Qualcomm/02_SA8155P/01_HS7001A/02_CodeLib/01_MainPath/1.2/02_Android/04_Shell/buildShell3.0/packShell"
		Build_Shell_Path="http://**********/svn/Qualcomm/02_SA8155P/01_HS7001A/02_CodeLib/01_MainPath/1.2/02_Android/04_Shell/buildShell3.0/buildShell"
	fi
	
	if [[ -f $Qualcomm8155BashPath/${JENKINS_PRJNAME}_Package/QNX_ERROR_MODULE ]];then rm -f $Qualcomm8155BashPath/${JENKINS_PRJNAME}_Package/QNX_ERROR_MODULE;fi
	if [[ ! -d $Qualcomm8155BashPath/${JENKINS_PRJNAME}_Package ]];then mkdir -p $Qualcomm8155BashPath/${JENKINS_PRJNAME}_Package;fi
	   
	#检查Jenkins上输入的编译参数是否合规
	_jenkins_input_param_check
	   
	#定时编译任务判断
	if [[ $DataNow -lt $DateRegular ]] && [[ $((DateRegular - DataNow)) -lt $DateRegularMaxN ]];then
		#清除结束的编译进程
	    $Qualcomm8155BashPath/${Start_Shell_Path##*/}/Qualcomm8155BashPID.sh clean $BASHPID $JENKINS_PRJNAME $BuildDoquePath $WaitPoolNum
		tail $BuildDoquePath/waitDoqueFile | grep ^${JENKINS_PRJNAME}$
		waitFlag=$?
		tail $BuildDoquePath/runDoqueFile | grep ^${JENKINS_PRJNAME}$
		runFlag=$?
	    while [[ $DataNow -lt $DateRegular ]] || [[ $waitFlag -eq 0 ]] || [[ $runFlag -eq 0 ]]
        do
            sleep 90
	        echo "Waiting.............."
	        DataNowTime=`date +%F" "%T`
			BuildNowTime=`date +%F"_"%T`
	        DataNow=`date -d "$DataNowTime" +%s`
			#清除结束的编译进程
			$Qualcomm8155BashPath/${Start_Shell_Path##*/}/Qualcomm8155BashPID.sh clean $BASHPID $JENKINS_PRJNAME $BuildDoquePath $WaitPoolNum
			tail $BuildDoquePath/waitDoqueFile | grep ^${JENKINS_PRJNAME}$
			waitFlag=$?
			tail $BuildDoquePath/runDoqueFile | grep ^${JENKINS_PRJNAME}$
			runFlag=$?
			echo "waitFlag=$waitFlag...runFlag=$runFlag"
        done
		echo "The scheduled task has arrived at the designated time and there are no identical waiting or running tasks"
	else
	    echo "Regular Build process didnt set or setting param of Regular data is wrong(Regular data must less than $DateRegularMax days)"
	fi
	
	PACKAGE_BUILD_DATE=$(date +%Y%m%d)
	   
	if [[ $JENKINS_SW1_NAME = "0" ]] || [[ $JENKINS_SW1_NAME = "1" ]] || [[ $JENKINS_SW1_NAME = "2" ]] || [[ $JENKINS_SW1_NAME = "4" ]];then
	    cd $Qualcomm8155BashPath
		_reco_build_pool
		
		init_flag
		write_flag 1 startBuildFlag
		
	    _start_build_module
		drop_out_inspect
	    _rebuild_package
	elif [[ $JENKINS_SW1_NAME = "3" ]];then
	    init_flag
		drop_out_inspect
	    _rebuild_package
	else
	    echo "Qualcomm8155 build type input is not invaild !!!!  "
	fi
	 
	build_process_exit
}

_start_build()
{
    _print "_start_build........"
	
	DateCheck=`date -d "2022-11-21 00:00:00" +%s`
    DateRegular=`date -d "$JENKINS_REGULAR_TIMER" +%s`
	DataNowTime=`date +%F" "%T`
	DataNow=`date -d "$DataNowTime" +%s`
	
	echo "Before inquired PID ........."
	echo "First Pidid......................"$BASHPID
	
	./Qualcomm8155BashPID.sh $JENKINS_PRJNAME $BASHPID
	PIDResum=$?
	if [[ $PIDResum -eq 2 ]];then
	    echo "Qualcomm8155 project $JENKINS_PRJNAME is already exist !!!!! exit ........."
	    chmod 777 ./$JENKINS_PRJNAME/AutoSendEmail.pyc
	    python ./$JENKINS_PRJNAME/AutoSendEmail.pyc $JENKINS_PRJNAME $JENKINS_SVN_VERSION $AndroidResum $QnxResum $JENKINS_SW1_NAME "$DataNowTime" $OTAResum $PIDResum
	    exit 2
	fi
	
	echo "$JENKINS_PRJNAME"
	
	if [[ $JENKINS_PRJNAME = "HS7001AWS" ]]; then
	    Shell_Svn_Path="http://**********/svn/Qualcomm/02_SA8155P/HS7001AWS/02_CodeLib/01_ProjectPath/02_Android/06_Shell/Jenkins"
	elif [[ $JENKINS_PRJNAME = "HS7002A" ]] || [[ $JENKINS_PRJNAME = "HS7002B" ]] || [[ $JENKINS_PRJNAME = "HS7010B" ]] || [[ $JENKINS_PRJNAME = "HS7016A" ]] || [[ $JENKINS_PRJNAME = "HS7019A" ]];then
	    Shell_Svn_Path="http://**********/svn/Qualcomm/02_SA8155P/HS7002A/02_CodeLib/01_ProjectPath/02_Android/06_Shell/newJenkins"
	else
	    Shell_Svn_Path="http://**********/svn/Qualcomm/02_SA8155P/HS7003A/02_CodeLib/01_ProjectPath/02_Android/06_Shell/buildShell2.0"
	fi
	
	if [ ! -d $JENKINS_PRJNAME ];then
	    mkdir -p "$JENKINS_PRJNAME"
	fi
	cd $JENKINS_PRJNAME
	F_BUILD_PATH=$(pwd)
  
	#_print "-------svn checkout $JENKINS_SHELL_PATH JenkinsShell $SUBMIT_FLAG ------------"
	if [ -e "Jenkins_Shell" ]; then
	    rm -rf ./Jenkins_Shell
	fi
	
	mkdir -p ./Jenkins_Shell
	cd Jenkins_Shell
	
	svn checkout $Shell_Svn_Path ./  
	
	cd $F_BUILD_PATH
	cp -rf ./Jenkins_Shell/* ./
	chmod 777 ./*.sh
	chmod 777 ./Android_Source_Shell/*.sh
	chmod 777 ./Qnx_Source_Shell/*.sh

    export FGE_PATH_TMP=$PATH
    echo "FGE_PATH_TMP=$FGE_PATH_TMP"

	if [[ $DateCheck -lt $DateRegular ]];then
	    while [ $DataNow -lt $DateRegular ]
        do
            sleep 60
	        echo "Waiting.............."
	        DataNowTime=`date +%F" "%T`
	        DataNow=`date -d "$DataNowTime" +%s`
        done
	fi	
	
	#  $JENKINS_SW1_NAME = 1 ? only build qnx
	if [[ $JENKINS_SW1_NAME == "0" ]] || [[ $JENKINS_SW1_NAME == "2" ]]; then
        cd $F_BUILD_PATH
	    rm -rf ./error_android_*.log
        if [[ $JENKINS_PRJNAME == "HS7001AWS" ]]; then
       	    ./Batch_Build.sh -v $JENKINS_SVN_VERSION -t $JENKINS_SVN_TAG_PATH -s $JENKINS_MODEL_NAME > >(tee ./error_android_${JENKINS_SVN_VERSION}_$(date "+%Y%m%d").log) 2>&1
		    AndroidResum=$?
	    elif [[ $JENKINS_PRJNAME == "HS7002A" ]] || [[ $JENKINS_PRJNAME == "HS7002B" ]] || [[ $JENKINS_PRJNAME = "HS7010B" ]] || [[ $JENKINS_PRJNAME = "HS7016A" ]] || [[ $JENKINS_PRJNAME = "HS7019A" ]];then
	        ./Batch_Build_1.2.sh -p $JENKINS_PRJNAME -v $JENKINS_SVN_VERSION -t $JENKINS_SVN_TAG_PATH -s $JENKINS_MODEL_NAME -c $JENKINS_BUILD_C1 -b $JENKINS_BUILD_NUMBER> >(tee ./error_android_${JENKINS_SVN_VERSION}_$(date "+%Y%m%d").log) 2>&1
		    AndroidResum=$?
	    else
	        ./Batch_Build_1.2.sh -v $JENKINS_SVN_VERSION -p $JENKINS_PRJNAME -t $JENKINS_SVN_TAG_PATH -s $JENKINS_MODEL_NAME -f $JENKINS_SW2_NAME -c $JENKINS_BUILD_C1 > >(tee ./error_android_${JENKINS_SVN_VERSION}_$(date "+%Y%m%d").log) 2>&1
		    AndroidResum=$?
	    fi
	   
	    echo "Build Android AndroidResum = "$AndroidResum
	   
        PATH=$FGE_PATH_TMP
	fi

    if ([[ $JENKINS_SW1_NAME == "0" ]] || [[ $JENKINS_SW1_NAME == "1" ]]) && [[ $AndroidResum -ne 2 ]]; then
        cd $F_BUILD_PATH
	    rm -rf ./error_qnx_*.log
	    if [[ $JENKINS_PRJNAME == "HS7001AWS" ]]; then
	        ./buildhyprojectHS7001A.sh -p $JENKINS_PRJNAME -v $JENKINS_SVN_VERSION -a $JENKINS_WORK_ORDER -y "$JENKINS_ISNEED_COV_BUILD" -r $JENKINS_SW3_NAME > >(tee ./error_qnx_${JENKINS_SVN_VERSION}_$(date "+%Y%m%d").log) 2>&1
	    elif [[ $JENKINS_PRJNAME == "HS7002A" ]] || [[ $JENKINS_PRJNAME == "HS7002B" ]] || [[ $JENKINS_PRJNAME = "HS7010B" ]] || [[ $JENKINS_PRJNAME = "HS7016A" ]] || [[ $JENKINS_PRJNAME = "HS7019A" ]]; then
	        ./buildhyprojectHS7002A.sh -p $JENKINS_PRJNAME -v $JENKINS_SVN_VERSION -a $JENKINS_WORK_ORDER -y "$JENKINS_ISNEED_COV_BUILD" -n $JENKINS_SVN_PATH -f $JENKINS_SW2_NAME -r $AndroidResum -c $JENKINS_BUILD_C1 -s $JENKINS_BUILD_SECURE -b $JENKINS_BUILD_NUMBER > >(tee ./error_qnx_${JENKINS_SVN_VERSION}_$(date "+%Y%m%d").log) 2>&1
	    else
	        ./buildhyprojectHS7003A.sh -p $JENKINS_PRJNAME -v $JENKINS_SVN_VERSION -a $JENKINS_WORK_ORDER -y $JENKINS_ISNEED_COV_BUILD -n $JENKINS_SVN_PATH -f $JENKINS_SW2_NAME -c $JENKINS_BUILD_C1 -u $JENKINS_BUILD_UFS -t $JENKINS_SW1_NAME > >(tee ./error_qnx_${JENKINS_SVN_VERSION}_$(date "+%Y%m%d").log) 2>&1
	    fi
	    result=$?
	    QnxResum=$result
	    AMSSResum=$result
	   
	    PATH=$FGE_PATH_TMP
	    _print "-------build work over ------------"
	fi
	
	cd $F_BUILD_PATH
	if [[ $JENKINS_SW3_NAME = "2" ]];then
	    if [[ $AndroidResum != "2" ]] && [[ $QnxResum != "2" ]] && [[ $AMSSResum != "4" ]];then
	        /mnt/home/<USER>/e-cockpit/qnx-android/jenkins_qnx-android_Qualcomm8155/Qualcomm8155BashPID.sh 'AILABI' $BASHPID
	        AilabiPid=$?
	        while [[ $AilabiPid -eq 2 ]]
            do
	            echo "Other Project is doing Ailabi Whole Package,Please wait.............."
			    sleep 90
			    /mnt/home/<USER>/e-cockpit/qnx-android/jenkins_qnx-android_Qualcomm8155/Qualcomm8155BashPID.sh 'AILABI' $BASHPID
	            AilabiPid=$?
            done
		    rm -rf ./error_ota_*.log
	        ./buildOtaPackage.sh $F_BUILD_PATH $JENKINS_PRJNAME > >(tee ./error_ota_${JENKINS_SVN_VERSION}_$(date "+%Y%m%d").log) 2>&1
	        OTAResum=$?
	    else
	        echo "Android or Qnx or Amss build Error !!!! Return Build Ota !!!"
	    fi
	fi
	
	cd $F_BUILD_PATH
	if [[ $JENKINS_PRJNAME != "HS7002A" ]] && [[ $JENKINS_PRJNAME != "HS7002B" ]] && [[ $JENKINS_PRJNAME != "HS7010B" ]] && [[ $JENKINS_PRJNAME != "HS7016A" ]] && [[ $JENKINS_PRJNAME != "HS7019A" ]]; then
	    echo "build MakePackage!!!!!!!"
	    rm -rf ./error_package_*.log
	    ./MakePackage.sh -p $JENKINS_PRJNAME -v $JENKINS_SVN_VERSION -t $JENKINS_SW1_NAME -y $JENKINS_ISNEED_COV_BUILD -r $JENKINS_SW3_NAME -c $JENKINS_BUILD_C1 -a $JENKINS_WORK_ORDER -u $JENKINS_BUILD_UFS -f $JENKINS_SW2_NAME > >(tee ./error_package_${JENKINS_SVN_VERSION}_$(date "+%Y%m%d").log) 2>&1
	fi	

	if [[ $JENKINS_BUILD_UFS = "1" ]];then
	    rm -rf ./error_ufs_*.log
	    ./Ufs_Qualcomm_Build.sh $JENKINS_PRJNAME $JENKINS_SVN_VERSION > >(tee ./error_ufs_${JENKINS_SVN_VERSION}_$(date "+%Y%m%d").log) 2>&1
	    UFSResum=$?
    fi
	
	if [[ $AndroidResum == 0 ]] && [[ $QnxResum == 0 ]];then
		echo "build $JENKINS_PRJNAME success, clean hqx1.2.1.c1_r00004.2"
		rm -rf ./hqx1.2.1.c1_r00004.2
	fi
	
	chmod 777 ./AutoSendEmail.pyc
	rm -rf ./error_email_*.log
	python ./AutoSendEmail.pyc $JENKINS_PRJNAME $JENKINS_SVN_VERSION $AndroidResum $QnxResum $JENKINS_SW1_NAME "$DataNowTime" $OTAResum $PIDResum > >(tee ./error_email_${JENKINS_SVN_VERSION}_$(date "+%Y%m%d").log) 2>&1
	
    echo "Build Process is over ,Result: AndroidResum = $AndroidResum QnxResum = $QnxResum AMSSResum = $AMSSResum  UFSResum = $UFSResum OTAResum = $OTAResum PIDResum = $PIDResum"
}

if [[ $JENKINS_PRJNAME =~ HS7002 ]] || [[ $JENKINS_PRJNAME = "HS7010B" ]] || [[ $JENKINS_PRJNAME =~ "HS7016" ]] || [[ $JENKINS_PRJNAME =~ "HS7019" ]];then
	_start_build
elif [[ $JENKINS_PRJNAME =~ HS7025 ]];then
	if [[ ! -d $Qualcomm8155BashPath/${JENKINS_PRJNAME}_Package ]];then 
		mkdir -p $Qualcomm8155BashPath/${JENKINS_PRJNAME}_Package
	fi
	if [[ -f $Qualcomm8155BashPath/${JENKINS_PRJNAME}_Package/ratbuild.sh ]];then
		cd $Qualcomm8155BashPath/${JENKINS_PRJNAME}_Package
		./ratbuild.sh
	else
		echo "$Qualcomm8155BashPath/${JENKINS_PRJNAME}_Package/ratbuild.sh is not exsit! "
	fi
else
    _start_build_xml
fi




