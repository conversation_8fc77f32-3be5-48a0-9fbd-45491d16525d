OEM signed image with RSAPSS
ERROR: Following validations failed for the image:
       1. Following signing attributes do not match: 
          | Attribute | Image | Config |
          |-----------|-------|--------|
          |   debug   |  0x1  |  0x0   |
          
Image /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/mpss_8155/modem_proc/build/ms/bin/sm8150.gennmgw.prod/qdsp6sw.mbn is not encrypted

Base Properties: 
| Integrity Check             | True  |
| Signed                      | True  |
| Encrypted                   | False |
| Size of signature           | 256   |
| Size of one cert            | 2048  |
| Num of certs in cert chain  | 3     |
| Number of root certs        | 1     |
| Maximum number of root certs| None  |
| Cert chain size             | 6144  |

ELF Properties: 
Header: 
| Magic                      | ELF                           |
| Class                      | ELF32                          |
| Data                       | 2's complement, little endian  |
| Version                    | 1 (Current)                    |
| OS/ABI                     | No extensions or unspecified   |
| ABI Version                | 0                              |
| Type                       | EXEC (Executable file)         |
| Machine                    | 164                            |
| Version                    | 0x1                            |
| Entry address              | 0x8d800000                     |
| Program headers offset     | 0x00000034                     |
| Section headers offset     | 0x00000000                     |
| Flags                      | 0x00000066                     |
| ELF header size            | 52                             |
| Program headers size       | 32                             |
| Number of program headers  | 15                             |
| Section headers size       | 40                             |
| Number of section headers  | 0                              |
| String table section index | 0                              |

Program Headers: 
| Num | Type | Offset | VirtAddr | PhysAddr | FileSize | MemSize | Flags |   Align   |
|-----|------|--------|----------|----------|----------|---------|-------|-----------|
|  1  | LOAD |0x003000|0xc0800000|0x8d800000| 0x002020 | 0x003000|   RE  | 0x100000  |
|  2  | LOAD |0x006000|0xc0812000|0x8d812000| 0x039728 | 0x03a000|  RWE  | 0x1000    |
|  3  | LOAD |0x040000|0xc084c000|0x8d84c000| 0x00f770 | 0x010000|   RW  | 0x1000    |
|  4  | LOAD |0x050000|0xc085c000|0x8d85c000| 0x000ec8 | 0x001000|   R   | 0x1000    |
|  5  | LOAD |0x051000|0xc085d000|0x8d85d000| 0x0097e8 | 0x00a000|  RWE  | 0x1000    |
|  6  | LOAD |0x05b000|0xc0867000|0x8d867000| 0x01d054 | 0x01e000|  RWE  | 0x1000    |
|  7  | LOAD |0x079000|0xc0890000|0x8d890000| 0x2e8ab0 | 0x2e9000|   RE  | 0x1000    |
|  8  | LOAD |0x362000|0xc0b80000|0x8db80000| 0x000000 | 0x001000|   RW  | 0x1000    |
|  9  | LOAD |0x362000|0xc0bc0000|0x8dbc0000| 0x001010 | 0x002000|   RW  | 0x1000    |
|  10 | LOAD |0x364000|0xc0c00000|0x8dc00000| 0x149198 | 0x92e000|   R   | 0x1000    |
|  11 | LOAD |0x4ae000|0xc152e000|0x8e52e000| 0x2005a2 | 0x201000|   RW  | 0x1000    |
|  12 | LOAD |0x6af000|0xc172f000|0x8e72f000| 0x000000 | 0x506000|   RW  | 0x1000    |
|  13 | LOAD |0x6af000|0xc1c35000|0x8ec35000| 0x009d80 | 0x00a000|   RW  | 0x1000    |
|  14 | LOAD |0x6b9000|0xc1c40000|0x8ec40000| 0x006f23 | 0x007000|   R   | 0x1000    |
|  15 | LOAD |0x6c0000|0xc1c48000|0x8ec48000| 0x000000 | 0xbb8000|   R   | 0x1000    |

Hash Segment Properties: 
| Header Size     | 168B  |
| Hash Algorithm  | sha384|

Header: 
| cert_chain_ptr              | 0x8f800248  |
| cert_chain_size             | 0x00001800  |
| cert_chain_size_qti         | 0x00000000  |
| code_size                   | 0x00000330  |
| image_id                    | 0x0000000c  |
| image_size                  | 0x00001c30  |
| metadata_major_version      | 0x00000000  |
| metadata_major_version_qti  | 0x00000000  |
| metadata_minor_version      | 0x00000000  |
| metadata_minor_version_qti  | 0x00000000  |
| metadata_size               | 0x00000078  |
| metadata_size_qti           | 0x00000000  |
| sig_ptr                     | 0x8f800248  |
| sig_size                    | 0x00000100  |
| sig_size_qti                | 0x00000000  |
| version                     | 0x00000006  |
Metadata:
| anti_rollback_version        | 0x00000000  |
| app_id                       | 0x00000000  |
| debug                        | 0x00000001  |
| hw_id                        | 0x00000000  |
| in_use_soc_hw_version        | 0x00000001  |
| model_id                     | 0x00000000  |
| mrc_index                    | 0x00000000  |
| multi_serial_numbers         | 0x00000000  |
| oem_id                       | 0x00000000  |
| oem_id_independent           | 0x00000000  |
| root_revoke_activate_enable  | 0x00000000  |
| rot_en                       | 0x00000000  |
| soc_vers                     | 0x00006003  |
| sw_id                        | 0x00000002  |
| uie_key_switch_enable        | 0x00000000  |
| use_serial_number_in_signing | 0x00000000  |


