#!/usr/bin/perl

# This script will remove debug symbols from the given binary and store them in 
# a separate file with the binary name + build_id + ".sym". This file name will
# be added to the original binary so that gdb can find them as if they were 
# still included in the binary itself.
#
# $Id: //deploy/qcom/qct/platform/qnp/qnx/Auto/dev/2_0/qnx_ap/strip-debug-and-store.pl#1 $

use File::Basename;
use Getopt::Std;

use warnings;
use strict;

my $self = basename($0);
my $objcopy = "ntoaarch64-objcopy";



sub usage() {
	print <<__EOF__;

Remove debug symbols and store in a separate file

usage: $self file_list


__EOF__
}



open(INFIL,$ARGV[0]) or die ("$self: Error: Unable to open " . $ARGV[0] . " for reading\n");

while (<INFIL>) {
	chomp;
	
#	if (! -r $_ ) {
#		warn "$self: Error: Could not read file \'$_\'";
#	} 
	
   if ($ARGV[0] eq "appslist.txt")
   {
      my $cmd = "";
       
#	   $cmd = "cp  $_ $_.sym";	
	   $cmd = "cp ./install/aarch64le/bin/$_ ./install/aarch64le/base/fyapps/apps";
           system($cmd);
#		$cmd = "$objcopy --strip-all -R.ident $_\n";
#		system($cmd);
#		$cmd = "$objcopy --add-gnu-debuglink=$_.sym $_\n";
#		system($cmd);
		
   }	
   
    if ($ARGV[0] eq "midslist.txt")
   {
      my $cmd = "";
       
#	   $cmd = "cp  $_ $_.sym";	
	   $cmd = "cp ./install/aarch64le/bin/$_ ./install/aarch64le/base/fyapps/mids";
           system($cmd);
#		$cmd = "$objcopy --strip-all -R.ident $_\n";
#		system($cmd);
#		$cmd = "$objcopy --add-gnu-debuglink=$_.sym $_\n";
#		system($cmd);
		
   }

   if ($ARGV[0] eq "libslist.txt")
   {
      my $cmd = "";
       
	  $cmd = "cp ./install/aarch64le/lib/lib$_.so ./install/aarch64le/base/fyapps/libs";
          system($cmd);	
   }

   if ($ARGV[0] eq "libslist.txt")
   {
      my $cmd = "";
       
	  $cmd = "cp ./install/aarch64le/lib/lib$_.so ./install/aarch64le/base/fyapps/libs/lib$_.so.1";
          system($cmd);	
   }    
}
close INFIL

