OEM signed image with RSAPSS
QTI PROD signed image with RSAPSS
ERROR: Following validations failed for the image:
       1. Following signing attributes do not match: 
          | Attribute | Image | Config |
          |-----------|-------|--------|
          |   debug   |  0x1  |  0x0   |
          
Image /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Qualcomm8155Pool0/hqx1.2.1.c1_r00004.2/amss/tz_8155/trustzone_images/build/ms/bin/YAQAANAA/tz.mbn is not encrypted

Base Properties: 
| Integrity Check             | True  |
| Signed                      | True  |
| Encrypted                   | False |
| Size of signature           | 256   |
| Size of one cert            | 2048  |
| Num of certs in cert chain  | 3     |
| Number of root certs        | 1     |
| Maximum number of root certs| 4     |
| Cert chain size             | 12288 |

ELF Properties: 
Header: 
| Magic                      | ELF                           |
| Class                      | ELF64                          |
| Data                       | 2's complement, little endian  |
| Version                    | 1 (Current)                    |
| OS/ABI                     | GNU                            |
| ABI Version                | 0                              |
| Type                       | EXEC (Executable file)         |
| Machine                    | 183                            |
| Version                    | 0x1                            |
| Entry address              | 0x14680000                     |
| Program headers offset     | 0x00000040                     |
| Section headers offset     | 0x00000000                     |
| Flags                      | 0x00000000                     |
| ELF header size            | 64                             |
| Program headers size       | 56                             |
| Number of program headers  | 21                             |
| Section headers size       | 0                              |
| Number of section headers  | 0                              |
| String table section index | 0                              |

Program Headers: 
| Num | Type | Offset | VirtAddr | PhysAddr | FileSize | MemSize | Flags | Align |
|-----|------|--------|----------|----------|----------|---------|-------|-------|
|  1  | LOAD |0x007000|0x14680000|0x14680000| 0x0b250  | 0x0b250 |  RWE  | 0x1000|
|  2  | LOAD |0x013000|0x1468c000|0x1468c000| 0x02248  | 0x02248 |   RW  | 0x1000|
|  3  | LOAD |0x016000|0x14690000|0x14690000| 0x017c8  | 0x017c8 |  RWE  | 0x1000|
|  4  | LOAD |0x018000|0x14692000|0x14692000| 0x01790  | 0x01790 |   RW  | 0x1000|
|  5  | LOAD |0x01a000|0x1c018000|0x1c018000| 0x07d00  | 0x07d00 |   RE  | 0x1000|
|  6  | LOAD |0x022000|0x1c020000|0x1c020000| 0x0c988  | 0x0c988 |   RW  | 0x1000|
|  7  | LOAD |0x02f000|0x1c02d000|0x1c02d000| 0x0a000  | 0x0a000 |   RW  | 0x1000|
|  8  | LOAD |0x039000|0x1c03a000|0x1c03a000| 0xdcdaa  | 0xdcdaa |  RWE  | 0x1000|
|  9  | LOAD |0x116000|0x1c117000|0x1c117000| 0x215e0  | 0x215e0 |   RW  | 0x1000|
|  10 | LOAD |0x138000|0x1c139000|0x1c139000| 0xc3380  | 0xc3380 |   RW  | 0x1000|
|  11 | LOAD |0x1fc000|0x1c1fd000|0x1c1fd000| 0x00000  | 0x00000 |   R   | 0x1000|
|  12 | LOAD |0x1fc000|0x1c281000|0x1c281000| 0x11548  | 0x5d000 |   RW  | 0x1000|
|  13 | LOAD |0x20e000|0x1c2de000|0x1c2de000| 0x10000  | 0x10000 |   RW  | 0x1000|
|  14 | LOAD |0x21e000|0x1c2ee000|0x1c2ee000| 0x10000  | 0x10000 |   RW  | 0x1000|
|  15 | LOAD |0x22e000|0x1c300000|0x1c300000| 0x000c5  | 0x000c5 |   R   | 0x1000|
|  16 | LOAD |0x22f000|0x1c301000|0x1c301000| 0x55550  | 0x55550 |   RE  | 0x4   |
|  17 | LOAD |0x285000|0x1c357000|0x1c357000| 0x00ad0  | 0x00ad0 |   RW  | 0x4   |
|  18 | LOAD |0x286000|0x1c358000|0x1c358000| 0x05b84  | 0x05b84 |   RW  | 0x4   |
|  19 | LOAD |0x28c000|0x1c35e000|0x1c35e000| 0x6e600  | 0x6e600 |   RE  | 0x1000|
|  20 | LOAD |0x2fb000|0x1c3cd000|0x1c3cd000| 0x012b8  | 0x012b8 |   RW  | 0x1000|
|  21 | LOAD |0x2fd000|0x1c3cf000|0x1c3cf000| 0x07e3e  | 0x07e3e |   RW  | 0x1000|

Hash Segment Properties: 
| Header Size     | 288B  |
| Hash Algorithm  | sha384|

Header: 
| cert_chain_ptr              | 0xffffffff  |
| cert_chain_size             | 0x00003000  |
| cert_chain_size_qti         | 0x00001800  |
| code_size                   | 0x00000450  |
| image_id                    | 0x00000019  |
| image_size                  | 0x00004f50  |
| metadata_major_version      | 0x00000000  |
| metadata_major_version_qti  | 0x00000000  |
| metadata_minor_version      | 0x00000000  |
| metadata_minor_version_qti  | 0x00000000  |
| metadata_size               | 0x00000078  |
| metadata_size_qti           | 0x00000078  |
| sig_ptr                     | 0xffffffff  |
| sig_size                    | 0x00000100  |
| sig_size_qti                | 0x00000100  |
| version                     | 0x00000006  |
Metadata:
| anti_rollback_version        | 0x00000000  |
| app_id                       | 0x00000000  |
| debug                        | 0x00000001  |
| hw_id                        | 0x00000000  |
| in_use_soc_hw_version        | 0x00000001  |
| model_id                     | 0x00000000  |
| mrc_index                    | 0x00000000  |
| multi_serial_numbers         | 0x00000000  |
| oem_id                       | 0x00000001  |
| oem_id_independent           | 0x00000001  |
| root_revoke_activate_enable  | 0x00000000  |
| rot_en                       | 0x00000000  |
| soc_vers                     | 0x00006003  |
| sw_id                        | 0x00000007  |
| uie_key_switch_enable        | 0x00000000  |
| use_serial_number_in_signing | 0x00000000  |
Metadata QTI:
| anti_rollback_version        | 0x00000000  |
| app_id                       | 0x00000000  |
| debug                        | 0x00000001  |
| hw_id                        | 0x00000000  |
| in_use_soc_hw_version        | 0x00000001  |
| model_id                     | 0x00000000  |
| mrc_index                    | 0x00000000  |
| multi_serial_numbers         | 0x00000000  |
| oem_id                       | 0x00000001  |
| oem_id_independent           | 0x00000001  |
| root_revoke_activate_enable  | 0x00000000  |
| rot_en                       | 0x00000000  |
| soc_vers                     | 0x00006003  |
| sw_id                        | 0x00000007  |
| uie_key_switch_enable        | 0x00000000  |
| use_serial_number_in_signing | 0x00000000  |


