#! /bin/bash

output_name=$1
OUTPUT=$2
TARGET_XML_FLIE=$OUTPUT/output/errors/index.xml
filter=$OUTPUT/chectout
model_error_output=${output_name}_error_output.csv
today=`date -d today +"%Y/%m/%d"`
applist=${OUTPUT}/App.list
prompt ()
{
	echo "======>$1" 
}

echo model,date,errornum  > ${OUTPUT}/${model_error_output}

checkfile () 
{
	if test -d $OUTPUT/chectout;then
		echo "chectout exist"
	else
		mkdir $OUTPUT/chectout
	fi
	AppNum=`wc -l ${applist} | cut -d' ' -f1`
	for i in `seq 1 $AppNum`
	do
		appname=`head -$i ${applist} | tail -1`
		grep -E -o -e  "/${appname}/" $TARGET_XML_FLIE | sed 's/<file>//g'  > $OUTPUT/chectout/${appname}
		num=`wc -l $OUTPUT/chectout/${appname} | awk '{print $1}'`
		echo ${appname},${today},${num} >> ${OUTPUT}/${model_error_output}
	done
	
	return 0
}

#check augument.
if [ ! $# == 2 ]; then 
	prompt "Usage: $0 projectname path" 
	exit 1
fi

checkfile
if [ $? -ne 0 ] ;then
	exit 1
fi

rm -rf $OUTPUT/chectout

exit 0

