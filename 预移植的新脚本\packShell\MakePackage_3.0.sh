#! /bin/bash 
#****************************
# �����������ű�
# �����������ű�
# Authot xizhuang.wu
# version 3.0
#****************************

#�����ȳ�����Ҫ�ֶ��ϴ�ά��
LOCAL_AILABI_PATH=/mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/Ailabi_Package

Qualcomm8155BashPath=/mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155
Pak_Shell_Path="http://10.2.4.101/svn/Qualcomm/02_SA8155P/01_HS7001A/02_CodeLib/01_MainPath/1.2/02_Android/04_Shell/buildShell3.0/packShell"
#0 ����ɹ�    1 ��׿����ʧ��   2 QNX����ʧ��   3 QNX��׿�����뱨��   4 �����ȴ������
PackageResum=0

#����������   
#            27 �������ռ䲻��      28 SVN����
ERROR_PARAM_SPACE=27
ERROR_PARAM_SVN=28
ERROR_XML_MCU=33
ERROR_XML_PACK=34
isBuildSecure="0"
isBuildXuGao="0"

while getopts "v:p:t:y:r:c:a:u:f:z:q:o:h:s:x:" opt
do
    case $opt in
            v ) echo "v "
            	projectversion=$OPTARG;;
            p ) echo "p "
            	projectname=$OPTARG;;
		    t ) echo "t "
            	buildType=$OPTARG;;
			y ) echo "y "
			    covRet=$OPTARG;;
		    r ) echo "r "
			    packageType=$OPTARG;;
			c ) echo "c "
			    isBuildC1=$OPTARG;;
			a ) echo "a "
                build_amss=$OPTARG;;
			u ) echo "u "
			    isBuildUFS=$OPTARG;;
		    f ) echo "f "
			    issueVersion=$OPTARG;;
			z ) echo "z "
			    Out6W6CDir=$OPTARG;;
			q ) echo "q "
			    PACKAGE_BUILD_DATE=$OPTARG;;
			o ) echo "o "
			    PackageResum=$OPTARG;;
			h ) echo "h "
			    BuildDateTime=$OPTARG;;
			s ) echo "s "
			    isBuildSecure=$OPTARG;;
			x ) echo "x "
			    isBuildXuGao=$OPTARG;;
		    ? ) echo "$0 error argument."
			exit 1;;
    esac
done

_save_build_param ()
{
	echo "JENKINS_PRJNAME=$projectname" > $LOCAL_PATH/JenkinsBuildParam.txt
	echo "JENKINS_SVN_VERSION=$projectversion" >> $LOCAL_PATH/JenkinsBuildParam.txt
	echo "JENKINS_SVN_INT_VERSION=$JENKINS_SVN_INT_VERSION" >> $LOCAL_PATH/JenkinsBuildParam.txt
	echo "JENKINS_SVN_TAG_PATH=$JENKINS_SVN_TAG_PATH" >> $LOCAL_PATH/JenkinsBuildParam.txt
	echo "JENKINS_MODEL_NAME=$JENKINS_MODEL_NAME" >> $LOCAL_PATH/JenkinsBuildParam.txt
	echo "JENKINS_WORK_ORDER=$build_amss" >> $LOCAL_PATH/JenkinsBuildParam.txt
	echo "JENKINS_ISNEED_COV_BUILD=$covRet" >> $LOCAL_PATH/JenkinsBuildParam.txt
	echo "JENKINS_SW1_NAME=$buildType" >> $LOCAL_PATH/JenkinsBuildParam.txt
	echo "JENKINS_SW2_NAME=$issueVersion" >> $LOCAL_PATH/JenkinsBuildParam.txt
	echo "JENKINS_SW3_NAME=$packageType" >> $LOCAL_PATH/JenkinsBuildParam.txt
	echo "JENKINS_BUILD_C1=$isBuildC1" >> $LOCAL_PATH/JenkinsBuildParam.txt
	echo "JENKINS_BUILD_UFS=$isBuildUFS" >> $LOCAL_PATH/JenkinsBuildParam.txt
	echo "JENKINS_BUILD_SECURE=$isBuildSecure" >> $LOCAL_PATH/JenkinsBuildParam.txt
	echo "JENKINS_BUILD_XUGAO=$isBuildXuGao" >> $LOCAL_PATH/JenkinsBuildParam.txt
	echo "JENKINS_BUILD_DATETIME=$BuildDateTime" >> $LOCAL_PATH/JenkinsBuildParam.txt
	echo "PackageResum=$PackageResum" >> $LOCAL_PATH/JenkinsBuildParam.txt
}

LOCAL_PATH=$Qualcomm8155BashPath/${projectname}_Package
LOCAL_PACK_SHELL_PATH=$LOCAL_PATH/${Pak_Shell_Path##*/}
LOCAL_ISO_PATH=$LOCAL_PATH/${Pak_Shell_Path##*/}/iso_package
LOCAL_XML_PATH=$LOCAL_PATH/${Pak_Shell_Path##*/}/xml_package

IMGESCOMMIT_PATH=/mnt/new_disk/jenkins/jenkins_output/SA8155/$projectname
PAR_COLLECT_PATH=/mnt/new_disk/jenkins/jenkins_output/SA8155/CollectBuild
RUNING_ERROR_PARAM=0

Project_XML_Config=http://10.2.4.101/svn/Qualcomm/02_SA8155P/${projectname}/02_CodeLib/01_ProjectPath/03_Config
Base_XML_Config="http://10.2.4.101/svn/Qualcomm/02_SA8155P/01_HS7001A/02_CodeLib/01_MainPath/1.2/03_Config"

function _clean_before_exit ()
{
    echo "Clean $LOCAL_PATH/${Out6W6CDir} and output to 6w6c before exit ........"
    if [[ ! -d ${IMGESCOMMIT_PATH} ]]; then
       mkdir -p ${IMGESCOMMIT_PATH}
	fi
	
	cd $IMGESCOMMIT_PATH
	if [[ $? -eq 0 ]];then
	    find ./ -mtime +9 -type d -maxdepth 1 > $LOCAL_PATH/deleDepth
		while read -r dirTem
        do 
		    dirTemDele=${dirTem##*/}
            if [[ -d /mnt/new_disk/jenkins/jenkins_output/SA8155/$projectname/$dirTemDele ]] && [[ $projectname != "" ]] && [[ $dirTemDele != "" ]];then
			    echo "/mnt/new_disk/jenkins/jenkins_output/SA8155/$projectname/$dirTemDele Dir is maked over 30 days, delete ........"
				rm -rf /mnt/new_disk/jenkins/jenkins_output/SA8155/$projectname/$dirTemDele
			fi
        done < $LOCAL_PATH/deleDepth
	fi

	ls -l $LOCAL_PATH/${Out6W6CDir}
	echo "OutPut Update ......  cp -rf $Qualcomm8155BashPath/${projectname}_Package/${Out6W6CDir} ${IMGESCOMMIT_PATH}/${Out6W6CDir}"
	cp -rf $LOCAL_PATH/${Out6W6CDir} ${IMGESCOMMIT_PATH}/${Out6W6CDir}
	outReum=$?
	ls -l ${IMGESCOMMIT_PATH}/${Out6W6CDir}
	
	
	if [[ $LOCAL_PATH/$Out6W6CDir =~ $Qualcomm8155BashPath ]] && [[ $projectname != "" ]] && [[ $Out6W6CDir != "" ]] && [[ -d $LOCAL_PATH ]];then
	    echo "rm -rf $LOCAL_PATH/${Out6W6CDir}"
	    rm -rf $LOCAL_PATH/${Out6W6CDir}
	fi
	
	# ���ailabi���Ŀ¼
	if [[ $projectname != "" ]] && [[ -d $LOCAL_AILABI_PATH/WholeUpdateZip/${projectname} ]];then
		rm -rf $LOCAL_AILABI_PATH/WholeUpdateZip/${projectname}
	fi
	
	return $outReum
}

function _build_error_exit ()
{
    EXIT_ERROR_PARAM=$1
	_clean_before_exit
	echo "Make Package Error,Error param is $EXIT_ERROR_PARAM,exit ........"
	exit $EXIT_ERROR_PARAM
}

function set_version()
{
	CarType=`$LOCAL_PACK_SHELL_PATH/readXMLFile.sh Qnx ${projectname} Package CarType`
	if [[ $CarType = "Beiqi" ]];then
		sv=`echo ${projectversion//./} | awk -F 'T' '{print $1}'`
		tv="T"`echo ${projectversion//./} | awk -F 'T' '{print $2}'`
   
		ControlSys=`$LOCAL_PACK_SHELL_PATH/readXMLFile.sh Qnx ${projectname} Package Version ControlSys`
		PN=`$LOCAL_PACK_SHELL_PATH/readXMLFile.sh Qnx ${projectname} Package Version PN`
		PNB=`$LOCAL_PACK_SHELL_PATH/readXMLFile.sh Qnx ${projectname} Package Version PNB`
		SoftNum=`$LOCAL_PACK_SHELL_PATH/readXMLFile.sh Qnx ${projectname} Package Version SoftNum`
		ProjectNum=`$LOCAL_PACK_SHELL_PATH/readXMLFile.sh Qnx ${projectname} Package Version ProjectNum`
   
		echo "Package Log....."
		echo "$LOCAL_PACK_SHELL_PATH/readXMLFile.sh    ControlSys = $ControlSys   PN = $PN   SoftNum = $SoftNum   ProjectNum = $ProjectNum"
   
		if [[ "${issueVersion}" = "0" ]];then
			updatePackage=${ControlSys}${PN}${sv}${SoftNum}${ProjectNum}${tv}${PACKAGE_BUILD_DATE:2}
			if [[ $PNB != "" ]];then
				updatePackageB=${ControlSys}${PNB}${sv}${SoftNum}${ProjectNum}${tv}${PACKAGE_BUILD_DATE:2}
			fi
		else
			updatePackage=${ControlSys}${PN}${sv}${SoftNum}${ProjectNum}
			if [[ $PNB != "" ]];then
				updatePackageB=${ControlSys}${PNB}${sv}${SoftNum}${ProjectNum}
			fi
		fi
	else
		if [[ ${isBuildSecure} == "1" ]];then
			updatePackage=Cockpit_${projectname%%_*}_${projectversion}.${PACKAGE_BUILD_DATE:2}_safety
		else
			updatePackage=Cockpit_${projectname%%_*}_${projectversion}.${PACKAGE_BUILD_DATE:2}
		fi
		
	fi
}

_count_summd5 ()
{
	InPackage=$1
	
    md5Android=`md5sum Android.tar.gz`
	md5QNX=`md5sum QNX.tar.gz`
	md5AMSS=`md5sum AMSS.tar.gz`
	if [[ -f ./mcu.bin ]];then
	   md5MCU=`md5sum mcu.bin`
	else
	   md5MCU=`md5sum MCU.bin`
	fi
	md5FOURG=`md5sum 4G_update.iso`
	md5FOURGhs7023a=`md5sum 4G_update_hs7023a.iso`
	md5FOURGhs7024a=`md5sum 4G_update_hs7024a.iso`
	md5ISO=`md5sum Adayo.iso`
	md5BIN=`md5sum Adayo.bin`
	
	summAndroid=`echo ${md5Android:0:32}`
	summQNX=`echo ${md5QNX:0:32}`
	summAMSS=`echo ${md5AMSS:0:32}`
	summMCU=`echo ${md5MCU:0:32}`
	summFOURG=`echo ${md5FOURG:0:32}`
	summFOURGhs7023a=`echo ${md5FOURGhs7023a:0:32}`
	summFOURGhs7024a=`echo ${md5FOURGhs7024a:0:32}`
	summISO=`echo ${md5ISO:0:32}`
	summBIN=`echo ${md5BIN:0:32}`
	
	if [[ -f ./DAB_APP_ForHu.bin ]];then
		md5DAB=`md5sum DAB_APP_ForHu.bin`
		summDAB=`echo ${md5DAB:0:32}`
	fi
	
	if [[ -f ./MCU_RH850.bin ]];then
		md5RH850=`md5sum MCU_RH850.bin`
		summRH850=`echo ${md5RH850:0:32}`
	fi
	
	md5Line=`grep -n "md5" version.cfg | awk -F ":" '{print $1}'`
	
	if [[ $projectname =~ 535B ]];then
		cubProjetctName=${projectname%%_535B*}
		cubProjetctName=${cubProjetctName}535B
	else
		cubProjetctName=${projectname%%_*}
	fi
	
	CarType=`$LOCAL_PACK_SHELL_PATH/readXMLFile.sh Qnx ${projectname} Package CarType`
	if [[ $CarType = "Changan" ]];then
		sed -i "s/\"version\":\"V0.01.04\",/\"version\":\"${InPackage}\",/" ./version.cfg
		sed -i "s/\"model\":\"HS7001A\",/\"model\":\"${cubProjetctName}\",/" ./version.cfg
		sed -i "s/\"isSafety\":\"0\",/\"isSafety\":\"${isBuildSecure}\",/" ./version.cfg
		sed -i "s/{\"ecuid\":\"HUT\",\"version\":\"V0.01.04\"},/{\"ecuid\":\"HUT\",\"version\":\"${projectversion}\"},/" ./version.cfg
		sed -i "s/{\"ecuid\":\"IC\",\"version\":\"V0.01.04\"}/{\"ecuid\":\"IC\",\"version\":\"${projectversion}\"}/" ./version.cfg
		sed -i "s/\"HS7001A\",\"HS7001B\"/\"${cubProjetctName}\"/" ./version.cfg
	else
		sed -i "2s/\"version\":\"V0.01.04\",/\"version\":\"${InPackage}\",/" ./version.cfg
		sed -i "3s/\"model\":\"HS7001A\",/\"model\":\"${cubProjetctName}\",/" ./version.cfg
		sed -i "5s/{\"ecuid\":\"HUT\",\"version\":\"V0.01.04\"},/{\"ecuid\":\"HUT\",\"version\":\"${projectversion}\"},/" ./version.cfg
		sed -i "6s/{\"ecuid\":\"IC\",\"version\":\"V0.01.04\"}/{\"ecuid\":\"IC\",\"version\":\"${projectversion}\"}/" ./version.cfg
		sed -i "9s/\"HS7001A\",\"HS7001B\"/\"${cubProjetctName}\"/" ./version.cfg
	fi
	
	countNum=0
	
	if [[ -f Android.tar.gz ]];then
	   countNum=$((countNum+1))
	   sed -i "$md5Line a{\"type\":\"ANDROID\",\"md5\":\"$summAndroid\"}," ./version.cfg
	fi
	
	if [[ -f QNX.tar.gz ]];then
	   countNum=$((countNum+1))
	   sed -i "$md5Line a{\"type\":\"QNX\",\"md5\":\"$summQNX\"}," ./version.cfg
	fi
	
	if [[ -f mcu.bin ]] || [[ -f MCU.bin ]];then
	   countNum=$((countNum+1))
	   sed -i "$md5Line a{\"type\":\"MCU\",\"md5\":\"$summMCU\"}," ./version.cfg
	fi
	
	if [[ -f DAB_APP_ForHu.bin ]];then
	   countNum=$((countNum+1))
	   sed -i "$md5Line a{\"type\":\"DAB\",\"md5\":\"$summDAB\"}," ./version.cfg
	fi
	
	if [[ -f MCU_RH850.bin ]];then
	   countNum=$((countNum+1))
	   sed -i "$md5Line a{\"type\":\"MCU_RH850\",\"md5\":\"$summRH850\"}," ./version.cfg
	fi
	
	if [[ -f AMSS.tar.gz ]];then
	   countNum=$((countNum+1))
	   sed -i "$md5Line a{\"type\":\"AMSS\",\"md5\":\"$summAMSS\"}," ./version.cfg
	fi
	
	if [[ -f 4G_update.iso ]];then
	   countNum=$((countNum+1))
	   sed -i "$md5Line a{\"type\":\"4G_update\",\"md5\":\"$summFOURG\"}," ./version.cfg
	fi
	
	if [[ -f 4G_update_hs7023a.iso ]];then
	   countNum=$((countNum+1))
	   sed -i "$md5Line a{\"type\":\"4G_update_hs7023a\",\"md5\":\"$summFOURGhs7023a\"}," ./version.cfg
	fi
	
	if [[ -f 4G_update_hs7024a.iso ]];then
	   countNum=$((countNum+1))
	   sed -i "$md5Line a{\"type\":\"4G_update_hs7024a\",\"md5\":\"$summFOURGhs7024a\"}," ./version.cfg
	fi
	
	if [[ -f Adayo.iso ]];then
	   countNum=$((countNum+1))
	   sed -i "$md5Line a{\"type\":\"Adayo\",\"md5\":\"$summISO\"}," ./version.cfg
	fi
	
	if [[ -f Adayo.bin ]];then
	   countNum=$((countNum+1))
	   sed -i "$md5Line a{\"type\":\"AdayoBin\",\"md5\":\"$summBIN\"}," ./version.cfg
	fi
	
	sed -i ''$((countNum+md5Line))'s/.$//g' ./version.cfg 
}

_count_summd5_package ()
{
	InPackageAll=$1
	
    md5Package=`md5sum $InPackageAll`
	
	summPackage=`echo ${md5Package:0:32}`
	
	echo "$InPackageAll MD5: $summPackage" >> $LOCAL_PATH/${Out6W6CDir}/JenkinsBuildParam.txt
}

_safe_checkSign ()
{
    checkPath=$1
	InPackage=$2
	
	if [[ $checkPath = "" ]] || [[ ! -d $checkPath ]];then
	   echo "_safe_checkSign $checkPath is not exist !!!!"
	   return
	fi
	cd $checkPath
	
	echo $ProjectNum > $checkPath/SOFTWARE_VER.mbn
	zip -r 1-ICC_A752E01260_V001.zip ./SOFTWARE_VER.mbn
    if [[ -f 1-ICC_A752E01260_V001.zip ]] && [[ -f $checkPath/SOFTWARE_VER.mbn ]];then
       rm -rf $checkPath/SOFTWARE_VER.mbn
    fi
	
	md5UpdateZip2=`md5sum ${InPackage}.zip | awk '{print $1}'`
	md5UpdateZip1=`md5sum 1-ICC_A752E01260_V001.zip | awk '{print $1}'`
	sha256UpdateZip2=`sha256sum ${InPackage}.zip | awk '{print $1}'`
	sha256UpdateZip1=`sha256sum 1-ICC_A752E01260_V001.zip | awk '{print $1}'`
	java -jar ${LOCAL_ISO_PATH}/RSA2048JavaUtil.jar ${LOCAL_ISO_PATH}/private_web.pem ${sha256UpdateZip2}
	isoSign2=`cat ./test.sig`
	java -jar ${LOCAL_ISO_PATH}/RSA2048JavaUtil.jar ${LOCAL_ISO_PATH}/private_web.pem ${sha256UpdateZip1}
	isoSign1=`cat ./test.sig`
	   
	if [[ $? -eq 0 ]];then
	   cp ${LOCAL_ISO_PATH}/5-CRC_SOC_S000001.xml $checkPath/5-CRC_SOC_${sv}.xml
	   sed -ri '9s|<fileName>.*</fileName>|<fileName>'${InPackage}'</fileName>|g' 5-CRC_SOC_${sv}.xml
	   sed -ri '10s|<fileSIG>.*</fileSIG>|<fileSIG>'${isoSign2}'</fileSIG>|g' 5-CRC_SOC_${sv}.xml
	   sed -ri '5s|<fileName>.*</fileName>|<fileName>1-ICC_A752E01260_V001</fileName>|g' 5-CRC_SOC_${sv}.xml
	   sed -ri '6s|<fileSIG>.*</fileSIG>|<fileSIG>'${isoSign1}'</fileSIG>|g' 5-CRC_SOC_${sv}.xml
	   rm -rf ./test.sig
	else
	   echo "RSA2048JavaUtil.jar sign error !!!!!"
	fi
}

_output_extra ()
{
    RUNING_ERROR_PARAM=$ERROR_PARAM_SPACE
	if [[ ! -d $LOCAL_PATH/${Out6W6CDir} ]];then mkdir -p $LOCAL_PATH/${Out6W6CDir};fi
	_save_build_param
	if [[ -f $LOCAL_PATH/JenkinsBuildParam.txt ]];then
		cp -f $LOCAL_PATH/JenkinsBuildParam.txt $LOCAL_PATH/${Out6W6CDir}
	fi
	case $PackageResum in
		4)
		    cp $LOCAL_PATH/error_ota_${projectname}_* $LOCAL_PATH/${Out6W6CDir}
			mkdir -p $LOCAL_PATH/${Out6W6CDir}/images/{android,qnx,amss}
			mkdir -p $LOCAL_PATH/${Out6W6CDir}/output
			for i in `seq 1 6`
            do
			    if [[ $i -eq 1 ]];then cp -rf $LOCAL_PATH/Image/Qnx/* $LOCAL_PATH/${Out6W6CDir}/images/qnx;fi
			    if [[ $i -eq 2 ]];then cp -rf $LOCAL_PATH/Image/AMSS/* $LOCAL_PATH/${Out6W6CDir}/images/amss;fi
				if [[ $i -eq 3 ]];then cp -rf $LOCAL_PATH/Image/Android/* $LOCAL_PATH/${Out6W6CDir}/images/android;fi
				if [[ $i -eq 4 ]];then cp -rf $LOCAL_PATH/output/Qnx $LOCAL_PATH/${Out6W6CDir}/output;fi
				if [[ $i -eq 5 ]];then cp -rf $LOCAL_PATH/output/wlanSymbol $LOCAL_PATH/${Out6W6CDir}/output;fi
				if [[ $i -eq 6 ]];then cp -rf $LOCAL_PATH/output/Android $LOCAL_PATH/${Out6W6CDir}/output;fi
			    if [[ $? -ne 0 ]];then _build_error_exit $RUNING_ERROR_PARAM;fi
            done
		;;
		3)
		    for i in `seq 1 2`
            do
			    if [[ $i -eq 1 ]];then cp $LOCAL_PATH/error_qnx_${projectname}_* $LOCAL_PATH/${Out6W6CDir};fi
			    if [[ $i -eq 2 ]];then cp $LOCAL_PATH/error_android_${projectname}_* $LOCAL_PATH/${Out6W6CDir};fi
			    if [[ $? -ne 0 ]];then _build_error_exit $RUNING_ERROR_PARAM;fi
            done
		;;
		1)
			cp $LOCAL_PATH/error_android_${projectname}_* $LOCAL_PATH/${Out6W6CDir}
			mkdir -p $LOCAL_PATH/${Out6W6CDir}/images/{qnx,amss}
            mkdir -p $LOCAL_PATH/${Out6W6CDir}/output
			for i in `seq 1 4`
            do
			    if [[ $i -eq 1 ]];then cp -rf $LOCAL_PATH/Image/Qnx/* $LOCAL_PATH/${Out6W6CDir}/images/qnx;fi
			    if [[ $i -eq 2 ]];then cp -rf $LOCAL_PATH/Image/AMSS/* $LOCAL_PATH/${Out6W6CDir}/images/amss;fi
				if [[ $i -eq 3 ]];then cp -rf $LOCAL_PATH/output/Qnx $LOCAL_PATH/${Out6W6CDir}/output;fi
				if [[ $i -eq 4 ]];then cp -rf $LOCAL_PATH/output/wlanSymbol $LOCAL_PATH/${Out6W6CDir}/output;fi
			    if [[ $? -ne 0 ]];then _build_error_exit $RUNING_ERROR_PARAM;fi
            done
		;;
		2)
		    cp $LOCAL_PATH/error_qnx_${projectname}_* $LOCAL_PATH/${Out6W6CDir}
			mkdir -p $LOCAL_PATH/${Out6W6CDir}/images/android
			mkdir -p $LOCAL_PATH/${Out6W6CDir}/output
			for i in `seq 1 2`
            do
			    if [[ $i -eq 1 ]];then cp -rf $LOCAL_PATH/Image/Android/* $LOCAL_PATH/${Out6W6CDir}/images/android;fi
			    if [[ $i -eq 2 ]];then cp -rf $LOCAL_PATH/output/Android $LOCAL_PATH/${Out6W6CDir}/output;fi
			    if [[ $? -ne 0 ]];then _build_error_exit $RUNING_ERROR_PARAM;fi
            done
		;;
		*)
		    mkdir -p $LOCAL_PATH/${Out6W6CDir}/images/{android,qnx,amss}
            mkdir -p $LOCAL_PATH/${Out6W6CDir}/output
			for i in `seq 1 6`
            do
			    if [[ $i -eq 1 ]];then cp -rf $LOCAL_PATH/Image/Qnx/* $LOCAL_PATH/${Out6W6CDir}/images/qnx;fi
			    if [[ $i -eq 2 ]];then cp -rf $LOCAL_PATH/Image/AMSS/* $LOCAL_PATH/${Out6W6CDir}/images/amss;fi
				if [[ $i -eq 3 ]];then cp -rf $LOCAL_PATH/Image/Android/* $LOCAL_PATH/${Out6W6CDir}/images/android;fi
				if [[ $i -eq 4 ]];then cp -rf $LOCAL_PATH/output/Qnx $LOCAL_PATH/${Out6W6CDir}/output;fi
				if [[ $i -eq 5 ]];then cp -rf $LOCAL_PATH/output/wlanSymbol $LOCAL_PATH/${Out6W6CDir}/output;fi
				if [[ $i -eq 6 ]];then cp -rf $LOCAL_PATH/output/Android $LOCAL_PATH/${Out6W6CDir}/output;fi
			    if [[ $? -ne 0 ]];then _build_error_exit $RUNING_ERROR_PARAM;fi
            done
		;;
	esac
}

_update_package ()
{
    RUNING_ERROR_PARAM=$ERROR_PARAM_SPACE
	upPackageType=$1
	MCUType=$2
	Path=$3
	FourGUpdateType=$4
	Package=$5
	packagePath=$Path/$Package
	
	echo "begin package updatePackage !!!  upPackageType=$upPackageType MCUType=$MCUType packagePath=$packagePath"
	mkdir -p $packagePath
	cd $packagePath
	if [[ $upPackageType = "3" ]];then
	    echo "$upPackageType = 3 do not thing ....."
	elif [[ $upPackageType = "2" ]] || [[ $upPackageType = "5" ]];then
	    cp -rf $LOCAL_AILABI_PATH/WholeUpdateZip/${projectname}/{AilabiAndroidWholeUpdate.zip,AilabiQnxWholeUpdate.zip} $packagePath
	else
	    cp -rf $LOCAL_PATH/Update/* $packagePath
	fi
	
	if [[ $MCUType -eq 0 ]];then
		rsync -rv --exclude='DAB_APP_ForHu.bin' --exclude='MCU_RH850.bin' $LOCAL_PATH/SVN_MCU_UPDATE/*.bin $packagePath
		if [[ $upPackageType = "3" ]];then cp -f $LOCAL_PATH/SVN_MCU/mcu_post.sh $packagePath/post.sh;fi
	else
	    svn update $LOCAL_PATH/SVN_MCU_UPDATE --username gjzhao --password gjz123 --no-auth-cache --trust-server-cert
	    if [ $? -ne 0 ];then
		    echo "SVN update SVN_MCU_UPDATE ERROR,exit 2 !!!!!!!"
	    else
	        echo "SVN update SVN_MCU_UPDATE Success !!!!!!!"
	    fi

		rsync -rv --exclude='DAB_APP_ForHu.bin' --exclude='MCU_RH850.bin' $LOCAL_PATH/SVN_MCU_UPDATE/*.bin $packagePath

		if [[ $upPackageType = "3" ]];then cp -f $LOCAL_PATH/SVN_MCU_UPDATE/mcu_post.sh $packagePath/post.sh;fi
	fi
	
	if [[ $upPackageType = "3" ]];then
	    mkdir -p ${LOCAL_PATH}/XMLInput/tmp
		mv $packagePath/{*.bin,post.sh} ${LOCAL_PATH}/XMLInput/tmp
		cd ${LOCAL_PATH}/XMLInput
		tar -czf MCU.tgz tmp
		rm -rf ${LOCAL_PATH}/XMLInput/tmp
		
		cp -f $LOCAL_PATH/Image/Android/* ${LOCAL_PATH}/XMLInput
		cp -f $LOCAL_PATH/Image/Qnx/* ${LOCAL_PATH}/XMLInput
		cp -f $LOCAL_PATH/Image/AMSS/* ${LOCAL_PATH}/XMLInput
		
		RUNING_ERROR_PARAM=$ERROR_XML_MCU
	    mkdir -p ${LOCAL_XML_PATH}/XmlConfig
		svn --force export ${Project_XML_Config}/update.xml ${LOCAL_XML_PATH}/XmlConfig --username gjzhao --password gjz123 --no-auth-cache --trust-server-cert
		if [[ $? -ne 0 ]];then
		    echo "svn --force export $Project_XML_Config/update.xml ${LOCAL_XML_PATH}/XmlConfig Error !!!!!"
			svn --force export ${Base_XML_Config}/update.xml ${LOCAL_XML_PATH}/XmlConfig --username gjzhao --password gjz123 --no-auth-cache --trust-server-cert
		fi
	    if [[ $? -ne 0 ]];then
			_build_error_exit $RUNING_ERROR_PARAM
		fi
		RUNING_ERROR_PARAM=$ERROR_XML_PACK
	    chmod 777 ${LOCAL_XML_PATH}/buildBin
		${LOCAL_XML_PATH}/buildBin --type=img-to-bin --update=${LOCAL_PATH}/XMLInput --config=${LOCAL_XML_PATH}/XmlConfig/update.xml --key=${LOCAL_XML_PATH}/rsa_aes_private.key --output=$packagePath/Adayo.iso
		buildBinParam=$?
		if [[ $buildBinParam -ne 0 ]];then
			echo "Build BinPackage Errror,Param is $buildBinParam !!!!!!"
			_build_error_exit $RUNING_ERROR_PARAM
		fi
		rm -f ${LOCAL_PATH}/XMLInput/*
	fi
	
	if [[ $upPackageType = "1" ]] || [[ $upPackageType = "2" ]];then
	    cd $packagePath
	    chmod 777 ${LOCAL_ISO_PATH}/installer
	    ${LOCAL_ISO_PATH}/installer -c 1 -i $packagePath -o $packagePath -p Adayo -k ${LOCAL_ISO_PATH}/public.key.aes
	    if [[ $packagePath != "" ]] && [[ $packagePath != "/" ]];then
	        echo "find $packagePath -not \( -name 'Adayo.iso' \) -delete"
		    find $packagePath -not \( -name 'Adayo.iso' \) -delete
	    fi
	fi
	
	if [[ $upPackageType = "4" ]];then
		RUNING_ERROR_PARAM=$ERROR_XML_PACK
		mkdir -p ${LOCAL_XML_PATH}/XmlConfig
		svn --force export ${Project_XML_Config}/update-Normal.xml ${LOCAL_XML_PATH}/XmlConfig --username gjzhao --password gjz123 --no-auth-cache --trust-server-cert
		if [[ $? -ne 0 ]];then
		    echo "svn --force export $Project_XML_Config/update-Normal.xml ${LOCAL_XML_PATH}/XmlConfig Error !!!!!"
			svn --force export ${Base_XML_Config}/update-Normal.xml ${LOCAL_XML_PATH}/XmlConfig --username gjzhao --password gjz123 --no-auth-cache --trust-server-cert
		fi
	    if [[ $? -ne 0 ]];then
			echo "svn --force export $Base_XML_Config/update-Normal.xml ${LOCAL_XML_PATH}/XmlConfig Error !!!!!"
			_build_error_exit $RUNING_ERROR_PARAM
		fi
		
	    cd $packagePath
	    chmod 777 ${LOCAL_XML_PATH}/buildBin
	    ${LOCAL_XML_PATH}/buildBin --type=img-to-bin --update=$packagePath --config=${LOCAL_XML_PATH}/XmlConfig/update-Normal.xml --key=${LOCAL_XML_PATH}/rsa_aes_private.key --output=$packagePath/Adayo.bin --cartype=${projectname}
	    buildBinParam=$?
		if [[ $buildBinParam -ne 0 ]];then
			echo "Build BinPackage Errror,Param is $buildBinParam !!!!!!"
			_build_error_exit $RUNING_ERROR_PARAM
		fi
	    if [[ $packagePath != "" ]] && [[ $packagePath != "/" ]];then
	        echo "find $packagePath -not \( -name 'Adayo.bin' \) -delete"
		    find $packagePath -not \( -name 'Adayo.bin' \) -delete
	    fi
	fi
	
	if [[ $upPackageType = "5" ]];then
		RUNING_ERROR_PARAM=$ERROR_XML_PACK
		mkdir -p ${LOCAL_XML_PATH}/XmlConfig
		svn --force export ${Project_XML_Config}/update-Ailabi.xml ${LOCAL_XML_PATH}/XmlConfig --username gjzhao --password gjz123 --no-auth-cache --trust-server-cert
		if [[ $? -ne 0 ]];then
		    echo "svn --force export $Project_XML_Config/update-Ailabi.xml ${LOCAL_XML_PATH}/XmlConfig Error !!!!!"
			svn --force export ${Base_XML_Config}/update-Ailabi.xml ${LOCAL_XML_PATH}/XmlConfig --username gjzhao --password gjz123 --no-auth-cache --trust-server-cert
		fi
	    if [[ $? -ne 0 ]];then
			echo "svn --force export $Base_XML_Config/update-Ailabi.xml ${LOCAL_XML_PATH}/XmlConfig Error !!!!!"
			_build_error_exit $RUNING_ERROR_PARAM
		fi
		
	    cd $packagePath
	    chmod 777 ${LOCAL_XML_PATH}/buildBin
	    ${LOCAL_XML_PATH}/buildBin --type=img-to-bin --update=$packagePath --config=${LOCAL_XML_PATH}/XmlConfig/update-Ailabi.xml --key=${LOCAL_XML_PATH}/rsa_aes_private.key --output=$packagePath/Adayo.bin --cartype=${projectname}
	    buildBinParam=$?
		if [[ $buildBinParam -ne 0 ]];then
			echo "Build BinPackage Errror,Param is $buildBinParam !!!!!!"
			_build_error_exit $RUNING_ERROR_PARAM
		fi
	    if [[ $packagePath != "" ]] && [[ $packagePath != "/" ]];then
	        echo "find $packagePath -not \( -name 'Adayo.bin' \) -delete"
		    find $packagePath -not \( -name 'Adayo.bin' \) -delete
	    fi
	fi
	
	if [[ $FourGUpdateType -eq 0 ]];then
		if [[ -d $LOCAL_PATH/SVN_4G_MODULE ]];then
			if [[ ${projectname} =~ "HS7023A" ]];then
				cp -rf $LOCAL_PATH/SVN_4G_MODULE/4G_update_hs7023a.iso $packagePath
				cp -rf $LOCAL_PATH/SVN_4G_MODULE/4G_update_hs7024a.iso $packagePath
			else
				cp -rf $LOCAL_PATH/SVN_4G_MODULE/4G_update.iso $packagePath
			fi
			echo "The 4G module needs to be packed!"
		fi
	else
		if [[ -d $LOCAL_PATH/SVN_4G_MODULE_UPDATE ]];then
			svn update $LOCAL_PATH/SVN_4G_MODULE_UPDATE --username gjzhao --password gjz123 --no-auth-cache --trust-server-cert
			if [ $? -ne 0 ];then
				echo "SVN update SVN_4G_MODULE_UPDATE ERROR,exit 2 !!!!!!!"
			else
				echo "SVN update SVN_4G_MODULE_UPDATE Success !!!!!!!"
			fi
			if [[ ${projectname} =~ "HS7023A" ]];then
				cp -rf $LOCAL_PATH/SVN_4G_MODULE_UPDATE/4G_update_hs7023a.iso $packagePath
				cp -rf $LOCAL_PATH/SVN_4G_MODULE_UPDATE/4G_update_hs7024a.iso $packagePath
			else
				cp -rf $LOCAL_PATH/SVN_4G_MODULE_UPDATE/4G_update.iso $packagePath
			fi
			echo "The 4G module needs to be packed!"
		fi
	fi
	
	#���android appdata��Դ��Android��Դ-HS7012A��Ŀ��
	if [[ -d $LOCAL_PATH/SVN_LIGHT_SOUND/appdata ]];then
		cp -rf $LOCAL_PATH/SVN_LIGHT_SOUND/appdata $packagePath
		echo "android appdata needs to be packed!"
	fi
	
	#���android Res��Դ��Android��Դ-HS7013A/HS7014A��Ŀ��
	if [[ -d $LOCAL_PATH/Android_Res ]];then
		cp -rf $LOCAL_PATH/Android_Res $packagePath
		echo "android Android_Res needs to be packed!"
	fi
	
	#HS7021A��Ŀ-���DAB_APP_ForHu.bin
	if [[ ${projectname} =~ "HS7021A" ]];then
		if [[ $MCUType -eq 0 ]];then
			cp -rf $LOCAL_PATH/SVN_MCU/DAB_APP_ForHu.bin $packagePath
		else
			cp -rf $LOCAL_PATH/SVN_MCU_UPDATE/DAB_APP_ForHu.bin $packagePath
		fi
	fi
	
	#HS7030A��Ŀ-���MCU_RH850.bin
	if [[ ${projectname} =~ "HS7030A" ]] || [[ ${projectname} =~ "HS7036A" ]] || [[ ${projectname} =~ "HS7037A" ]] || [[ ${projectname} =~ "HS7038A" ]] || [[ ${projectname} =~ "HS7043A" ]];then
		if [[ $MCUType -eq 0 ]];then
			cp -rf $LOCAL_PATH/SVN_MCU/MCU_RH850.bin $packagePath
		else
			cp -rf $LOCAL_PATH/SVN_MCU_UPDATE/MCU_RH850.bin $packagePath
		fi
	fi
	
	cp -rf $LOCAL_PATH/version.cfg $packagePath
	cd $packagePath
	_count_summd5 $Package
	   
	cd $packagePath/../
	zip -r -P 123456 ${Package}.zip ${Package}
	
	if [[ $? -ne 0 ]];then
	    _build_error_exit $RUNING_ERROR_PARAM
	fi
	
	_count_summd5_package ${Package}.zip
	
	if [[ ${projectname} =~ "HS7026A" ]];then
		cp -r ${Package} ${updatePackageB}
		zip -r -P 123456 ${updatePackageB}.zip ${updatePackageB}
		
		if [[ $? -ne 0 ]];then
			_build_error_exit $RUNING_ERROR_PARAM
		else
			rm -rf ${updatePackageB}
		fi
		
		_count_summd5_package ${updatePackageB}.zip
	fi
	
	if [[ $? -ne 0 ]];then
	    _build_error_exit $RUNING_ERROR_PARAM
	else
		if [[ $Qualcomm8155BashPath != "" ]] && [[ $projectname != "" ]] && [[ $Out6W6CDir != "" ]];then
	        rm -rf $packagePath
	    fi
	fi
	
	_safe_checkSign	$Path $Package
    
}

_count_ota_summd5 ()
{
	if [[ ${projectname} =~ "HS7023A" ]];then
		md5MCU=`md5sum THU_MCU@${mcuversion}.zip`
		md5FOURGhs7023a=`md5sum THU_4G_hs7023a@${FourGversionhs7023a}.zip`
		md5FOURGhs7024a=`md5sum THU_4G_hs7024a@${FourGversionhs7024a}.zip`
		md5SO=`md5sum THU_OS@${projectversion}.zip`
	else
		md5MCU=`md5sum THU_MCU@${mcuversion}.bin`
		md5FOURG=`md5sum THU_4G@${FourGversion}.iso`
		md5SO=`md5sum THU_OS@${projectversion}.tar.gz`
	fi
	
	summMCU=`echo ${md5MCU:0:32}`
	if [[ ${projectname} =~ "HS7023A" ]];then
		summFOURGhs7023a=`echo ${md5FOURGhs7023a:0:32}`
		summFOURGhs7024a=`echo ${md5FOURGhs7024a:0:32}`
	else
		summFOURG=`echo ${md5FOURG:0:32}`
	fi
	summSO=`echo ${md5SO:0:32}`
	
	if [[ -f THU_RES@${ResVersion}.zip ]];then
		md5Res=`md5sum THU_RES@${ResVersion}.zip`
		summRes=`echo ${md5Res:0:32}`
		echo "$summRes" >THU_Res_MD5.txt
	fi
	
	if [[ ${projectname} =~ "HS7023A" ]];then
		echo "$summFOURGhs7023a" >THU_4G_MD5_hs7023a.txt
		echo "$summFOURGhs7024a" >THU_4G_MD5_hs7024a.txt
	else
		echo "$summFOURG" >THU_4G_MD5.txt
	fi
	echo "$summMCU" >THU_MCU_MD5.txt
	echo "$summSO" >THU_OS_MD5.txt
	
	if [[ ${projectname} =~ "HS7023A" ]];then
		echo "$FourGversionhs7023a" >THU_4G_version_hs7023a.txt
		echo "$FourGversionhs7024a" >THU_4G_version_hs7024a.txt
	else
		echo "$FourGversion" >THU_4G_version.txt
	fi
	echo "$mcuversion" >THU_MCU_version.txt
	echo "$projectversion" >THU_OS_version.txt
}

_ota_package ()
{
	OnlyPackage=$1
	
	mkdir -p $LOCAL_PATH/${Out6W6CDir}/ota/THU_OS
	mkdir -p $LOCAL_PATH/${Out6W6CDir}/4g_ufs

	if [[ $OnlyPackage -eq 0 ]];then
		mcuversion=`cat $LOCAL_PATH/SVN_MCU/version.txt`
	    cp -rf $LOCAL_PATH/SVN_MCU/*.bin $LOCAL_PATH/${Out6W6CDir}/ota/THU_MCU@${mcuversion}.bin
		if [[ ${projectname} =~ "HS7023A" ]];then
			FourGversionhs7023a=`cat $LOCAL_PATH/SVN_4G_MODULE/version_hs7023a.txt`
			cp -rf $LOCAL_PATH/SVN_4G_MODULE/4G_update_hs7023a.iso $LOCAL_PATH/${Out6W6CDir}/ota/THU_4G_hs7023a@${FourGversionhs7023a}.iso
			cp -rf $LOCAL_PATH/SVN_4G_MODULE/Firehose_hs7023a.zip $LOCAL_PATH/${Out6W6CDir}/4g_ufs
			
			FourGversionhs7024a=`cat $LOCAL_PATH/SVN_4G_MODULE/version_hs7024a.txt`
			cp -rf $LOCAL_PATH/SVN_4G_MODULE/4G_update_hs7024a.iso $LOCAL_PATH/${Out6W6CDir}/ota/THU_4G_hs7024a@${FourGversionhs7024a}.iso
			cp -rf $LOCAL_PATH/SVN_4G_MODULE/Firehose_hs7024a.zip $LOCAL_PATH/${Out6W6CDir}/4g_ufs
			
			cd $LOCAL_PATH/${Out6W6CDir}/ota/
			
			zip -r THU_4G_hs7023a@${FourGversionhs7023a}.zip THU_4G_hs7023a@${FourGversionhs7023a}.iso
			if [[ $? -ne 0 ]];then
				echo "zip THU_4G_hs7023a@${FourGversionhs7023a}.iso fail!"
			else
				rm -rf THU_4G_hs7023a@${FourGversionhs7023a}.iso
			fi
			
			zip -r THU_4G_hs7024a@${FourGversionhs7024a}.zip THU_4G_hs7024a@${FourGversionhs7024a}.iso
			if [[ $? -ne 0 ]];then
				echo "zip THU_4G_hs7024a@${FourGversionhs7024a}.iso fail!"
			else
				rm -rf THU_4G_hs7024a@${FourGversionhs7024a}.iso
			fi
			
			zip -r THU_MCU@${mcuversion}.zip THU_MCU@${mcuversion}.bin
			if [[ $? -ne 0 ]];then
				echo "zip THU_MCU@${mcuversion}.bin fail!"
			else
				rm -rf THU_MCU@${mcuversion}.bin
			fi
		
		else
			FourGversion=`cat $LOCAL_PATH/SVN_4G_MODULE/version.txt`
			cp -rf $LOCAL_PATH/SVN_4G_MODULE/4G_update.iso $LOCAL_PATH/${Out6W6CDir}/ota/THU_4G@${FourGversion}.iso
			cp -rf $LOCAL_PATH/SVN_4G_MODULE/Firehose.zip $LOCAL_PATH/${Out6W6CDir}/4g_ufs
		fi
		
	else
		mcuversion=`cat $LOCAL_PATH/SVN_MCU_UPDATE/version.txt`
	    cp -rf $LOCAL_PATH/SVN_MCU_UPDATE/*.bin $LOCAL_PATH/${Out6W6CDir}/ota/THU_MCU@${mcuversion}.bin
		if [[ ${projectname} =~ "HS7023A" ]];then
			FourGversionhs7023a=`cat $LOCAL_PATH/SVN_4G_MODULE_UPDATE/version_hs7023a.txt`
			cp -rf $LOCAL_PATH/SVN_4G_MODULE_UPDATE/4G_update_hs7023a.iso $LOCAL_PATH/${Out6W6CDir}/ota/THU_4G_hs7023a@${FourGversionhs7023a}.iso
			cp -rf $LOCAL_PATH/SVN_4G_MODULE_UPDATE/Firehose_hs7023a.zip $LOCAL_PATH/${Out6W6CDir}/4g_ufs
			
			FourGversionhs7024a=`cat $LOCAL_PATH/SVN_4G_MODULE_UPDATE/version_hs7024a.txt`
			cp -rf $LOCAL_PATH/SVN_4G_MODULE_UPDATE/4G_update_hs7024a.iso $LOCAL_PATH/${Out6W6CDir}/ota/THU_4G_hs7024a@${FourGversionhs7024a}.iso
			cp -rf $LOCAL_PATH/SVN_4G_MODULE_UPDATE/Firehose_hs7024a.zip $LOCAL_PATH/${Out6W6CDir}/4g_ufs
			
			cd $LOCAL_PATH/${Out6W6CDir}/ota/
			
			zip -r THU_4G_hs7023a@${FourGversionhs7023a}.zip THU_4G_hs7023a@${FourGversionhs7023a}.iso
			if [[ $? -ne 0 ]];then
				echo "zip THU_4G_hs7023a@${FourGversionhs7023a}.iso fail!"
			else
				rm -rf THU_4G_hs7023a@${FourGversionhs7023a}.iso
			fi
			
			zip -r THU_4G_hs7024a@${FourGversionhs7024a}.zip THU_4G_hs7024a@${FourGversionhs7024a}.iso
			if [[ $? -ne 0 ]];then
				echo "zip THU_4G_hs7024a@${FourGversionhs7024a}.iso fail!"
			else
				rm -rf THU_4G_hs7024a@${FourGversionhs7024a}.iso
			fi
			
			zip -r THU_MCU@${mcuversion}.zip THU_MCU@${mcuversion}.bin
			if [[ $? -ne 0 ]];then
				echo "zip THU_MCU@${mcuversion}.bin fail!"
			else
				rm -rf THU_MCU@${mcuversion}.bin
			fi
			
		else
			FourGversion=`cat $LOCAL_PATH/SVN_4G_MODULE_UPDATE/version.txt`
			cp -rf $LOCAL_PATH/SVN_4G_MODULE_UPDATE/4G_update.iso $LOCAL_PATH/${Out6W6CDir}/ota/THU_4G@${FourGversion}.iso
			cp -rf $LOCAL_PATH/SVN_4G_MODULE_UPDATE/Firehose.zip $LOCAL_PATH/${Out6W6CDir}/4g_ufs
		fi
	fi
	
	#��Դ���
	if [[ -d $LOCAL_PATH/Android_OTA_Res/appdata ]] && [[ -f $LOCAL_PATH/Android_OTA_Res/THU_RES_version.txt ]];then
		ResVersion=`cat $LOCAL_PATH/Android_OTA_Res/THU_RES_version.txt`
		cd $LOCAL_PATH/Android_OTA_Res 
		zip -r THU_RES@${ResVersion}.zip appdata
		if [[ $? -ne 0 ]];then
			echo "zip android ota res fail!"
		fi
		cp -f THU_RES@${ResVersion}.zip $LOCAL_PATH/${Out6W6CDir}/ota/
		cp -f THU_RES_version.txt  $LOCAL_PATH/${Out6W6CDir}/ota/
	fi


	cp -f $LOCAL_PATH/OTA/AMSS/* $LOCAL_PATH/${Out6W6CDir}/ota/THU_OS
	cp -f $LOCAL_PATH/OTA/Qnx/* $LOCAL_PATH/${Out6W6CDir}/ota/THU_OS
	cp -f $LOCAL_PATH/OTA/Android/* $LOCAL_PATH/${Out6W6CDir}/ota/THU_OS

	cd $LOCAL_PATH/${Out6W6CDir}/ota/THU_OS
	if [[ ${projectname} =~ "HS7023A" ]];then
		tar -czf THU_OS@${projectversion}.tar.gz *
		zip -r THU_OS@${projectversion}.zip THU_OS@${projectversion}.tar.gz
		cp -f  $LOCAL_PATH/${Out6W6CDir}/ota/THU_OS/THU_OS@${projectversion}.zip  $LOCAL_PATH/${Out6W6CDir}/ota/
	else
		tar -czf THU_OS@${projectversion}.tar.gz *
		cp -f  $LOCAL_PATH/${Out6W6CDir}/ota/THU_OS/THU_OS@${projectversion}.tar.gz  $LOCAL_PATH/${Out6W6CDir}/ota/
	fi
	
	if [[ $? -ne 0 ]];then
		echo "tar OTA OS fail"
	fi
	
	cd $LOCAL_PATH/${Out6W6CDir}/ota
	_count_ota_summd5
	rm -rf $LOCAL_PATH/${Out6W6CDir}/ota/THU_OS
	
	cd $LOCAL_PATH/${Out6W6CDir}
	zip -r ota.zip ota
	rm -rf $LOCAL_PATH/${Out6W6CDir}/ota
}

_output_package ()
{
    if [[ $buildType = "3" ]];then
	    MCUUpdateType=1
		FourGUpdateType=1 
	else
	    MCUUpdateType=0
		FourGUpdateType=0
	fi

	_update_package $packageType $MCUUpdateType $LOCAL_PATH/${Out6W6CDir}/update $FourGUpdateType ${updatePackage}
	
	if [[ ${projectname} =~ "HS7006A" ]] || [[ ${projectname} =~ "HS7008A" ]];then
		_update_package $packageType $MCUUpdateType $LOCAL_PATH/${Out6W6CDir}/update/package2 $FourGUpdateType ${updatePackageB}
	fi
	
	#HS7012A�Լ�����չ��Ŀ��OTA���
	if [[ ${projectname} =~ "HS7012A" ]] || [[ ${projectname} =~ "HS7023A" ]] || [[ ${projectname} =~ "HS7029A" ]];then
		_ota_package $MCUUpdateType
	fi
	
	#HS7006A/HS7008A�Լ�����չ��Ŀ��OTA���
	# if [[ ${projectname} =~ "HS7006A" ]] || [[ ${projectname} =~ "HS7008A" ]];then
		# if [[ -f $LOCAL_PACK_SHELL_PATH/make_full_ota_package.py ]];then
			# if [[ -d $LOCAL_PATH/${Out6W6CDir}/ota ]];then
				# rm -rf $LOCAL_PATH/${Out6W6CDir}/ota
			# fi
			# mkdir -p $LOCAL_PATH/${Out6W6CDir}/ota
			# cd $LOCAL_PATH/${Out6W6CDir}/ota
			# if [[ $? -eq 0 ]];then
				# chmod 777 $LOCAL_PACK_SHELL_PATH/make_full_ota_package.py 
				# echo "start pack ${projectname} OTA"
				# python3 $LOCAL_PACK_SHELL_PATH/make_full_ota_package.py $LOCAL_PATH/${Out6W6CDir}/update/${updatePackage}.zip $LOCAL_PATH/${Out6W6CDir}/ota ${projectname%%_*}
				# if [[ $? -ne 0 ]];then
					# echo " ${projectname} OTA pack fail! "
				# fi
				# sync
			# else
				# echo "mkdir $LOCAL_PATH/${Out6W6CDir}/ota fail"
			# fi

		# else
			# echo "$LOCAL_PACK_SHELL_PATH/make_full_ota_package.py is not exist"
		# fi
	# fi
	
	RUNING_ERROR_PARAM=$ERROR_PARAM_SPACE
    #copy AMSS.tar
    if ([[ $build_amss == "1" ]] || [[ $isBuildUFS == "1" ]]) && [[ $buildType != "3" ]];then
		if [[ -d $LOCAL_PATH/${Out6W6CDir}/amss ]];then
			rm -rf $LOCAL_PATH/${Out6W6CDir}/amss
		fi
		mkdir -p $LOCAL_PATH/${Out6W6CDir}/amss
        if [[ -f $LOCAL_PATH/amss.tar.gz ]]; then
            mv $LOCAL_PATH/amss.tar.gz $LOCAL_PATH/${Out6W6CDir}/amss/
			if [[ $? -ne 0 ]];then
				echo "mv amss.tar.gz to $LOCAL_PATH/${Out6W6CDir}/amss/ fail!"
			fi
	    else
			echo "$LOCAL_PATH is not exist amss.tar.gz"
	        cp -f $LOCAL_PATH/error_qnx_${projectname}_* $LOCAL_PATH/${Out6W6CDir}/amss/
	    fi
    else
	    echo "Didnt build AMSS,package amss.tat.gz is not requied !!!"
    fi
	if [[ $? -ne 0 ]];then
	    _build_error_exit $RUNING_ERROR_PARAM
	fi
  
    #copy Coverity html and xml
	if [[ $covRet = "true" ]] && ([[ $buildType = "0" ]] || [[ $buildType = "1" ]] || [[ $buildType = "4" ]]);then
	    mkdir -p $LOCAL_PATH/${Out6W6CDir}/Coverity_${projectversion}
	    cp -rf $LOCAL_PATH/Coverity_Build/Coverity_${projectname}*/output/errors $LOCAL_PATH/${Out6W6CDir}/Coverity_${projectversion}
	    cp -rf $LOCAL_PATH/Coverity_Build/Coverity_${projectname}*/${projectname}_error_output.csv $LOCAL_PATH/${Out6W6CDir}/Coverity_${projectversion}
	fi
	if [[ $? -ne 0 ]];then
	    _build_error_exit $RUNING_ERROR_PARAM
	fi
}

_make_package ()
{
    if [[ $Qualcomm8155BashPath != "" ]] && [[ $projectname != "" ]] && [[ -d $LOCAL_PATH ]];then
	   rm -rf $LOCAL_PATH/${projectname}_*
	fi
	
	_output_extra
	
    if [[ $PackageResum -eq 0 ]];then
	    _output_package
	fi
	            
    _clean_before_exit
	cleanResum=$?
	if [[ $cleanResum -ne 0 ]];then
	   exit $ERROR_PARAM_SPACE
	fi
}

set_version

_make_package