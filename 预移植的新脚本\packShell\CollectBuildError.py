#!/usr/bin/python
# -*- coding: UTF-8 -*-

import openpyxl
import sys
import io
import datetime
import string
import os
import shutil
reload(sys)
sys.setdefaultencoding('utf8')

projectName = sys.argv[1]
buildNum = sys.argv[2]
buildVersion = sys.argv[3]
errorParam = sys.argv[4]
errorModule = sys.argv[5]
packageResum = sys.argv[6]
buildTime = sys.argv[7]

buildMonth = str(datetime.date.today().month)

buildErrorFileDirs = "/mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/" + projectName + "_Package/"
buildErrorFileOutDirs = "/mnt/new_disk/jenkins/jenkins_output/SA8155/CollectBuild/Month" + buildMonth
errorConformFileName = sys.path[0] + "/Qualcomm8155BuildError.xlsx"
errorFileDirs = "/mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/CollectBuild"
errorFileOutDir = "/mnt/new_disk/jenkins/jenkins_output/SA8155/CollectBuild"

if os.path.exists(errorFileDirs) == False :
	os.makedirs(errorFileDirs)

if os.path.exists(errorFileOutDir) == False :
	os.makedirs(errorFileOutDir)

if os.path.exists(buildErrorFileOutDirs) == False :
	os.makedirs(buildErrorFileOutDirs)

errorFileName = errorFileDirs + "/" + "高通平台项目" + buildMonth + "月份编译汇总统计New.xlsx"
errorTemFileName = sys.path[0] + "/TempCollectErrorFile.xlsx"

pronameFlag = 0

if os.path.exists(errorFileName) == True :
	print(errorFileName+" file is exist ....")
else :
	if os.path.exists(errorTemFileName) == True :
		shutil.copy(errorTemFileName,errorFileName)
		os.system("touch "+errorFileDirs+"/SendCollectEmail")
	else :
		sys.exit()

excelFile = openpyxl.load_workbook(errorFileName)
if projectName.split("_")[0] in excelFile :
	detailSheet = excelFile[projectName.split("_")[0]]
else :
	excelFile.create_sheet(projectName.split("_")[0])
	detailSheet = excelFile[projectName.split("_")[0]]
	detailSheet.append(["编译分支","构建号","编译日期","编译版本","编译结果","报错原因","报错日志文件","编译时长"]) 
	
totalSheet = excelFile['Total']
errorConformFile = openpyxl.load_workbook(errorConformFileName)
runparamSheet = errorConformFile['RunError']

totalSheet.cell(row=1,column=1).value = "高通平台项目" + buildMonth + "月份编译情况统计"

if str(totalSheet.cell(row=2,column=2).value).split(" - ")[0] == "" or str(totalSheet.cell(row=2,column=2).value).split(" - ")[0] == "None" :
	totalSheet.cell(row=2,column=2).value = datetime.date.today().strftime("%Y-%m-%d") + "" + " - "+ datetime.date.today().strftime("%Y-%m-%d")
else :
	totalSheet.cell(row=2,column=2).value = str(totalSheet.cell(row=2,column=2).value).split(" - ")[0] + "" + " - "+ datetime.date.today().strftime("%Y-%m-%d")
totalSheet.cell(row=3,column=2).value = int(totalSheet.cell(row=3,column=2).value or 0) + 1

errorFlag = 0
error_module = ""
error_result = ""
error_log = ""

if errorParam == '0' :
	error_module = "编译成功"
else :
	for paramRow in runparamSheet.iter_rows():
		strparamRow = str(paramRow[0].value)
		if strparamRow == errorParam:
			errorFlag = 1
			error_failed = paramRow[1].value
			error_result = paramRow[2].value
			error_module = str(paramRow[3].value) + "失败"
			if errorModule != "" :
				error_result = errorModule
			else :
				error_result = error_failed + "," + error_result
	if packageResum == '1' or packageResum == '3':
		error_log = "error_android_" + projectName + "_" + buildNum + ".log"
	elif packageResum == '2' :
		error_log = "error_qnx_" + projectName + "_" + buildNum + ".log"	
	else :
		if errorParam >= '21' and errorParam <= '23' :
			error_log = "error_ota_" + projectName + "_" + buildNum + ".log"
		elif errorParam >= '24' and errorParam <= '25' :
			error_log = "error_ufs_" + projectName + "_" + buildNum + ".log"
		elif errorParam == '27':
			error_log = "error_package_" + projectName + "_" + buildNum + ".log"
		else :
			error_log = ""
	buildErrorFile = buildErrorFileDirs + error_log
	if os.path.exists(buildErrorFile) == True :
		shutil.copy(buildErrorFile,buildErrorFileOutDirs)

try:
	buildTime = float(buildTime)
except ValueError:
	buildTime = 0.0  # 默认值或错误处理
	
for idex,row in enumerate(totalSheet.iter_rows()):
	if row[0].value == projectName.split("_")[0] :
		row[1].value = row[1].value + 1
		if errorFlag == 1 :
			row[2].value = row[2].value + 1 
		if buildTime > 3 :
			row[3].value = row[3].value + 1
		elif buildTime < 2 :
			row[4].value = row[4].value + 1
		else :
			row[5].value = row[5].value + 1
		break
else :
	totalSheet._current_row=totalSheet.max_row
	if buildTime > 3 :
		totalSheet.append([projectName.split("_")[0],1,errorFlag,1,0,0])
	elif buildTime < 2 :
		totalSheet.append([projectName.split("_")[0],1,errorFlag,0,1,0])
	else :
		totalSheet.append([projectName.split("_")[0],1,errorFlag,0,0,1])
	
detailSheet._current_row=detailSheet.max_row
detailSheet.append([projectName,buildNum,datetime.date.today(),buildVersion,error_module,error_result,error_log,buildTime])

excelFile.save(errorFileName)
shutil.copy(errorFileName,errorFileOutDir)
sys.exit()


