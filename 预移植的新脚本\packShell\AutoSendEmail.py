#!/usr/bin/python
# -*- coding: UTF-8 -*-

#****************************
# 通知脚本
# 自动发送邮件主脚本
# Authot xizhuang.wu
# version 1.0
#****************************

import smtplib
import sys
import glob
import os
import datetime
import openpyxl
import string
from email.mime.multipart import MIMEMultipart 
from email.mime.text import MIMEText
from datetime import date
reload(sys)
sys.setdefaultencoding('utf8')

projectName = sys.argv[1]
projectVersion = sys.argv[2]
buildType = sys.argv[3]
buildTime = sys.argv[4]
buildEmailResult = sys.argv[5]
buildSendReceivers = sys.argv[6]
buildOut6W6CDir = sys.argv[7]
buildWaitDoqueStr = sys.argv[8]
buildRunDoqueStr = sys.argv[9]
buildMemoryTip = sys.argv[10]
buildTaskNum = sys.argv[11]
buildCollectMessage = sys.argv[12]
buildUploadJfrog = sys.argv[13]
buildJfrogWeb = sys.argv[14]

print(projectName)
print(projectVersion)
print(buildType)
print(buildTime)
print(buildEmailResult)
print(buildSendReceivers)
print(buildOut6W6CDir)
print(buildWaitDoqueStr)
print(buildRunDoqueStr)
print(buildMemoryTip)
print(buildTaskNum)
print(buildUploadJfrog)
print(buildJfrogWeb)

# 第三方 SMTP 服务
mail_host = "www.foryouge.com.cn"  # SMTP服务器
mail_user = "<EMAIL>"  # 用户名
mail_pass = ""  # 密码(这里的密码不是登录邮箱密码，而是授权码)

sender = '<EMAIL>'  # 发件人邮箱

nowTime = datetime.datetime.now()
month = nowTime.month - 1
errorTotalFile = "/mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/CollectBuild/高通平台项目"+str(month)+"月份编译汇总统计.xlsx"

if ',' not in buildSendReceivers:
	buildSendReceivers = buildSendReceivers + ','
receivers = buildSendReceivers.split(',')

if buildType == '0':
	buildTaskType='''全量同步编译'''
elif buildType == '1':
	buildTaskType='''单编QNX'''
elif buildType == '2':
	buildTaskType='''单编安卓'''
elif buildType == '3':
	buildTaskType='''重新集成打包'''
elif buildType == '4':
	buildTaskType='''全量异步编译'''
else :
	print("编译参数有误，请重新输入")

contentBaseCon = '''
	\t 构建号 ：''' + buildTaskNum + '''
	\t 编译时间 ：''' + buildTime + '''
	\t 编译项目 ：''' + projectName + '''
	\t 编译类型 ：''' + buildTaskType + '''
	\t 编译版本 ：''' + projectVersion + '''	

'''

contentResult = ""
contentCause = ""

errorConformFileName = sys.path[0] + "/Qualcomm8155BuildError.xlsx"
errorConformFile = openpyxl.load_workbook(errorConformFileName)
runparamSheet = errorConformFile['RunError']

for paramRow in runparamSheet.iter_rows():
		strparamRow = str(paramRow[0].value)
		if strparamRow == buildEmailResult:
			error_failed = paramRow[1].value
			error_result = paramRow[2].value
			contentResult = '\t编译结果：        '+error_failed
			if buildEmailResult == '0' :     #编译成功
				contentCause='''软件路径:        \\\\10.2.4.151\\SwPublic'''+'\\Jenkins_output\\SA8155\\'+projectName+'\\'+buildOut6W6CDir
			elif buildEmailResult > '50' and buildEmailResult < '101' :	#QNX编译报错
				contentCause='''报错日志路径:       \\\\10.2.4.151\\SwPublic'''+'\\Jenkins_output\\SA8155\\'+projectName+'\\'+buildOut6W6CDir+'''下查看'''
			elif buildEmailResult > '100' and buildEmailResult < '151' :	#Android编译报错
				contentCause='''报错日志路径:        \\\\10.2.4.151\\SwPublic'''+'\\Jenkins_output\\SA8155\\'+projectName+'\\'+buildOut6W6CDir+'''下查看'''
			else :	#其他报错
				contentCause = '\t说明：        '+error_result
   
contentpassword='''\t<路径访问帐号密码：swRead/foryouge@909>'''
contentOther='''\t<此邮件由Jenkins编译服务器自动发送,请勿直接回复此邮件>'''

contentJFrogmessage='''\t------------'''
contentJFrogPath='''\t------------'''
contentJFrogPWD='''\t------------'''
if buildUploadJfrog == "1" : #上传JFROG系统成功
	contentJFrogmessage='''\t升级包已上传到JFROG系统，请使用如下地址进行访问查验！'''
	contentJFrogPath='''\tJFROG访问网址：  ''' +buildJfrogWeb+'/'+projectName+'/'+buildOut6W6CDir
	contentJFrogPWD='''\t<JFROG外部访问账号/密码：rjliang/Nru2dqxj*>'''
elif buildUploadJfrog == '2' : #上传JFROG系统失败
	contentJFrogmessage='''\t升级包自动上传JFROG系统失败，请使用管理员账号登录项目仓库手动上传！'''
	contentJFrogPath='''\tJFROG访问网址：  ''' +buildJfrogWeb
	contentJFrogPWD='''\t<JFROG外部访问账号/密码：rjliang/Nru2dqxj*>'''
elif buildUploadJfrog == '3' : #编译失败
	print("没找到升级包，检查是否编译成功")
else : #无需上传JFROG系统
	print("不需要上传到JFROG系统")


title = projectName+'  '+projectVersion+'版本编译结果通知'  # 邮件主题
contentCollect = ''

message = MIMEMultipart()
#月初将上个月底统计的编译统计表邮件发送出来
if buildCollectMessage == "1" and os.path.exists(errorTotalFile) == True:
	outEmailFile="高通平台项目"+str(month)+"月份编译汇总统计.xlsx"
	
	messageFile = MIMEText(open(errorTotalFile, 'rb').read(), 'base64', 'utf-8')
	messageFile["Content-Type"] = 'application/octet-stream'
	messageFile["Content-Disposition"] = 'attachment; filename=outEmailFile'
	message.attach(messageFile)       #添加附件
	title = projectName+' '+str(month)+'月份编译结果月统计'
	contentCollect = '''
	
	\t另上个月'''+str(month)+'''月份项目编译情况总结详情可见附件
	
	'''

content='''大家好！您启动的编译流程通知如下


		\t'''+contentBaseCon+'''
		\t'''+contentResult+'''
		\t'''+contentCause+'''
		\t'''+contentCollect+'''
		\t'''+contentpassword+'''
		\t'''+contentOther+'''
		\t'''+contentJFrogmessage+'''
		
		
		\t'''+contentJFrogPath+'''
		\t'''+contentJFrogPWD
		

if buildMemoryTip != '0':
		content+'''
		\t
		\t
		\t
		\t温馨提示：当前编译输出路径6w6c空间已经严重不足，当前剩余空间为   '''+buildMemoryTip+'''G。请各位SSE及时清理过时软件，避免由于空间不足问题导出软件失败。
		\t6w6c仅作为编译导出路径，软件需要备份存档的话需另存其他专门用于保存软件的服务器'''

message['From'] = "{}".format(sender)
message['To'] = ",".join(receivers)
message['Subject'] = title
message['Content'] = content

messageContent = MIMEText(content, 'plain', 'utf-8')
message.attach(messageContent)    #添加内容
	
try:
	smtpObj = smtplib.SMTP_SSL(mail_host, 465)  # 启用SSL发信, 端口一般是465
	smtpObj.login(mail_user, mail_pass)  # 登录验证
	smtpObj.sendmail(sender, receivers, message.as_string())  # 发送
	print("mail has been send successfully.")
except smtplib.SMTPException as e:
	print(e)