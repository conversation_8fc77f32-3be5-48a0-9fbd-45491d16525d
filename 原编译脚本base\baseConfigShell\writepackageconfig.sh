#! /bin/bash 

function get_block_value()  
{  
    if [ ! -f $1 ] || [ $# -ne 3 ];then   
        return 1  
    fi  
	blockname=$2  
	filename=$3  
	  
	begin_block=0  
	end_block=0  
  
    while read -r line  
    do  
        if [ "X$line" = "X[$blockname]" ];then  
            begin_block=1  
            continue  
        fi  
          
        if [ $begin_block -eq 1 ];then  
            end_block=$(echo $line | awk 'BEGIN{ret=0} /^.? $/{ret=1} END{print ret}')  
            if [ $end_block -eq 1 ];then    
                break  
            fi  
      
            need_ignore=$(echo $line | awk 'BEGIN{ret=0} /^#/{ret=1} /^$/{ret=1} END{print ret}')
			
            if [ $need_ignore -eq 1 ];then   
                continue  
            fi
			
			if [[ "$blockname" = "Remove_DIR" ]];then
			   if [[ "$filename" =~ "$line" ]];then
			      return 2
			   fi
			elif [[ "$blockname" = "Remove_FILE" ]];then
			   if [[ "$line" = "$filename" ]];then
			      return 2
			   fi
			elif [[ "$blockname" = "Switch_DIR" ]];then
			   befDir=$(echo $line | awk -F '->' '{gsub(" |\t","",$1); print $1}')  
               aftDirFile=$(echo $line | awk -F '->' '{gsub(" |\t","",$2); print $2}')
			   if [[ "$befDir" = "$filename" ]];then
			      echo $aftDirFile
				  return 1
			   elif [[ "$befDir" =~ $filename ]];then
			      echo $aftDirFile
			      return 2
			   else
			      continue
			   fi
			else
			   echo "nothing to do!"
			   return 0
			fi
        fi  
    done < $1	
	return 0
}

pathDir=`pwd`

cd ./install/aarch64le/base

if [[ -f depthlist.txt ]] || [[  -f depthlist2.txt ]];then
   rm -rf depthlist.txt
   rm -rf depthlist2.txt
fi

depNum=1
while true
do
   if [[ `find ./ -maxdepth ${depNum} -mindepth ${depNum} -type d` = "" ]];then
      break
   fi
   find ./ -maxdepth ${depNum} -mindepth ${depNum} -type d >> depthlist.txt
   let depNum++
done

awk '{sub("\./","base/");print "#[search="$0"]" echo "\n[type=dir uid=0 gid=0 perms=0744] "$0 echo "\n";}' depthlist.txt >> depthlist2.txt

#将获取到的子目录全部写入打包配置文件中
dNumBegin=`sed -n '/#MPU PACKAGE BEGIN/=' ../../../target/hypervisor/host/build_files/system.build.tmpl`
dNumEnd=`sed -n '/#MPU PACKAGE END/=' ../../../target/hypervisor/host/build_files/system.build.tmpl`
sed -i ''${dNumBegin}'r depthlist2.txt' ../../../target/hypervisor/host/build_files/system.build.tmpl

while read -r depLines
do
   if [[ $depLines = "" ]] || [[ "${depLines}" =~ .*search=.*$ ]];then
      continue
   fi
   awkfilestr=`echo $depLines | awk -F '] ' '{print $2}'`
   lineNum=`grep -n ''${awkfilestr}'$' ../../../target/hypervisor/host/build_files/system.build.tmpl | awk -F ':' '{print $1}'` 
   lineNum2=`echo $lineNum | awk -F' ' '{print $1}'`
   find ../$awkfilestr -maxdepth 1 -type f > filelist.txt
 
   #awk '{sub("\../","");print "[type=file uid=0 gid=0 perms=0744] "$0"=aarch64le/"$0;}' filelist.txt > filelist2.txt
   
   while read -r bLine
   do 
      aLine=`echo ${bLine/..\//}`
	  if [[ "$aLine" = "$awkfilestr" ]];then
	     continue
	  else
	     echo "[type=file uid=0 gid=0 perms=0744] "$aLine"=aarch64le/"$aLine >> filelist2.txt
	  fi
   done < filelist.txt
   sed -i ''${lineNum2}'r filelist2.txt' ../../../target/hypervisor/host/build_files/system.build.tmpl
   rm -rf filelist2.txt
done < depthlist2.txt

rm -rf depthlist.txt
rm -rf depthlist2.txt
rm -rf filelist.txt

cd ${pathDir}