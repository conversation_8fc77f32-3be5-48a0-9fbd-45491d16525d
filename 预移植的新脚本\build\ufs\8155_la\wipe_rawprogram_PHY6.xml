<?xml version="1.0" ?>
<data>
  <!--NOTE: This is an ** Autogenerated file **-->
  <!--NOTE: Sector size is 4096bytes-->
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="zeros_33sectors.bin" label="Overwrite MBR sector" num_partition_sectors="1" partofsingleimage="false" physical_partition_number="6" readbackverify="false" size_in_KB="0.5" sparse="false" start_byte_hex="0x0" start_sector="0"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="zeros_5sectors.bin" label="Overwrite Primary GPT Sectors" num_partition_sectors="5" partofsingleimage="false" physical_partition_number="6" readbackverify="false" size_in_KB="20.0" sparse="false" start_byte_hex="0x1000" start_sector="1"/>
  <program SECTOR_SIZE_IN_BYTES="4096" file_sector_offset="0" filename="zeros_5sectors.bin" label="Overwrite Backup GPT Sectors" num_partition_sectors="5" partofsingleimage="false" physical_partition_number="6" readbackverify="false" size_in_KB="20.0" sparse="false" start_byte_hex="(4096*NUM_DISK_SECTORS)-20480." start_sector="NUM_DISK_SECTORS-5."/>
</data>
