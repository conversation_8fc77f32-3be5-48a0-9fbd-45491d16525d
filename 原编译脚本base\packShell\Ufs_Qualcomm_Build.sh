#!/bin/bash
#****************************
# UFS编译脚本
# UFS编译主脚本
# Author xizhuang.wu
# version 1.0
#****************************

projectName=$1
projectVersion=$2
PoolCount=$3
build_paramNum=$4

UFSResum='0'
UFSVersion='v0.1'

Qualcomm8155BashPath=/mnt/home/<USER>/e-cockpit/qnx-android/jenkins_qnx-android_Qualcomm8155
BuildPath=$Qualcomm8155BashPath/Qualcomm8155Pool${PoolCount}
Build_Shell_Path="http://10.2.4.101/svn/Qualcomm/02_SA8155P/01_HS7001A/02_CodeLib/01_MainPath/1.2/02_Android/04_Shell/buildShell3.0/buildShell"
Pak_Shell_Path="http://10.2.4.101/svn/Qualcomm/02_SA8155P/01_HS7001A/02_CodeLib/01_MainPath/1.2/02_Android/04_Shell/buildShell3.0/packShell"

ERROR_PARAM_UFS_CONFIG=24
ERROR_PARAM_UFS_MAKE=25
ERROR_PARAM_SPACE=27

RUNING_ERROR_PARAM=0

_build_error_exit ()
{
    EXIT_ERROR_PARAM=$1
	echo "UFS build Error,Error param is $EXIT_ERROR_PARAM,exit ........"
	exit $EXIT_ERROR_PARAM
}

echo "beging delete UFS path, rebuild UFS path and SVN export UFS config !!!!!!"
if [[ -d $Qualcomm8155BashPath/${projectName}_Package/UFS ]];then
    rm -rf $Qualcomm8155BashPath/${projectName}_Package/UFS
fi
mkdir -p $Qualcomm8155BashPath/${projectName}_Package/UFS/outImg

RUNING_ERROR_PARAM=$ERROR_PARAM_UFS_CONFIG
UFSVersion=`$BuildPath/${Build_Shell_Path##*/}/readXMLFile.sh Qnx ${projectName} Package UFS`
echo "read UFS version  Version = $UFSVersion"
if [[ $UFSVersion = "" ]];then
   echo "$projectName UFS is not exit or isnt setting !!!!!  exit $RUNING_ERROR_PARAM"
   _build_error_exit $RUNING_ERROR_PARAM
fi


svn info http://10.2.4.101/svn/Qualcomm/02_SA8155P/${projectName}/02_CodeLib/01_ProjectPath/05_Smt_Img/${UFSVersion}
if [[ $? -eq 0 ]];then
   svn --force export http://10.2.4.101/svn/Qualcomm/02_SA8155P/${projectName}/02_CodeLib/01_ProjectPath/05_Smt_Img/${UFSVersion} $Qualcomm8155BashPath/${projectName}_Package/UFS
else
   svn --force export http://10.2.4.101/svn/Qualcomm/02_SA8155P/${projectName%%_*}/02_CodeLib/01_ProjectPath/05_Smt_Img/${UFSVersion} $Qualcomm8155BashPath/${projectName}_Package/UFS
fi

if [[ $? -ne 0 ]] || [[ ! -f $Qualcomm8155BashPath/${projectName}_Package/UFS/config/UFS_SMT_IMG.cfg ]];then
    echo "$projectName UFS svn export ERROR !!!!! exit $RUNING_ERROR_PARAM"
    _build_error_exit $RUNING_ERROR_PARAM
fi

sed -i "/^path_android*/cpath_android=\""$Qualcomm8155BashPath"/Qualcomm8155Pool"$PoolCount"/hqx1.2.1.c1_r00004.2/android\"" $Qualcomm8155BashPath/${projectName}_Package/UFS/config/UFS_SMT_IMG.cfg
sed -i "/^path_qnx*/cpath_qnx=\""$Qualcomm8155BashPath"/Qualcomm8155Pool"$PoolCount"/hqx1.2.1.c1_r00004.2/qnx_bsp\"" $Qualcomm8155BashPath/${projectName}_Package/UFS/config/UFS_SMT_IMG.cfg
sed -i "/^path_amss*/cpath_amss=\""$Qualcomm8155BashPath"/Qualcomm8155Pool"$PoolCount"/hqx1.2.1.c1_r00004.2/amss\"" $Qualcomm8155BashPath/${projectName}_Package/UFS/config/UFS_SMT_IMG.cfg
sed -i "/^path_gpt*/cpath_gpt=\""$Qualcomm8155BashPath"/"$projectName"_Package/UFS/gpt\"" $Qualcomm8155BashPath/${projectName}_Package/UFS/config/UFS_SMT_IMG.cfg
sed -i "/^path_out_img*/cpath_out_img=\""$Qualcomm8155BashPath"/"$projectName"_Package/UFS/outImg\"" $Qualcomm8155BashPath/${projectName}_Package/UFS/config/UFS_SMT_IMG.cfg

echo "beging build $projectName UFS image !!!!!!"
cd $Qualcomm8155BashPath/${projectName}_Package/${Pak_Shell_Path##*/}

RUNING_ERROR_PARAM=$ERROR_PARAM_UFS_MAKE
chmod 777 ./ufs_make_signed
chmod 777 ./ufs_smt_img_making_signed.sh
./ufs_make_signed 2 ufs_smt_img_making_signed.sh $Qualcomm8155BashPath/${projectName}_Package/UFS/config/UFS_SMT_IMG.cfg

if [[ $? -ne 0 ]];then
    echo "build $projectName UFS image ERROR !!!!!! exit $RUNING_ERROR_PARAM"
    _build_error_exit $RUNING_ERROR_PARAM
else
    echo "build $projectName UFS image SUCCESS !!!!!!"
    if [[ -d /mnt/BU2_NAS/Jenkins_output/SA8155/${projectName}/UFS/${projectVersion}_${build_paramNum} ]] && [[ ${projectName} != "" ]] && [[ ${projectVersion} != "" ]];then
	    rm -rf /mnt/BU2_NAS/Jenkins_output/SA8155/${projectName}/UFS/${projectVersion}_${build_paramNum}
    fi
	
	RUNING_ERROR_PARAM=$ERROR_PARAM_SPACE
    mkdir -p /mnt/BU2_NAS/Jenkins_output/SA8155/${projectName}/UFS/${projectVersion}_${build_paramNum}
    cp $Qualcomm8155BashPath/${projectName}_Package/UFS/outImg/*.tar /mnt/BU2_NAS/Jenkins_output/SA8155/${projectName}/UFS/${projectVersion}_${build_paramNum}
    if [[ $? -ne 0 ]];then
        echo "$projectName UFS ima tar output ERROR !!!!!! exit $RUNING_ERROR_PARAM"
        _build_error_exit $RUNING_ERROR_PARAM
    else
	    if [[ $Qualcomm8155BashPath != "" ]] && [[ ${projectName} != "" ]] && [[ -d $Qualcomm8155BashPath/${projectName}_Package/UFS/outImg ]];then 
		   rm -rf $Qualcomm8155BashPath/${projectName}_Package/UFS/outImg/*
		fi
	fi
    cp $Qualcomm8155BashPath/${projectName}_Package/UFS/ufs_smt_descriptor_*.xlsx /mnt/BU2_NAS/Jenkins_output/SA8155/${projectName}/UFS/${projectVersion}_${build_paramNum}
fi



