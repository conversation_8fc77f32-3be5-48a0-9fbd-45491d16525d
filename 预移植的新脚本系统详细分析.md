# 预移植的新脚本系统详细分析

## 📋 概述

本文档详细分析预移植的新脚本系统，这是在原base脚本基础上的重大升级版本。新系统在保持核心功能的同时，增强了并发处理能力、优化了路径配置、改进了错误处理机制，并提供了更完善的XML配置驱动构建流程。

## 🔍 1. 系统架构对比分析

### 1.1 核心改进点
```bash
# 构建池数量提升
# 原base版本: 3个并发构建池
# 新版本: 4个并发构建池
PoolNum=4  # 提升33%的并发处理能力

# 路径配置优化
# 原base版本路径
Qualcomm8155BashPath=/mnt/home/<USER>/e-cockpit/qnx-android/jenkins_qnx-android_Qualcomm8155

# 新版本路径
Qualcomm8155BashPath=/mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155
```

### 1.2 SVN认证机制增强
```bash
# 新版本硬编码SVN凭据，提高自动化程度
SVN_CMD="svn --username gjzhao --password gjz123 --no-auth-cache --trust-server-cert"

# 在关键SVN操作中统一使用认证
$SVN_CMD --force export ${tmp_path_head_hyp}${root_path[tmp_index]} ${LOCAL_PATH}/temDir
$SVN_CMD --force export $Base_Shell_Path ${LOCALQUA_BSP_PATH}/apps/qnx_ap
```

## 🏗️ 2. QNX构建流程深度分析

### 2.1 环境准备阶段优化
```bash
function sync_mainline() {
    # 1. 增强的清理机制
    echo "Begin delect Qnx output img and update !!!!"
    rm -f $Qualcomm8155BashPath/${projectname}_Package/amss.tar.gz
    rm -rf $Qualcomm8155BashPath/${projectname}_Package/output/Qnx
    rm -rf $Qualcomm8155BashPath/${projectname}_Package/Image/{AMSS,Qnx}
    rm -rf $Qualcomm8155BashPath/${projectname}_Package/Update/{AMSS.tar.gz,QNX.tar.gz}
    
    # 2. 目录结构创建
    mkdir -p $Qualcomm8155BashPath/${projectname}_Package/Image/{Android,Qnx,AMSS}
    mkdir -p $Qualcomm8155BashPath/${projectname}_Package/Update
    mkdir -p $Qualcomm8155BashPath/${projectname}_Package/output/Qnx
    
    # 3. QNX基线代码处理 - 条件化解压
    if [[ ! -d $LOCALQNX_SDP_PATH ]] && [[ "${isBuildC1}" = "0" ]]; then
        echo "download sdp"
        tar -vxf /home/<USER>/e-cockpit/qualcomm/HQX1.2.1_R00005.2/sdp700_hqx1_2.tar.gz -C $LOCAL_PATH/
    fi
    
    # 4. 双重许可证部署机制
    if [[ ! -d $LOCAL_PATH/.qnx/license ]]; then
        echo "qnx sdp license register"
        tar -xf /home/<USER>/e-cockpit/qualcomm/HQX1.0_R00020.1/qnx_sdp_install/sdp_license/qnx700_license/license.tar -C $LOCAL_PATH
    fi
    
    # 新增：许可证备份部署
    if [[ ! -d $LOCALQNX_SDP_PATH/qnx_bins/license ]]; then
        cp -rf $LOCAL_PATH/.qnx/license $LOCALQNX_SDP_PATH/qnx_bins/
    fi
}
```

### 2.2 源码导出流程增强
```bash
function _export_project_code() {
    for i in "${GLOBAL_PROJECT_DEF_SRCCODE[@]}"; do
        root_path=($i)
        
        # 获取SVN标签路径 - 支持安全启动版本
        svn_tag=$(get_field_value $configFile ${root_path[${DEF_PROJECT_TAG_PATH}]} tag)
        
        # 构建完整SVN路径 - 支持安全启动分支
        if [[ ${isBuildSecure} = "1" ]]; then
            tmp_path_head_hyp=${GLOBAL_SVN_SOURCE_TAG_MAIN_HYP}/${projectname}_Tag-${projectversion}_safety
        else
            tmp_path_head_hyp=${GLOBAL_SVN_SOURCE_TAG_MAIN_HYP}/${projectname}_Tag-${projectversion}
        fi
        
        # 增强的源码导出 - 统一认证
        echo "QNX Export ${root_path[${DEF_PROJECT_TAG_PATH}]} code from ${svn_tag}"
        $SVN_CMD --force export ${tmp_path_head_hyp}${root_path[tmp_index]} ${LOCAL_PATH}/temDir
        
        # MPU模块特殊处理 - 生成模块列表
        if [[ "${root_path[${DEF_PROJECT_TAG_PATH}]}" =~ .*MPU.* ]]; then
            $SVN_CMD list ${tmp_path_head_hyp}${root_path[tmp_index]}/mpu/apps | grep -v "public" | awk -F '/' '{print $1}' >> ${LOCAL_PATH}/appsList.txt
            $SVN_CMD list ${tmp_path_head_hyp}${root_path[tmp_index]}/mpu/mids | grep -v "public" | awk -F '/' '{print $1}' >> ${LOCAL_PATH}/midsList.txt
            $SVN_CMD list ${tmp_path_head_hyp}${root_path[tmp_index]}/mpu/libs | awk -F '/' '{print $1}' >> ${LOCAL_PATH}/libsList.txt
        fi
        
        # 源码移动和清理
        rsync -av ${LOCAL_PATH}/temDir/* ${LOCAL_PATH}/${root_path[${DEF_PROJECT_SOURCE_PATH}]}/
        rm -rf ${LOCAL_PATH}/temDir
    done
}
```

### 2.3 Coverity代码扫描集成
```bash
function _build() {
    cd $LOCALQUA_BSP_PATH/apps/qnx_ap
    source setenv_64.sh --external $LOCALQNX_SDP_PATH
    make clean
    
    if [[ $coverity_scan = "true" ]]; then
        # Coverity扫描构建流程
        ${RA_license}/nuw-config --qnx 
        mkdir -p $Qualcomm8155BashPath/${projectname}_Package/Coverity_Build
        
        # 构建并扫描
        ${RA_license}/nuw-build --dir $Qualcomm8155BashPath/${projectname}_Package/Coverity_Build make all 2>$Qualcomm8155BashPath/${projectname}_Package/QNX_Build_Error_${build_paramNum}.log
        result=$?
        
        # 分析和提交结果
        ${RA_license}/nuw-analyze --dir $Qualcomm8155BashPath/${projectname}_Package/Coverity_Build 
        ${RA_license}/nuw-commit-error --dir $Qualcomm8155BashPath/${projectname}_Package/Coverity_Build --host *********** --port 9900 --access-token `cat ${RA_token}` --project $PROJECT_NAME --project-version $PROJECT_VERSION
    else
        # 普通构建
        make all 2>$Qualcomm8155BashPath/${projectname}_Package/QNX_Build_Error_${build_paramNum}.log
        result=$?
    fi
}
```

## 🔒 3. 安全启动构建流程分析

### 3.1 安全启动环境配置
```bash
function secure_boot_make() {
    RUNING_ERROR_PARAM=$ERROR_PARAM_BUILD_AMSS
    
    # 1. 清理和准备
    if [[ -f $LOCALQUA_AMSS_PATH.tar ]]; then
        rm -rf $LOCALQUA_AMSS_PATH.tar
    fi
    
    cd $LOCALQUA_AMSS_PATH
    # 复制安全启动专用AMSS代码
    cp -rf /mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/SecureBoot/amss/* $LOCALQUA_AMSS_PATH
    
    # 2. ADSP构建 - 设置OEM_ROOT
    export OEM_ROOT=${LOCALQUA_AMSS_PATH}/adsp_8155/adsp_proc/hap/oem
    python ./adsp_8155/adsp_proc/build/build.py -c sm8150 -o all -f ADSP
    if [ $? -ne 0 ]; then
        echo "===> build.py -c sm8150 -o all -f ADSP fail"
        _build_error_exit $RUNING_ERROR_PARAM
    fi
    
    # 3. CDSP构建
    export OEM_ROOT=${LOCALQUA_AMSS_PATH}/cdsp_8155/cdsp_proc/hap/oem
    python ./cdsp_8155/cdsp_proc/build/build.py -c sm8150 -o all -f CDSP
    if [ $? -ne 0 ]; then
        echo "===> build.py -c sm8150 -o all -f CDSP fail"
        _build_error_exit $RUNING_ERROR_PARAM
    fi
    
    # 4. Boot构建
    python ./boot_8155/boot_images/QcomPkg/buildex.py --variant AU -r RELEASE -t SDM855Pkg,QcomToolsPkg
    if [ $? -ne 0 ]; then
        echo "===> buildex.py --variant AU fail"
        _build_error_exit $RUNING_ERROR_PARAM
    fi
    
    # 5. TZ构建
    python ./tz_8155/trustzone_images/build/ms/build_all.py -b TZ.XF.5.0 CHIPSET=sm8150
    if [ $? -ne 0 ]; then
        echo "===> build_all.py -b TZ.XF.5.0 fail"
        _build_error_exit $RUNING_ERROR_PARAM
    fi
    
    # 6. AOP构建
    cd aop_8155
    ./aop_proc/build/build_855au.sh    
    if [[ $? -ne 0 ]]; then
        echo "build amss ERROR !!!!!!!!"
        _build_error_exit $RUNING_ERROR_PARAM
    fi
    cd ../
}
```

### 3.2 安全启动QNX构建
```bash
# 安全启动模式下的QNX构建
function secure_boot_qnx_build() {
    # 环境变量设置
    PATH=$FGE_PATH
    JAVA_HOME=$FGE_JAVA_HOME
    
    echo "===>secure_boot_make make qnx ---->>"
    rm -f $Qualcomm8155BashPath/${projectname}_Package/QNX_Build_Error_*.log
    cd $LOCALQUA_BSP_PATH/apps/qnx_ap
    source setenv_64.sh --external $LOCALQNX_SDP_PATH
    make clean 
    
    if [[ $coverity_scan = "true" ]]; then
        # Coverity扫描模式
        if [[ -e $Qualcomm8155BashPath/${projectname}_Package/Coverity_Build ]]; then
            rm -rf $Qualcomm8155BashPath/${projectname}_Package/Coverity_Build
            mkdir -p $Qualcomm8155BashPath/${projectname}_Package/Coverity_Build
        fi
        $COVBUILD --dir $OUTPUT --add-arg --c++11 --add-arg -D__QNX_CPP11__ make all 2>$Qualcomm8155BashPath/${projectname}_Package/QNX_Build_Error_${build_paramNum}.log
        result=$?
    else
        # 普通构建
        make all 2>$Qualcomm8155BashPath/${projectname}_Package/QNX_Build_Error_${build_paramNum}.log
        result=$?
    fi
}
```

## 🔧 4. AMSS Meta构建流程

### 4.1 Meta构建准备
```bash
function amss_meta_build() {
    # 检查super.img依赖
    if [[ ! -f "$LOCALQUA_BSP_PATH/apps/qnx_ap/target/hypervisor/host/out_8155/super.img" ]]; then
        echo "Warning: super.img not found, Meta build may fail"
        echo "Expected location: $LOCALQUA_BSP_PATH/apps/qnx_ap/target/hypervisor/host/out_8155/super.img"
    else
        echo "super.img found, proceeding with normal build process"
    fi
    
    # Meta构建执行
    echo "Begin Build AMSS Meta build !!!" `date +%F" "%T`
    cd common/build
    ./build.py --flavors=8155_la
    result=$?
    echo "End Build AMSS Meta build !!!" `date +%F" "%T`
    echo "Build AMSS Meta build Over !!!! Make result is "$result

    if [ $result -ne 0 ]; then
        echo "=========================================="
        echo "AMSS Meta build failed with exit code: $result"
        echo "This may be due to missing super.img file"
        echo "Check the build log above for checksparse.py errors"
        echo "=========================================="
        _build_error_exit $RUNING_ERROR_PARAM
    fi
}
```

### 4.2 CDT生成流程
```bash
function generate_cdt() {
    # CDT生成
    cd boot_8155/boot_images/QcomPkg/Tools/
    python cdt_generator.py cdp_0_1.xml cdt_8155.bin
    if [ $? -ne 0 ]; then
        echo "CDT generation failed"
        _build_error_exit $RUNING_ERROR_PARAM
    fi
}
```

## 📦 5. 打包和输出处理增强

### 5.1 QNX打包流程
```bash
function package_qnx_outputs() {
    # 进入QNX更新目录
    cd $LOCALQUA_BSP_PATH/apps/qnx_ap/target/hypervisor/host/out_8155/update/$projectversion

    # 创建QNX.tar.gz压缩包
    RUNING_ERROR_PARAM=$ERROR_PARAM_PACKAGE
    tar -czf QNX.tar.gz *
    if [[ $? -ne 0 ]]; then
        _build_error_exit $RUNING_ERROR_PARAM
    fi

    # 复制到打包目录
    cp QNX.tar.gz $Qualcomm8155BashPath/${projectname}_Package/Update

    # 同步镜像文件到Image目录
    rsync -rv --exclude='QNX.tar.gz' * $Qualcomm8155BashPath/${projectname}_Package/Image/Qnx

    # 特定项目的OTA处理
    if [[ $projectname =~ "HS7012A" ]] || [[ $projectname =~ "HS7023A" ]] || [[ $projectname =~ "HS7029A" ]]; then
        rsync -rv --exclude='QNX.tar.gz' * $Qualcomm8155BashPath/${projectname}_Package/OTA/Qnx
    fi
}
```

### 5.2 AMSS打包流程
```bash
function package_amss_outputs() {
    # 进入AMSS输出目录
    cd $LOCALQUA_AMSS_PATH/$projectversion

    # 创建AMSS.tar.gz压缩包
    tar -czf AMSS.tar.gz *

    # 复制到打包目录
    cp AMSS.tar.gz $Qualcomm8155BashPath/${projectname}_Package/Update

    # 同步镜像文件到Image目录
    rsync -rv --exclude='AMSS.tar.gz' * $Qualcomm8155BashPath/${projectname}_Package/Image/AMSS

    # 特定项目的OTA处理
    if [[ $projectname =~ "HS7012A" ]] || [[ $projectname =~ "HS7023A" ]] || [[ $projectname =~ "HS7029A" ]]; then
        cp -f NON-HLOS.bin $Qualcomm8155BashPath/${projectname}_Package/OTA/AMSS
        cp -f $LOCALQUA_BSP_PATH/apps/qnx_ap/target/hypervisor/host/out_8155/share.img $Qualcomm8155BashPath/${projectname}_Package/Image/Qnx
    fi
}
```

## 🚨 6. 错误处理和诊断增强

### 6.1 构建错误分类系统
```bash
# 错误参数定义 - 更细粒度分类
ERROR_PARAM_BUILD_MPU=51    # MPU模块构建错误
ERROR_PARAM_BUILD_BSP=52    # BSP模块构建错误
ERROR_PARAM_BUILD_OTHER=53  # 其他模块构建错误
ERROR_PARAM_BUILD_AMSS=54   # AMSS构建错误
ERROR_PARAM_PACKAGE=55      # 打包错误
ERROR_PARAM_META=56         # Meta构建错误

# 错误模块路径定义
ERROR_BUILD_MODULE_MPU_PATH="apps/qnx_ap/src/mpu"
ERROR_BUILD_MODULE_BSP_PATH="apps/qnx_ap/src/services"
```

### 6.2 智能错误分析
```bash
function analyze_build_error() {
    buildErrorModule=`cat QNX_Build_Error_${build_paramNum}.log | grep -E 'failed|error'`

    if [[ $buildErrorModule =~ .*${ERROR_BUILD_MODULE_MPU_PATH}.* ]]; then
        echo "QNX MPU Module Build Error ....."
        RUNING_ERROR_PARAM=$ERROR_PARAM_BUILD_MPU
        # 提取具体错误模块
        echo $buildErrorModule | awk -F ${ERROR_BUILD_MODULE_MPU_PATH} '{print $2}' | awk -F "/" '{print $3}' > $Qualcomm8155BashPath/${projectname}_Package/QNX_ERROR_MODULE
    elif [[ $buildErrorModule =~ .*${ERROR_BUILD_MODULE_BSP_PATH}.* ]]; then
        echo "QNX BSP Module Build Error ....."
        RUNING_ERROR_PARAM=$ERROR_PARAM_BUILD_BSP
    elif [[ $buildErrorModule =~ .*"checksparse.py".* ]]; then
        echo "AMSS Meta Build Error - Missing super.img ....."
        RUNING_ERROR_PARAM=$ERROR_PARAM_META
    else
        echo "QNX Other Module Build Error ....."
        RUNING_ERROR_PARAM=$ERROR_PARAM_BUILD_OTHER
    fi

    _build_error_exit $RUNING_ERROR_PARAM
}
```

## 📁 7. 关键路径配置对比

### 7.1 新版本路径配置
```bash
# 工作根目录 - 迁移到新磁盘
Qualcomm8155BashPath=/mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155

# 本地构建路径
LOCAL_PATH=$Qualcomm8155BashPath/Qualcomm8155Pool${build_paramNum}/${QualcommPathName}

# QNX相关路径
LOCALQNX_SDP_PATH=$LOCAL_PATH/qnx_sdp
LOCALQUA_BSP_PATH=$LOCAL_PATH/qnx_bsp
LOCALQUA_AMSS_PATH=$LOCAL_PATH/amss

# 安全启动专用路径
SECURE_BOOT_PATH=/mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155/SecureBoot
```

### 7.2 源码基线路径
```bash
# QNX基线代码路径 - 保持不变
QualcommPathName="hqx1.2.1.c1_r00004.2"
QualcommLastPathName="HQX1.2.1_R00005.2"

# 基线代码存储路径 - 预移植版本路径简化
BASE_CODE_PATH="/var/lib/jenkins/source/$QualcommLastPathName/$QualcommPathName.tar.gz"

# SDP路径 - 迁移到Jenkins标准路径
SDP_PATH="/var/lib/jenkins/source/HQX1.2.1_R00005.2/sdp700_hqx1_2.tar.gz"

# 许可证路径 - 简化路径结构
LICENSE_PATH="/var/lib/jenkins/source/sdp_license/qnx700_license/license.tar"
```

## 🔄 8. 异步构建监控机制

### 8.1 构建状态监控
```bash
function async_build_inspect() {
    # 检查构建状态文件
    if [[ -f $Qualcomm8155BashPath/${projectname}_Package/BUILD_STOP ]]; then
        echo "Build stop signal detected, terminating build process"
        _build_error_exit $ERROR_PARAM_BUILD_STOP
    fi
    
    # 检查磁盘空间
    available_space=$(df $Qualcomm8155BashPath | awk 'NR==2 {print $4}')
    if [[ $available_space -lt 10485760 ]]; then  # 10GB in KB
        echo "Insufficient disk space, terminating build"
        _build_error_exit $ERROR_PARAM_DISK_SPACE
    fi
}
```

## 📊 9. 构建流程时序分析

### 9.1 完整构建时序
```
1. 环境准备 (0-5分钟)
   ├── 清理旧构建输出
   ├── 创建目录结构
   ├── 解压QNX基线代码
   ├── 条件化SDP解压
   ├── 双重许可证部署
   └── 权限设置

2. 源码获取 (5-15分钟)
   ├── XML配置读取
   ├── 源码路径解析
   ├── SVN导出 (统一认证)
   ├── MPU模块列表生成
   └── 源码后处理

3. 构建执行 (15-120分钟)
   ├── 普通构建模式
   │   ├── QNX环境设置
   │   ├── make clean
   │   └── make all
   └── 安全启动模式
       ├── AMSS安全构建
       │   ├── ADSP构建
       │   ├── CDSP构建
       │   ├── Boot构建
       │   ├── TZ构建
       │   └── AOP构建
       └── QNX安全构建

4. Meta构建 (120-140分钟)
   ├── super.img依赖检查
   ├── AMSS Meta构建
   ├── CDT生成
   └── 构建验证

5. 成果物处理 (140-160分钟)
   ├── QNX打包
   ├── AMSS打包
   ├── 符号文件输出
   ├── OTA处理 (特定项目)
   └── 最终验证

6. Coverity扫描 (可选)
   ├── 扫描配置
   ├── 构建扫描
   ├── 结果分析
   └── 结果提交
```

## 🆚 10. 与原base版本的关键差异

| 特性 | 原base版本 | 新版本 | 改进说明 |
|------|------------|--------|----------|
| **构建池数量** | 3个 | 4个 | 提升33%并发能力 |
| **路径配置** | /mnt/home/<USER>/mnt/new_disk | 迁移到新磁盘 |
| **SVN认证** | 基础认证 | 硬编码凭据 | 提高自动化程度 |
| **许可证管理** | 单一部署 | 双重部署 | 增强可靠性 |
| **错误处理** | 基础分类 | 智能分析 | 更精确的错误定位 |
| **Coverity集成** | 无 | 完整集成 | 代码质量保障 |
| **安全启动** | 基础支持 | 完整流程 | 企业级安全 |
| **Meta构建** | 简单处理 | 依赖检查 | 更可靠的构建 |
| **异步监控** | 无 | 完整监控 | 实时状态跟踪 |
| **OTA支持** | 基础支持 | 项目化配置 | 灵活的OTA策略 |

## 🎯 11. 系统优势总结

### 11.1 性能提升
- **并发能力**: 4个构建池，支持更高的并发处理
- **构建速度**: 优化的源码导出和构建流程
- **资源利用**: 智能的磁盘空间和资源监控

### 11.2 可靠性增强
- **双重许可证**: 确保QNX许可证的可用性
- **错误恢复**: 完善的错误分类和处理机制
- **构建监控**: 实时的构建状态监控

### 11.3 安全性提升
- **安全启动**: 完整的安全启动构建流程
- **代码扫描**: 集成Coverity代码质量扫描
- **认证机制**: 统一的SVN认证管理

### 11.4 维护性改进
- **模块化设计**: 清晰的功能模块划分
- **配置驱动**: 完善的XML配置机制
- **日志记录**: 详细的构建日志和错误记录

## 🔮 12. 技术特色

### 12.1 智能化构建
- 条件化的SDP解压，避免不必要的操作
- 智能的依赖检查，如super.img存在性验证
- 自适应的错误处理和恢复机制

### 12.2 企业级特性
- Coverity代码扫描集成
- 完整的安全启动支持
- 项目化的OTA配置

### 12.3 运维友好
- 详细的构建日志
- 实时的状态监控
- 完善的错误诊断

这个新脚本系统代表了自动化构建技术的重大进步，在保持原有功能的基础上，大幅提升了系统的可靠性、安全性和维护性。

