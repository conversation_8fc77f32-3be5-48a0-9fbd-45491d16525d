#! /bin/bash 
#****************************
# ѹ��������ű�
# Authot xizhuang.wu
# version 1.0
#****************************

Qualcomm8155BashPath=/mnt/new_disk/jenkins/jenkins_qnx-android_Qualcomm8155
RUNING_ERROR_PARAM=0

#����������   
#            901 �������ռ䲻�� 
ERROR_PARAM_SPACE=901

while getopts "b:p:i:a:" opt
do
    case $opt in
	        b ) echo "b "
            	buildPoolNum=$OPTARG;;
            p ) echo "p "
            	projectname=$OPTARG;;
		    i ) echo "i "
            	InstallParam=$OPTARG;;
			a ) echo "a "
            	AmssParam=$OPTARG;;
		    ? ) echo "$0 error argument."
			exit 1;;
    esac
done

InstallOutPath=$Qualcomm8155BashPath/${projectname}_Package/output/Qnx
InstallTarPath=$Qualcomm8155BashPath/Qualcomm8155Pool${buildPoolNum}/hqx1.2.1.c1_r00004.2/qnx_bsp/apps/qnx_ap

AmssTarPath=$Qualcomm8155BashPath/Qualcomm8155Pool${buildPoolNum}/hqx1.2.1.c1_r00004.2
AmssOutPath=$Qualcomm8155BashPath/${projectname}_Package

_build_error_exit ()
{
    EXIT_ERROR_PARAM=$1
	echo "Android build Error,Error param is $EXIT_ERROR_PARAM,exit ........"
	exit $EXIT_ERROR_PARAM
}

function _tar_amss_package ()
{
    RUNING_ERROR_PARAM=$ERROR_PARAM_SPACE
	cd $AmssTarPath
    tar -czvf amss.tar.gz amss
	if [[ $? -ne 0 ]]; then
	    echo "make amss.tar.gz ERROR ,please ensure sufficient space !!!!!!!!"
	    _build_error_exit $RUNING_ERROR_PARAM
    else
	    mv amss.tar.gz $Qualcomm8155BashPath/${projectname}_Package
	fi
}

function _tar_install_package ()
{
    RUNING_ERROR_PARAM=$ERROR_PARAM_SPACE
	
    cd $InstallTarPath
	tar -czvf install.tar.gz install
    if [[ $? -ne 0 ]];then
        echo "Outcome Install is ERROR !!!!!! return !!!"
	    _build_error_exit $RUNING_ERROR_PARAM
	else
	    if [[ ! -d $Qualcomm8155BashPath/${projectname}_Package/output/Qnx ]];then mkdir -p $Qualcomm8155BashPath/${projectname}_Package/output/Qnx;fi
	    mv ./install.tar.gz $Qualcomm8155BashPath/${projectname}_Package/output/Qnx
	fi
} 

_tar_process_start ()
{
    echo "Begin Make Tar Package !!!!!! "
	if [[ $InstallParam -eq 1 ]];then _tar_install_package;fi
	if [[ $AmssParam -eq 1 ]];then _tar_amss_package;fi
	echo "End Make Tar Package !!!!!! "
}

_tar_process_start

