<?xml version="1.0" ?>
<patches>
  <!--NOTE: This is an ** Autogenerated file **-->
  <!--NOTE: Patching is in little endian format, i.e. 0xAABBCCDD will look like DD CC BB AA in the file or on disk-->
  <!--NOTE: This file is used by Trace32 - So make sure to add decimals, i.e. 0x10-10=0, *but* 0x10-10.=6.-->
  <patch SECTOR_SIZE_IN_BYTES="4096" byte_offset="424" filename="gpt_main1.bin" physical_partition_number="1" size_in_bytes="8" start_sector="2" value="NUM_DISK_SECTORS-6." what="Update last partition 4 'last_parti' with actual size in Primary Header."/>
  <patch SECTOR_SIZE_IN_BYTES="4096" byte_offset="424" filename="DISK" physical_partition_number="1" size_in_bytes="8" start_sector="2" value="NUM_DISK_SECTORS-6." what="Update last partition 4 'last_parti' with actual size in Primary Header."/>
  <patch SECTOR_SIZE_IN_BYTES="4096" byte_offset="424" filename="gpt_backup1.bin" physical_partition_number="1" size_in_bytes="8" start_sector="0" value="NUM_DISK_SECTORS-6." what="Update last partition 4 'last_parti' with actual size in Backup Header."/>
  <patch SECTOR_SIZE_IN_BYTES="4096" byte_offset="424" filename="DISK" physical_partition_number="1" size_in_bytes="8" start_sector="NUM_DISK_SECTORS-5." value="NUM_DISK_SECTORS-6." what="Update last partition 4 'last_parti' with actual size in Backup Header."/>
  <patch SECTOR_SIZE_IN_BYTES="4096" byte_offset="48" filename="gpt_main1.bin" physical_partition_number="1" size_in_bytes="8" start_sector="1" value="NUM_DISK_SECTORS-6." what="Update Primary Header with LastUseableLBA."/>
  <patch SECTOR_SIZE_IN_BYTES="4096" byte_offset="48" filename="DISK" physical_partition_number="1" size_in_bytes="8" start_sector="1" value="NUM_DISK_SECTORS-6." what="Update Primary Header with LastUseableLBA."/>
  <patch SECTOR_SIZE_IN_BYTES="4096" byte_offset="48" filename="gpt_backup1.bin" physical_partition_number="1" size_in_bytes="8" start_sector="4" value="NUM_DISK_SECTORS-6." what="Update Backup Header with LastUseableLBA."/>
  <patch SECTOR_SIZE_IN_BYTES="4096" byte_offset="48" filename="DISK" physical_partition_number="1" size_in_bytes="8" start_sector="NUM_DISK_SECTORS-1." value="NUM_DISK_SECTORS-6." what="Update Backup Header with LastUseableLBA."/>
  <patch SECTOR_SIZE_IN_BYTES="4096" byte_offset="32" filename="gpt_main1.bin" physical_partition_number="1" size_in_bytes="8" start_sector="1" value="NUM_DISK_SECTORS-1." what="Update Primary Header with BackupGPT Header Location."/>
  <patch SECTOR_SIZE_IN_BYTES="4096" byte_offset="32" filename="DISK" physical_partition_number="1" size_in_bytes="8" start_sector="1" value="NUM_DISK_SECTORS-1." what="Update Primary Header with BackupGPT Header Location."/>
  <patch SECTOR_SIZE_IN_BYTES="4096" byte_offset="24" filename="gpt_backup1.bin" physical_partition_number="1" size_in_bytes="8" start_sector="4" value="NUM_DISK_SECTORS-1." what="Update Backup Header with CurrentLBA."/>
  <patch SECTOR_SIZE_IN_BYTES="4096" byte_offset="24" filename="DISK" physical_partition_number="1" size_in_bytes="8" start_sector="NUM_DISK_SECTORS-1." value="NUM_DISK_SECTORS-1." what="Update Backup Header with CurrentLBA."/>
  <patch SECTOR_SIZE_IN_BYTES="4096" byte_offset="72" filename="gpt_backup1.bin" physical_partition_number="1" size_in_bytes="8" start_sector="4" value="NUM_DISK_SECTORS-5." what="Update Backup Header with Partition Array Location."/>
  <patch SECTOR_SIZE_IN_BYTES="4096" byte_offset="72" filename="DISK" physical_partition_number="1" size_in_bytes="8" start_sector="NUM_DISK_SECTORS-1" value="NUM_DISK_SECTORS-5." what="Update Backup Header with Partition Array Location."/>
  <patch SECTOR_SIZE_IN_BYTES="4096" byte_offset="88" filename="gpt_main1.bin" physical_partition_number="1" size_in_bytes="4" start_sector="1" value="CRC32(2,4096)" what="Update Primary Header with CRC of Partition Array."/>
  <patch SECTOR_SIZE_IN_BYTES="4096" byte_offset="88" filename="DISK" physical_partition_number="1" size_in_bytes="4" start_sector="1" value="CRC32(2,4096)" what="Update Primary Header with CRC of Partition Array."/>
  <patch SECTOR_SIZE_IN_BYTES="4096" byte_offset="88" filename="gpt_backup1.bin" physical_partition_number="1" size_in_bytes="4" start_sector="4" value="CRC32(0,4096)" what="Update Backup Header with CRC of Partition Array."/>
  <patch SECTOR_SIZE_IN_BYTES="4096" byte_offset="88" filename="DISK" physical_partition_number="1" size_in_bytes="4" start_sector="NUM_DISK_SECTORS-1." value="CRC32(NUM_DISK_SECTORS-5.,4096)" what="Update Backup Header with CRC of Partition Array."/>
  <patch SECTOR_SIZE_IN_BYTES="4096" byte_offset="16" filename="gpt_main1.bin" physical_partition_number="1" size_in_bytes="4" start_sector="1" value="0" what="Zero Out Header CRC in Primary Header."/>
  <patch SECTOR_SIZE_IN_BYTES="4096" byte_offset="16" filename="gpt_main1.bin" physical_partition_number="1" size_in_bytes="4" start_sector="1" value="CRC32(1,92)" what="Update Primary Header with CRC of Primary Header."/>
  <patch SECTOR_SIZE_IN_BYTES="4096" byte_offset="16" filename="DISK" physical_partition_number="1" size_in_bytes="4" start_sector="1" value="0" what="Zero Out Header CRC in Primary Header."/>
  <patch SECTOR_SIZE_IN_BYTES="4096" byte_offset="16" filename="DISK" physical_partition_number="1" size_in_bytes="4" start_sector="1" value="CRC32(1,92)" what="Update Primary Header with CRC of Primary Header."/>
  <patch SECTOR_SIZE_IN_BYTES="4096" byte_offset="16" filename="gpt_backup1.bin" physical_partition_number="1" size_in_bytes="4" start_sector="4" value="0" what="Zero Out Header CRC in Backup Header."/>
  <patch SECTOR_SIZE_IN_BYTES="4096" byte_offset="16" filename="gpt_backup1.bin" physical_partition_number="1" size_in_bytes="4" start_sector="4" value="CRC32(4,92)" what="Update Backup Header with CRC of Backup Header."/>
  <patch SECTOR_SIZE_IN_BYTES="4096" byte_offset="16" filename="DISK" physical_partition_number="1" size_in_bytes="4" start_sector="NUM_DISK_SECTORS-1." value="0" what="Zero Out Header CRC in Backup Header."/>
  <patch SECTOR_SIZE_IN_BYTES="4096" byte_offset="16" filename="DISK" physical_partition_number="1" size_in_bytes="4" start_sector="NUM_DISK_SECTORS-1." value="CRC32(NUM_DISK_SECTORS-1.,92)" what="Update Backup Header with CRC of Backup Header."/>
</patches>
